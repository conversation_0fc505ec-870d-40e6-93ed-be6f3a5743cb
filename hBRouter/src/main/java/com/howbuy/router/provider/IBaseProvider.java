package com.howbuy.router.provider;

import android.widget.ImageView;

import com.alibaba.android.arouter.facade.template.IProvider;

/**
 * Create by zsm on 2019/4/8.
 **/
public interface IBaseProvider extends IProvider {

    void displayImage(String url, ImageView imageView);

    void analyticsClick(String clickId, String ext[]);

    void stopNetDiagnose();

    void putString(String key, String value);
    void putInt(String key, int value);
    void putBoolean(String key, boolean value);

    String getString(String key, String def);
    boolean getBoolean(String key, boolean def);
    int getInt(String key, int def);
    void remove(String key);

    String getChannel();
}
