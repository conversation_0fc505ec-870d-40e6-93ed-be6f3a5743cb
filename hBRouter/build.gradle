plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'

    id 'com.alibaba.arouter' //arouter
}

apply from: "${rootProject.projectDir}/arouter-kotlin-config.gradle"

android {
    compileSdkVersion rootProject.ext.commonVersions.compileSdkVersion
    defaultConfig {
        minSdkVersion rootProject.ext.commonVersions.minSdkVersion
        targetSdkVersion rootProject.ext.commonVersions.targetSdkVersion
    }
}

dependencies {

    api rootProject.ext.dependencies["arouter-api"]
}
