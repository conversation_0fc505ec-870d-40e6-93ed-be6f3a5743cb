<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="15dp"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:orientation="vertical"
        tools:ignore="MissingDefaultResource">

        <TextView
            android:id="@+id/tv_sub_amt_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:textColor="#ffb8b8b8"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:text="数据截止日期：2024-04-09" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginHorizontal="15dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <View
                android:id="@+id/v_space"
                android:layout_width="0dp"
                android:layout_height="10dp"
                android:visibility="gone"
                tools:visibility="visible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_sub_amt_value"
                app:layout_constraintTop_toBottomOf="@+id/v_space"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:lines="1"
                android:textColor="#ff333333"
                android:textSize="27sp"
                app:autoSizeMaxTextSize="27sp"
                app:autoSizeMinTextSize="13sp"
                app:autoSizeTextType="uniform"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tv_sub_amt_value_unit"
                tools:text="****" />

            <TextView
                android:id="@+id/tv_sub_amt_value_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="3dp"
                android:text="美元"
                android:textColor="#ff666666"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tv_sub_amt_value"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <com.howbuy.component.widgets.TriangleView
            android:layout_width="22dp"
            android:layout_height="10dp"
            android:layout_gravity="center_horizontal"
            app:triangleColor="#FFD9D9" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginHorizontal="10dp"
                android:background="@drawable/bg_gradient_ffd9d9_to_00ffffff_v" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:clipChildren="false"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp"
                    android:orientation="horizontal"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="3dp"
                        android:src="@mipmap/ic_amt_desc_tishi" />

                    <TextView
                        android:id="@+id/tv_sub_amt_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:lineSpacingExtra="3dp"
                        android:textColor="#ff2a3050"
                        android:textSize="14sp"
                        tools:text="现金余额：指您存入或留在好买香港账户而暂未用作它用的资金(即未买入基金且未投入海外储蓄罐的留账现金余额)。" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lay_cash_huilv_table"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="10dp"
                    android:orientation="vertical"
                    android:visibility="gone"

                    tools:visibility="visible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="余额明细："
                        android:textColor="#ff2a3050"
                        android:textSize="14sp"
                        android:textStyle="bold" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:background="#4dD8D8D8"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal">


                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="5dp"
                                android:layout_weight="1.3"
                                android:gravity="center"
                                android:text="币种"
                                android:textColor="#ff2a3050"
                                android:textSize="14sp" />

                            <View
                                android:layout_width="1dp"
                                android:layout_height="match_parent"
                                android:background="#ffffff" />

                            <TextView
                                android:id="@+id/tv_table_cur_bizhong"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="5dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:textColor="#ff2a3050"
                                android:textSize="14sp"
                                tools:text="金额\n(换算为美元)" />

                            <View
                                android:layout_width="1dp"
                                android:layout_height="match_parent"
                                android:background="#ffffff" />

                            <TextView
                                android:id="@+id/tv_table_origin_bizhong"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="5dp"
                                android:layout_marginBottom="5dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="原币种金额\n(原币种)"
                                android:textColor="#ff2a3050"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rcv_amt_cash_huilv_table"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="#ffffff"
                            android:nestedScrollingEnabled="false"
                            android:orientation="vertical"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                            tools:itemCount="2"
                            tools:listitem="@layout/item_sub_amt_cash_huilv_table_layout" />


                        <LinearLayout
                            android:id="@+id/lay_table_huilv_desc"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingLeft="12dp"
                            android:paddingTop="10dp"
                            android:paddingRight="12dp"
                            android:paddingBottom="10dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="*汇率换算"
                                android:textColor="#ff2a3050"
                                android:textSize="12sp" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rcv_amt_cash_huilv_table_desc"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:nestedScrollingEnabled="false"
                                android:orientation="vertical"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                tools:itemCount="2"
                                tools:listitem="@layout/item_sub_amt_cash_huilv_table_desc_layout" />


                        </LinearLayout>


                    </LinearLayout>


                </LinearLayout>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rcv_sub_amt_content_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="4dp"
                    android:layout_marginRight="4dp"
                    android:nestedScrollingEnabled="false"
                    android:orientation="vertical"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_sub_amt_content_layout" />
            </LinearLayout>
        </FrameLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="12dp"
            android:layout_marginTop="15dp">

            <TextView
                android:id="@+id/tv_sub_amt_sm_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="4dp"
                android:text="声明：资产/余额数据仅供参考，具体持仓情况请以好买香港后台发送给您的开户邮箱的交易结单为准。如未收到相关结单，请联系您的专属服务人员或咨询客服 +852-37258088查询。"
                android:textColor="#888888"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_sub_amt_sm_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingExtra="4dp"
                android:text="声明："
                android:textColor="#ff333333"
                android:textSize="12sp" />


        </FrameLayout>


    </LinearLayout>
</androidx.core.widget.NestedScrollView>