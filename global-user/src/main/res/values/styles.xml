<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">

    <style name="mine_line">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">44dp</item>
        <item name="android:background">@drawable/bg_mine_line</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:paddingStart">15dp</item>
        <item name="android:paddingEnd">15dp</item>
    </style>

    <style name="mine_line_txt_left">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#333333</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="mine_line_txt_right">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:drawableEnd">@mipmap/ic_right_arrow_gray</item>
        <item name="android:drawablePadding">5dp</item>
        <item name="maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginStart">20dp</item>
        <item name="android:gravity">end</item>
        <item name="android:textColor">#878BA5</item>
        <item name="android:textSize">16sp</item>
    </style>

    <!-- Entry View属性-->
    <declare-styleable name="EntryView">
        <attr name="leftText" format="string"/>
        <attr name="rightText" format="string"/>
        <attr name="rightTextVisible" format="boolean"/>
        <attr name="rightArrowVisible" format="boolean"/>
        <attr name="leftOffset" format="dimension"/>
        <attr name="rightOffset" format="dimension"/>
        <attr name="bottomDividerVisible" format="boolean"/>
    </declare-styleable>

    <style name="MineAssetTitleStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">#333</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="MineAssetTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#ff333333</item>
    </style>
</resources>