package com.howbuy.global.user.mine

import android.graphics.drawable.Drawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.howbuy.account.UserDataHelper
import com.howbuy.account.api.DataWrapper
import com.howbuy.account.api.UserDataObserver
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.utils.ResourceUtils
import com.howbuy.fund.base.utils.ShapeCreator
import com.howbuy.fund.util.VisibleGone
import com.howbuy.global.data_api.ApiUserInfo
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.isLogined
import com.howbuy.global.user.FragMine
import com.howbuy.global.user.MineRouterPath
import com.howbuy.global.user.R
import com.howbuy.global.user.announcement.Announcement
import com.howbuy.global.user.entity.MessageData
import com.howbuy.global.user.utils.MultiTouchDelegate
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils

/**
 * 我的页面-顶部工具栏：[R.layout.frag_mine]
 */
class ToolbarRender(private val moduleView: View, private val frag:FragMine) : ModuleRender {
    private val personalCenterEntry: TextView by lazy {
        moduleView.findViewById<TextView?>(R.id.tv_personal_center).also { tv ->
            tv.background = createPersonalBg()
        }
    }
    private val parentView:View by lazy { moduleView.findViewById(R.id.toolbarContainer)} // 父容器
    private val tvNickname: TextView by lazy { moduleView.findViewById(R.id.tv_nickname) }
    private val ivAvatar: ImageView by lazy { moduleView.findViewById(R.id.iv_avatar) }
    private val ivSetting: ImageView by lazy { moduleView.findViewById(R.id.iv_mine_settings) }
    private val ivMessage: ImageView by lazy { moduleView.findViewById(R.id.iv_mine_msg) }
    private val viewRedDot: View by lazy { moduleView.findViewById(R.id.iv_mine_msg_red) }
    private val ucEntry: View by lazy { moduleView.findViewById(R.id.ucEntry) }

    // 待办任务提醒管理器
    private val todoReminderManager:TodoReminderManager by lazy { TodoReminderManager(frag) }
    private val multiTouchDelegate: MultiTouchDelegate by lazy {
        MultiTouchDelegate(parentView)
            .also {
                it.addDelegateView(ivMessage, 10f)
                it.addDelegateView(ivSetting, 10f)
            }
    }

    /**
     * 未读消息和待办消息 数量
     */
    private var todoNum: Int = 0
    private var unReadNum: Int = 0

    override fun init() {
        //扩大点击事件的响应范围
        parentView.touchDelegate = multiTouchDelegate

        // 设置点击事件
        setClickListeners()

        //监听消息数据更新
        UserDataHelper.getDataManager(DataIds.ID_USER_MESSAGE).addObserver(frag, true, object :
            UserDataObserver<MessageData> {
            override fun onChanged(t: DataWrapper<MessageData>) {
                val activity = frag.activity
                if (activity == null || activity.isFinishing) {
                    return
                }
                if (isLogined() && t.data != null) {
                    //若{未读消息数量} = 0，则隐藏该窗口；若{未读消息数量}>0，则显示该窗口；
                    unReadNum = MathUtils.forValI(t.data?.unReadCount, 0)
                    todoNum = MathUtils.forValI(t.data?.todoCount, 0)
                    //设置toolbar上的消息红点状态
                    setToolbarMsgRedIconStatus()
                    if (unReadNum > 0) {
                        frag.binding.layoutLogin.layMessageModule.root.visibility = View.VISIBLE
                        //最近一条未读消息
                        frag.setLastedMsgModuleUIData(unReadNum, t.data?.messageInfo)
                    } else {
                        frag.binding.layoutLogin.layMessageModule.root.visibility = View.GONE
                    }
                }
            }
        })

        //TODO 删除
        mockAnnouncement()

        //TODO 页面再次可见，需要刷新消息红点、以及待办任务提醒
    }

    private fun mockAnnouncement() {
        GlobalApp.getApp().handler.postDelayed({
        frag.renderAnnouncement(Announcement(
            "134123",
            "1",
            "1",
            "",
            "尊敬的投资人。系统将于2023/9/1824:00进行升级   尊敬的投资人。系统将于2023/9/1824:00进行升级，期间签约功能可能无法使用，很抱歉给您带来了不便。期间签约功能可能无法使用，很抱歉",
            null
        ))

            updateMessageRedDotStatus()
        }, 1500L)
    }

    private fun handleLogin() {
        // 登录状态，显示头像、昵称、个人中心
        handleWidgetsVisible(true)

        // 设置用户头像
        setUserAvatar()

        // 设置用户昵称
        setUserNickname()

        // 设置消息红点状态
        updateMessageRedDotStatus()
    }

    override fun handleLoginChanged(login: Boolean) {
        if (login) {
            handleLogin()
        } else {
            // 未登录，不显示头像、昵称、个人中心
            handleWidgetsVisible(false)
            tvNickname.text = "未登录"
            //不显示待办提醒
            todoReminderManager.removeReminderView()
        }
    }

    private fun handleWidgetsVisible(visible: Boolean) {
        VisibleGone.handle(ivAvatar, visible)
        VisibleGone.handle(tvNickname, visible)
        VisibleGone.handle(personalCenterEntry, visible)
        VisibleGone.handle(frag.binding.layMsg, visible)
        VisibleGone.handle(frag.binding.laySetting, true) //todo 涉及2.9代码，需要在此基础上作出调整
        VisibleGone.handle(frag.binding.ivMineSettings, true) //todo 涉及2.9代码，需要在此基础上作出调整

    }

    private fun createPersonalBg(): Drawable {
        return ShapeCreator()
            .stokeWidth(DensityUtils.dip2px(0.5f).toFloat())
            .stokeColor(ResourceUtils.getColor(R.color.white))
            .radius(DensityUtils.dip2px(15f).toFloat())
            .create()
    }

    /**
     * 设置用户头像
     */
    private fun setUserAvatar() {
        val gender = apiUserInfo().genderIsMale()
        val avatarResId = gender.getAvatarResource()
        ivAvatar.setImageResource(avatarResId)
    }



    /**
     * 设置用户昵称
     */
    private fun setUserNickname() {
        tvNickname.text = resolveNickname()
    }



    /**
     * 掩码处理真实姓名
     */
    private fun maskRealName(name: String): String {
        return if (name.length > 1) {
            "*".repeat(name.length - 1) + name.last()
        } else {
            name
        }
    }

    /**
     * 更新消息红点状态
     */
    private fun updateMessageRedDotStatus() {
//        val messageData = UserDataHelper.getMessageData()
        val unreadMessages = true //todo messageData?.unReadCount ?: 0 > 0
        val todoTasks = emptyList<Any>()//messageData?.todoList ?: emptyList()
        val currentTime = System.currentTimeMillis()

        // 判断是否显示红点
        val showRedDot = shouldShowRedDot(unreadMessages, todoTasks, currentTime)
        VisibleGone.handle(viewRedDot, showRedDot)

        // 处理“待办提醒”气泡显示逻辑
        todoReminderManager.updateTodoReminder(frag.binding.layMsg, todoTasks)
    }

    /**
     * 判断是否显示红点
     */
    private fun shouldShowRedDot(unreadMessages: Boolean, todoTasks: List<Any>, currentTime: Long): Boolean {
        if (!isLogined()) return false
        if (unreadMessages) {
            return true
        }

        // 检查是否有未处理且已过期的待办任务
        return todoTasks.any { task ->
            val isProcessed:Boolean = task::class.java.getMethod("isProcessed").invoke(task) as Boolean
            val deadlineTime = task::class.java.getMethod("getDeadlineTime").invoke(task) as Long
            (!isProcessed) && deadlineTime < currentTime
        }
    }

    /**
     * 判断是否显示待办提醒
     */
    private fun shouldShowTodoReminder(todoTasks: List<Any>, currentTime: Long): Boolean {
        val unprocessedTasks = todoTasks.filter { task ->
            val isProcessed = task::class.java.getMethod("isProcessed").invoke(task) as Boolean
            val deadlineTime = task::class.java.getMethod("getDeadlineTime").invoke(task) as Long

            (!isProcessed) && deadlineTime < currentTime
        }
        return unprocessedTasks.isNotEmpty()
    }

    /**
     * 获取待办提醒文本
     */
    private fun getTodoReminderText(todoTasks: List<Any>, currentTime: Long): String {
        val count = todoTasks.count { task ->
            val isProcessed = task::class.java.getMethod("isProcessed").invoke(task) as Boolean
            val deadlineTime = task::class.java.getMethod("getDeadlineTime").invoke(task) as Long
            (!isProcessed) && deadlineTime < currentTime
        }
        return "${count}条待办需处理"
    }

    /**
     * 设置点击事件
     */
    private fun setClickListeners() {
        val centerClickAction: View.OnClickListener = View.OnClickListener {
            if (isLogined()) {
                navigateToPersonalCenter()
            } else {
                frag.login()
            }
        }
        // 头像,昵称,个人中心 3个位置点击后进入个人中心
        ucEntry.setOnClickListener(centerClickAction)

        // 设置按钮点击事件
        ivSetting.setOnClickListener {
            LogUtils.d("Mine-Toolbar", "setting click")
            //跳转到设置页
//            RouterHelper.launchFragWithCallback(
//                frag,
//                MineRouterPath.PATH_FRAG_MINE_SETTINGS,
//                NavHelper.obtainArg("设置", ValConfig.IT_VALUE_1, frag.mCurAmtType?.code)
//            ) { resultCode, intent ->
//                if (resultCode == Activity.RESULT_OK) {
//                    intent?.extras?.let { it ->
            //TODO 以下为之前的币种切换
//                        val amtTypeCode = it.getString(ValConfig.IT_VALUE_1)
//                        frag.mCurAmtType = AmtType.values().find { p ->
//                            p.code == amtTypeCode
//                        }
//                    }
//                    //页面在onResume会刷新接口,所以这里回调后, 不需要重复调用接口刷新数据
//                    // refreshData()
//                }
//                true
//            }
            //跳转到设置页
            RouterHelper.launchFrag(
                frag,
                MineRouterPath.PATH_FRAG_SETTINGS,
                NavHelper.obtainArg("设置")
            )
            HbAnalytics.onClick("611140")
            HbAnalytics.onClick("611140")
        }

        // 消息中心按钮点击事件
        ivMessage.setOnClickListener {
            //有待办,定位待办tab,否则, 默认定位消息tab
            frag.launcherToMsgPage(if (todoNum > 0) 1 else 0)
            HbAnalytics.onClick("611150")
        }
    }

    /**
     * 跳转到个人中心
     */
    private fun navigateToPersonalCenter() {
        if (isLogined()) {
            RouterHelper.launchFrag(frag, MineRouterPath.PATH_FRAG_USER_CENTER, NavHelper.obtainArg("个人中心"))
        }
    }

    /**
     * 跳转到消息中心
     * @param tabIndex 消息中心的tab索引，0表示消息列表，1表示待办列表
     */
    private fun navigateToMessageCenter(tabIndex: Int = 0) {
        // 实现跳转到消息中心的逻辑
    }


    /**
     * 设置toolbar上的消息红点状态
     * @param unReadNum 未读消息数量
     * @param todoNum 待办消息数量
     */
    private fun setToolbarMsgRedIconStatus() {
        VisibleGone.handle(viewRedDot, unReadNum > 0 || todoNum > 0)
    }
}


/**
 * 根据性别获取头像资源
 */
fun Boolean?.getAvatarResource(): Int {
    return when(this) {
        true -> R.mipmap.ic_avatar_male
        false -> R.mipmap.ic_avatar_female
        else -> R.mipmap.ic_avatar_default
    }
}

/**
 * 获取显示的昵称
 */
fun resolveNickname(): String {
    return "Hi，${apiUserInfo().getNickNameToShow("尊敬的用户")}"
}

fun ApiUserInfo.getNickNameToShow(defaultVal: String): String {
    val nickName = getNickName()
    val custName = getCustName()
    if (!nickName.isNullOrEmpty()) return nickName
    if (!custName.isNullOrEmpty()) return custName
    return defaultVal
}