package com.howbuy.global.user.feedback

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.graphics.toColorInt
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.dialog.DlgHelper
import com.howbuy.file.FileRequestMgr
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.AtyBridgeHelper
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.fund.base.utils.gone
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.base.utils.visible
import com.howbuy.fund.base.widget.pickview.ImageShowPickerBean
import com.howbuy.fund.base.widget.pickview.ImageShowPickerListener
import com.howbuy.fund.base.widget.tag.BaseTagAdapter
import com.howbuy.fund.net.entity.common.normal.NormalHeaderInfo
import com.howbuy.fund.net.http.ReqParams
import com.howbuy.fund.net.interfaces.IFileListener
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.getHkCustNo
import com.howbuy.global.data_api.isLogined
import com.howbuy.global.user.MineRouterPath
import com.howbuy.global.user.R
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.databinding.FragFeedbackLayoutBinding
import com.howbuy.global.user.dialog.DlgSmTgQrCode
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.SysUtils

/**
 * 意见反馈页面
 */
@Route(path = MineRouterPath.PATH_FRAG_FEEDBACK)
class FragFeedback : AbsFragViewBinding<FragFeedbackLayoutBinding>(), OnTouchListener {

    companion object {
        private const val MAX_IMAGES = 4
        private const val MAX_CONTENT_LENGTH = 200
    }

    private val imageList = mutableListOf<FeedbackImageBean>()
    private var selectedFeedbackType: String? = null
    private var originalSoftInputMode: Int = 0

    private var saveBtn: TextView? = null

    // 反馈类型列表
    private val feedbackTypes = listOf(
        R.string.feedback_type_feature,
        R.string.feedback_type_bug,
        R.string.feedback_type_service,
        R.string.feedback_type_other
    )

    // 反馈类型适配器
    private val feedbackTypeAdapter by lazy {
        object : BaseTagAdapter<Int>(feedbackTypes) {
            override fun getTagView(parent: ViewGroup?, position: Int): View {
                val view = LayoutInflater.from(context).inflate(R.layout.item_feedback_type, parent, false)
                val tvType = view.findViewById<TextView>(R.id.tv_feedback_type)
                tvType.text = getString(tagList[position])

                // 设置选中状态
                val isSelected = getString(tagList[position]) == selectedFeedbackType
                updateTypeItemState(tvType, isSelected)

                // 设置点击事件
                view.setOnClickListener {
                    // 更新所有标签状态
                    for (i in 0 until binding.feedbackTypeTagLayout.childCount) {
                        val childView = binding.feedbackTypeTagLayout.getChildAt(i)
                        val childTv = childView.findViewById<TextView>(R.id.tv_feedback_type)
                        updateTypeItemState(childTv, false)
                    }

                    // 更新当前选中标签状态
                    updateTypeItemState(tvType, true)
                    selectedFeedbackType = getString(tagList[position])
                    validateForm()
                }

                return view
            }
        }
    }

    // 更新反馈类型标签的状态
    private fun updateTypeItemState(textView: TextView, isSelected: Boolean) {
        textView.isSelected = isSelected
        textView.typeface = if (isSelected) Typeface.create("sans-serif-medium", Typeface.NORMAL) else Typeface.DEFAULT
    }

    // 图片选择结果处理 - 单张图片
    private val pickSingleImage = registerForActivityResult(ActivityResultContracts.PickVisualMedia()) { uri ->
        if (uri != null) {
            addImageToAdapter(uri)
            updateImageCount()
        }
    }

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragFeedbackLayoutBinding {
        return FragFeedbackLayoutBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_feedback_layout
    }

    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        super.onCreateOptionsMenu(menu, inflater)
        val menuItem = menu.add(0, 1, 0, "提交")
        if (menuItem != null) {
            menuItem.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
            menuItem.setActionView(R.layout.menu_feedback_save)
            saveBtn = menuItem.actionView as TextView?
            menuItem.actionView?.setOnClickListener {
                if (saveBtn?.isEnabled == true) {
                    submitFeedback()
                }
            }
        }
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        AtyBridgeHelper.getAtyEmptyApi(requireActivity()).setToolbarLineVisibility(false)
        // 保存原始的软键盘模式
        activity?.window?.let { window ->
            originalSoftInputMode = window.attributes.softInputMode
            // 设置软键盘模式为adjustNothing，我们将手动处理键盘显示/隐藏
            window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        }

        setupUI()
        setupListeners()
    }

    private fun setupUI() {
        // 设置反馈类型标签
        setupFeedbackTypeTagLayout()

        // 设置图片选择器
        setupImagePicker()

        //未登录用户才显示联系电话
        if (isLogined()) {
            binding.layContractTitle.gone()
            binding.layContractInput.gone()
        } else {
            binding.layContractTitle.visible()
            binding.layContractInput.visible()
        }

        // 初始化字数统计
        binding.tvCharCount.text = ""

        // 初始化图片计数
        binding.tvImageCount.text = "0/$MAX_IMAGES"

        // 初始化提交按钮状态
        validateForm()
    }

    /**
     * 设置反馈类型标签布局
     */
    private fun setupFeedbackTypeTagLayout() {
        binding.feedbackTypeTagLayout.apply {
            setTagAdapter(feedbackTypeAdapter)
            notifyDataSetChanged()
        }
    }

    override fun parseArgment(arg: Bundle?) {
        initServiceCards()
    }

    /**
     * 初始化服务卡片
     * 根据CMS配置和是否有持牌人来决定显示哪种卡片
     */
    private fun initServiceCards() {
        UserRequest.reqSmTgInfo("", "", 0) {
            if (activity == null || activity?.isFinishing == true) {
                return@reqSmTgInfo
            }
            if (it.isSuccess && it.mData != null) {
                val smTgInfo = it.mData as SmTgInfo
                if (TextUtils.equals("TG", smTgInfo.adType)) {
                    // 有持牌人，显示持牌人卡片
                    binding.layoutService.root.visibility = View.VISIBLE
                    setupServiceCard(smTgInfo)
                    binding.layoutService.tvServiceTitle.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.ic_sm_tg, 0, 0, 0)
                } else if (TextUtils.equals("KF", smTgInfo.adType)) {
                    // 无持牌人，显示客服卡片
                    binding.layoutService.root.visibility = View.VISIBLE
                    setupServiceCard(smTgInfo)
                    binding.layoutService.tvServiceTitle.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                } else {
                    // 无任何服务卡片信息，隐藏整个服务容器
                    binding.layoutService.root.visibility = View.GONE
                }
            } else {
                binding.layoutService.root.visibility = View.GONE
            }
        }
    }

    /**
     * 设置服务卡片内容
     */
    private fun setupServiceCard(cardInfo: SmTgInfo) {
        // 使用findViewById直接获取视图，避免ViewBinding可能的问题
        ImgHelper.display(cardInfo.iconUrl, binding.layoutService.ivServiceAvatar)

        binding.layoutService.tvServiceName.text = FundTextUtils.showTextEmpty(cardInfo.name)
        binding.layoutService.tvServiceTitle.text = FundTextUtils.showTextEmpty(cardInfo.title)
        binding.layoutService.tvServiceDesc.text = FundTextUtils.showTextEmpty(cardInfo.desc)
        if (cardInfo.showPhoneButton()) {
            binding.layoutService.ivCall.visibility = View.VISIBLE
            binding.layoutService.ivCall.setOnClickListener {
                SysUtils.dialHowbuy(requireContext(), cardInfo.mobile)
            }
        } else {
            binding.layoutService.ivCall.visibility = View.GONE
        }

        // 设置二维码按钮
        if (cardInfo.showQrCodeButton()) {
            binding.layoutService.ivQrcode.visibility = View.VISIBLE
            binding.layoutService.ivQrcode.setOnClickListener {
                DlgSmTgQrCode.getInstance(cardInfo.adImageUrl ?: "", cardInfo.name).show(childFragmentManager, "")
            }
        } else {
            binding.layoutService.ivQrcode.visibility = View.GONE
        }
    }

    /**
     * 设置图片选择器
     */
    private fun setupImagePicker() {
        // 设置图片加载器
        binding.imagePickerView.setImageLoaderInterface(FeedbackImagePickerLoader())

        // 设置图片选择监听
        binding.imagePickerView.setPickerListener(object : ImageShowPickerListener {
            override fun addOnClickListener(remainNum: Int) {
                // 点击添加图片
                openImagePicker()
            }

            override fun picOnClickListener(list: MutableList<ImageShowPickerBean>, position: Int, remainNum: Int) {

            }

            override fun delOnClickListener(position: Int, remainNum: Int) {
                // 删除图片
                if (position >= 0 && position < imageList.size) {
                    imageList.removeAt(position)
                }
                updateImageCount()
                validateForm()
            }
        })

        // 显示图片选择器
        binding.imagePickerView.show()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun setupListeners() {
        // 反馈内容输入监听
        binding.etFeedbackContent.setOnTouchListener(this)
        binding.etFeedbackContent.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                val length = s?.length ?: 0
                if (length > 0) {
                    binding.tvCharCount.text = SpannableUtils.formatStr(
                        SpannableItem(length.toString(), "#ca3538".toColorInt()),
                        SpannableItem("/", "#333333".toColorInt()),
                        SpannableItem(MAX_CONTENT_LENGTH.toString(), "#999999".toColorInt()),
                    )
                } else {
                    binding.tvCharCount.text = ""
                }
                validateForm()
            }
        })
    }

    /**
     * 验证表单，控制提交按钮状态
     */
    private fun validateForm() {
        val isTypeSelected = selectedFeedbackType != null
        val isContentValid = binding.etFeedbackContent.text.toString().trim().isNotEmpty()

        saveBtn?.isEnabled = isTypeSelected && isContentValid
    }

    /**
     * 打开图片选择器
     */
    private fun openImagePicker() {
        // 使用 PickVisualMedia API 选择图片，不需要请求存储权限
        pickSingleImage.launch(
            PickVisualMediaRequest.Builder()
                .setMediaType(ActivityResultContracts.PickVisualMedia.ImageOnly)
                .build()
        )
    }

    private fun addImageToAdapter(uri: Uri) {
        // 创建图片Bean并添加到列表
        val imageBean = FeedbackImageBean(uri)
        imageList.add(imageBean)

        // 更新图片选择器
        binding.imagePickerView.addData(imageBean)

        // 更新图片计数
        updateImageCount()
    }

    private fun updateImageCount() {
        val count = imageList.size
        binding.tvImageCount.text = "$count/$MAX_IMAGES"
    }

    /**
     * TODO wy 提交服务端
     */
    private fun submitFeedback() {
        val content = binding.etFeedbackContent.text.toString().trim()
        val params = java.util.HashMap<String?, String?>(1)
        params.putAll(GlobalApp.getApp().publicParams)
        params.put("feedbackContent", content)
        params.put("feedbackType", selectedFeedbackType)
        params.put("hkCustNo", getHkCustNo())
        if (isLogined()) {
            params.put("contactInfo", apiUserInfo().mobileMask())
        } else {
            val contract = binding.etFeedbackContract.getText().toString().trim { it <= ' ' }
            params.put("contactInfo", contract)
        }
        //组装文件参数
        val files = java.util.HashMap<String?, String?>(1)
        val list = binding.imagePickerView.getDataList<ImageShowPickerBean>()
        list.forEachIndexed { index, it ->
            if (!TextUtils.isEmpty(it.imageShowPickerUrl)) {
                //上传图片,需要把图片全路径中的file:// 去掉,否则new File(path)时不正确
                val path = it.imageShowPickerUrl.replace("file://", "")
                files.put("file$index", path)
            }
        }
        //TODO urlkey
        FileRequestMgr.getInstanse().uploadFile(
            "TODO urlkey", null,
            NormalHeaderInfo::class.java, params, files, 50, true, object : IFileListener<NormalHeaderInfo?> {
                override fun onFileError(msg: String?) {
                    if (activity != null && activity?.isFinishing != true) {
                        showAlermDlg(null, 0)
                        LogUtils.pop("提交失败")
                    }
                }

                override fun onFileSuccess(headerInfo: NormalHeaderInfo?, reqParams: ReqParams?) {
                    if (activity != null && activity?.isFinishing != true) {
                        showAlermDlg(null, 0)
                        if (headerInfo != null) {
                            val code = headerInfo.code
                            if (TextUtils.equals("0000", code)) {
                                LogUtils.pop("提交成功")
                                finishActivity()
                            } else {
                                LogUtils.pop("提交失败")
                            }
                        }
                    }
                }

                override fun onFileLoading(curProgress: Long, totalProgress: Long) {
                }
            })
    }

    override fun onTouch(view: View, motionEvent: MotionEvent): Boolean {
        //触摸的是EditText并且当前EditText可以滚动则将事件交给EditText处理；否则将事件交由其父类处理
        if ((view.id == R.id.et_feedback_content && canVerticalScroll(binding.etFeedbackContent))) {
            view.parent.requestDisallowInterceptTouchEvent(true)
            if (motionEvent.action == MotionEvent.ACTION_UP) {
                view.parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        return false
    }


    /**
     *  * EditText竖直方向是否可以滚动
     *  * @param editText 需要判断的EditText
     *  * @return true：可以滚动  false：不可以滚动
     *
     */
    private fun canVerticalScroll(editText: EditText): Boolean {
        //滚动的距离
        val scrollY = editText.scrollY
        //控件内容的总高度
        val scrollRange = editText.layout.height
        //控件实际显示的高度
        val scrollExtent = editText.height - editText.getCompoundPaddingTop() - editText.getCompoundPaddingBottom()
        //控件内容总高度与实际显示高度的差值
        val scrollDifference = scrollRange - scrollExtent
        if (scrollDifference == 0) {
            return false
        }
        return (scrollY > 0) || (scrollY < scrollDifference - 1)
    }

    override fun onKeyBack(fromBar: Boolean): Boolean {
        return goback()
    }

    /**
     * 若进来时选中的TAB仍存在（无论顺序有没有被调整），则仍然定位在进来的Tab上；
     * 若进来时选中的TAB被隐藏或者删除了，则默认定位在“全部”分组；
     * 若部分分组的名称被修改了，返回私募自选页时，更新对应分组的名称；
     */
    private fun goback(): Boolean {
        if (saveBtn?.isEnabled == true) {
            DlgHelper { _, which ->
                if (which == DlgHelper.IDlgHelper.DLG_POSITIVE) {
                    submitFeedback()
                } else if (which == DlgHelper.IDlgHelper.DLG_NEGATIVE) {
                    finishActivity()
                }
            }.showDialog(
                activity, DlgHelper.DlgArg("退出", "提交", "", "您的意见反馈未提交，请确认是否提交？")
                    .setBackCancelAble(false).setTouchCancelAble(false), 1
            )
            return true
        } else {
            finishActivity()
        }
        return false
    }

    override fun onDestroyView() {
        // 恢复原始的软键盘模式
        activity?.window?.setSoftInputMode(originalSoftInputMode)
        super.onDestroyView()
    }
}
