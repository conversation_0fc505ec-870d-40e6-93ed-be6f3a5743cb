package com.howbuy.global.user

import android.text.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.global.user.entity.LoginRecordBody
import com.howbuy.lib.utils.DateUtils

/**
 * description.
 * 登录日志UI数据渲染
 * tao.liang
 * 2024/1/23
 */
class AdpLoginRecord : BaseQuickAdapter<LoginRecordBody.LoginRecordItem, BaseViewHolder>(
    R.layout.adp_item_login_record_layout, null
) {


    override fun convert(holder: BaseViewHolder, item: LoginRecordBody.LoginRecordItem) {
        //字段无值时,显示 --
        holder.setText(R.id.tv_login_time, FundTextUtils.showTextEmpty(item.loginDtm))
        holder.setText(R.id.tv_login_ip, "IP地址: ${FundTextUtils.showTextEmpty(item.loginIp)}")
        holder.setText(R.id.tv_login_plamtform, FundTextUtils.showTextEmpty(item.loginSourceMemo))
        //PC端,H5端登录,是无值的
        holder.setText(R.id.tv_login_device, item.deviceName)
    }
}