package com.howbuy.global.user

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.account.UserDataHelper
import com.howbuy.account.api.DataWrapper
import com.howbuy.account.api.UserDataObserver
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.AtyBridgeHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.gesture.FingerUtils
import com.howbuy.gesture.ui.lockpattern.AtyGestureSetting
import com.howbuy.gesture.utils.GestureUtils
import com.howbuy.h5.H5UrlKey
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.user.databinding.FragSafeCenterBinding
import com.howbuy.global.user.dialog.DlgCheckPwd
import com.howbuy.global.user.userinfo.UserInfo
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker

/**
 * 安全中心
 * <AUTHOR>
 * @Date 2024/8/15
 * @Version V2.2
 */
@Route(path = PATH_FRAG_SAFE_CENTER)
class FragSafeCenter : AbsFragViewBinding<FragSafeCenterBinding>() {
    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragSafeCenterBinding {
        return FragSafeCenterBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_safe_center

    //手势密码开关
    private var hasGesture: Boolean? = null

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        val toolBarColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.white)
        StatusBarUtil.setStatusBarLightMode(activity ?: return, toolBarColor, true)
        AtyBridgeHelper.getAtyEmptyApi(activity!!).setToolbarTitle("安全中心")
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).actionBarToolBar?.setBackgroundColor(
            toolBarColor
        )
        binding.layModifyLoginPwd.setOnClickListener {
            if (apiUserInfo().hasLoginPwd()) {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "修改登录密码", H5UrlKey.UPDATE_LOGIN_PWD, null, null)
            } else {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "设置登录密码", H5UrlKey.SET_LOGIN_PWD, null, null)
            }
        }
        binding.layModifyTradePwd.setOnClickListener {
            if (apiUserInfo().hasTradePwd()) {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "修改交易密码", H5UrlKey.UPDATE_TRADE_PWD, null, null)
            } else {
                if (!TextUtils.isEmpty(apiUserInfo().mobileDigest()) && !TextUtils.isEmpty(
                        apiUserInfo().emailDigest()
                    )
                ) {
                    LogUtils.pop("设置交易密码")
                    Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                        this,
                        "设置交易密码",
                        H5UrlKey.SET_TRADE_PWD_PHONE_EMAIL,
                        null,
                        null
                    )
                } else if (!TextUtils.isEmpty(apiUserInfo().emailDigest())) {
                    Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                        this,
                        "设置交易密码",
                        H5UrlKey.SET_TRADE_PWD_EMAIL,
                        null,
                        null
                    )
                } else {
                    Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                        this,
                        "设置交易密码",
                        H5UrlKey.SET_TRADE_PWD_PHONE,
                        null,
                        null
                    )
                }
            }
        }
        binding.btnGesture.setOnClickListener {
            showPwdVerifyDlg(true)
        }
        binding.layModifyGesturePwd.setOnClickListener {
            showPwdVerifyDlg(false)
        }
        binding.btnFinger.setOnClickListener {
            //设置开关指纹
            FingerUtils.showFingerVerifyDialog(activity, childFragmentManager) {
                GlobalApp.getApp().runOnUiThread({
                    if (it) {
                        binding.switchFinger.isChecked = !binding.switchFinger.isChecked
                        if (binding.switchFinger.isChecked) {
                            LogUtils.pop("指纹验证已开启")
                            GestureUtils.saveFingerSwitch(true)
                        } else {
                            LogUtils.pop("指纹验证已关闭")
                            GestureUtils.saveFingerSwitch(false)
                        }
                    }
                }, 1)
            }
        }
        binding.layRemoveAccount.setOnClickListener {
            //跳转注销账户
            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.MEMBER_LOG_OFF, null, null)
        }
        binding.layLoginRecord.setOnClickListener {
            RouterHelper.launchFrag(this, PATH_FRAG_LOGIN_RECORD, NavHelper.obtainArg("登录日志"))
        }
    }

    override fun parseArgment(arg: Bundle?) {
        UserDataHelper.getDataManager(DataIds.ID_USER_INFO).addObserver(this, true, object :
            UserDataObserver<UserInfo> {
            override fun onChanged(t: DataWrapper<UserInfo>) {
                if (activity == null || activity?.isFinishing == true) {
                    return
                }
                binding.tvLabelModifyPassword.text =
                    if (apiUserInfo().hasLoginPwd()) "修改登录密码" else "设置登录密码"
                binding.tvLabelModifyTradePwd.text =
                    if (apiUserInfo().hasTradePwd()) "修改交易密码" else "设置交易密码"
            }
        })
    }

    override fun onResume() {
        super.onResume()
        //上一次的设置手势密码开关状态(默认是关闭的)
        val currentHasGesture = GestureUtils.getPatternSwitch(false)
        if (hasGesture != null && hasGesture != currentHasGesture) {
            if (currentHasGesture) {
                LogUtils.pop("手势验证已开启")
            } else {
                LogUtils.pop("手势验证已关闭")
            }
        }
        hasGesture = currentHasGesture
        handleGestureView(currentHasGesture)
        context?.let {
            if (Invoker.getInstance().navigation(ILoginProvider::class.java).hasFinger(it)) {
                binding.layFingerPwd.visibility = View.VISIBLE
                binding.switchFinger.isChecked = GestureUtils.getFingerSwitch()
            } else {
                binding.layFingerPwd.visibility = View.GONE
            }
        }
    }

    /**
     * 设置手势密码开关UI
     */
    private fun handleGestureView(hasGesture: Boolean) {
        binding.switchGesture.isChecked = hasGesture
        if (hasGesture) {
            binding.tvModifyGesturePwd.setTextColor(ColorUtils.parseColor("#4b4e61"))
            binding.layModifyGesturePwd.isClickable = true
        } else {
            binding.tvModifyGesturePwd.setTextColor(ColorUtils.parseColor("#999999"))
            binding.layModifyGesturePwd.isClickable = false
        }
    }

    /**
     * 登录密码验证弹框
     */
    private fun showPwdVerifyDlg(isClickSwitch: Boolean) {
        activity?.let {
            if (apiUserInfo().hasLoginPwd()) {
                //有登录密码弹框验证登录密码
                DlgCheckPwd.getInstance(null) { inputPwd ->
                    if (TextUtils.isEmpty(inputPwd)) {
                        pop("密码不能为空", false)
                    } else {
                        UserRequest.validateLoginPwd(inputPwd, 0) {
                            if (it.isSuccess) {
                                handleValidateLoginPwd(isClickSwitch)
                            } else {
                                //验证失败
                                handleValidateLoginPwdError(it)
                            }
                        }
                    }
                }.show(childFragmentManager, "")
            } else {
                //没有登录密码，跳转设置登录密码页面
                LogUtils.pop("请先设置登录密码")
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "设置登录密码", H5UrlKey.SET_LOGIN_PWD, null, null)
            }
        }
    }

    private fun handleValidateLoginPwdError(it: ReqResult<ReqNetOpt>) {
        if (it.mErr == null || TextUtils.isEmpty(it.mErr.message)) {
            LogUtils.pop("密码错误，请重新输入")
        } else {
            val errMsg = HandleErrorMgr.handErrorMsg(it.mErr, true)
            LogUtils.pop(errMsg)
        }
    }

    /**
     * 登录密码验证成功
     */
    private fun handleValidateLoginPwd(isClickSwitch: Boolean) {
        //验证通过
        if (isClickSwitch) {
            if (hasGesture == true) {
                //关闭手势密码
                hasGesture = false
                GestureUtils.savePatternSwitch(false)
                handleGestureView(false)
                LogUtils.pop("手势验证已关闭")
            } else {
                //开启手势密码，并跳转设置手势密码页面--设置成功才会将SpConfig.SF_PATTERN_SWITCH设置为true
                launcherSettingGesture()
            }
        } else {
            //跳转设置手势密码页面
            launcherSettingGesture()
        }
    }

    /**
     * 跳转设置手势密码页面
     */
    private fun launcherSettingGesture() {
        val b = NavHelper.obtainArg("修改手势密码", AtyGestureSetting.CANGOBACK, true)
        b.putBoolean(AtyGestureSetting.NODLG, true) //点击 "跳过设置" 是否要弹出dialog
        Invoker.getInstance().navigation(ILoginProvider::class.java)
            .launchModifyGestureSetting(activity)
    }
}