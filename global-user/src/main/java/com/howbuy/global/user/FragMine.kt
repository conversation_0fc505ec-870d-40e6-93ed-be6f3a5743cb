package com.howbuy.global.user

import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import com.howbuy.account.UserDataHelper
import com.howbuy.account.api.DataWrapper
import com.howbuy.account.api.UserDataObserver
import com.howbuy.account.remote.FetchAction
import com.howbuy.account.remote.ResponseValue
import com.howbuy.analytics.PvReportUtils
import com.howbuy.android.analytics.annotation.pv.PvInfo
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.arch.AutoReleaseObserver
import com.howbuy.fund.base.arch.AutoReleaseSingleObserver
import com.howbuy.fund.base.arch.ClearViewModel
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.push.PushDispatchHelper
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ShapeCreator
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.base.widget.RadiusSpan
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.fund.net.error.WrapException
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.fund.util.VisibleGone
import com.howbuy.global.common.banner.BannerImageAdp
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.isLogined
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginCallback
import com.howbuy.global.login_api.LoginParams
import com.howbuy.global.login_api.LoginResult
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.user.ad.AdItem
import com.howbuy.global.user.ad.AdResult
import com.howbuy.global.user.amtdesc.AmtDescTitle
import com.howbuy.global.user.amtdesc.MineAmtDescDlg
import com.howbuy.global.user.amtdesc.QxnProdBody
import com.howbuy.global.user.announcement.Announcement
import com.howbuy.global.user.announcement.AnnouncementHelper
import com.howbuy.global.user.announcement.AnnouncementResult
import com.howbuy.global.user.databinding.FragMineBinding
import com.howbuy.global.user.entity.AboutUsItemInfo
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.global.user.mine.AccountStateLogic
import com.howbuy.global.user.mine.AssetsModuleRender
import com.howbuy.global.user.mine.MineAssetDataManager
import com.howbuy.global.user.mine.ModuleRender
import com.howbuy.global.user.mine.ToolbarRender
import com.howbuy.global.user.settings.AmtType
import com.howbuy.global.user.userinfo.ApiImplUserInfo
import com.howbuy.global.user.userinfo.UserAssets
import com.howbuy.global.user.userinfo.UserAssetsFetcher
import com.howbuy.h5.H5UrlKey
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.StatusBarUtil
import com.howbuy.lib.utils.SysUtils
import com.howbuy.lib.utils.ViewUtils
import com.howbuy.login.LoginManagerImpl
import com.howbuy.login.LoginObserver
import com.howbuy.login.LogoutObserver
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
 * @Description 我的页面
 * <AUTHOR>
 * @Date 2024/2/18
 * @Version V1.0
 */
@PvInfo(pageId = "351250", level = "1", name = "个人中心页", className = "FragMine")
class FragMine : AbsFragViewBinding<FragMineBinding>(), AnnouncementHelper.INoticeInvoke {
    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragMineBinding {
        return FragMineBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_mine

    //弹框说明中:千喜年购买产品数据
    private var mQxnProdBody: QxnProdBody? = null


    private val mAnnounceHelper = AnnouncementHelper(this)


    private var mUserAssets: DataWrapper<UserAssets>? = null
    private var mUserReqFinish = false
    private var mAssetsReqFinish = false

    //当前币种, 无值时默认: 美元
    var mCurAmtType: AmtType? = AmtType.DOLLAR

    private val toolbarRender: ToolbarRender by lazy { ToolbarRender(binding.toolbarContainer, this@FragMine) }
    private val assetModule: AssetsModuleRender by lazy { AssetsModuleRender(binding.layoutLogin.layAssets, this@FragMine) }
    private var assetDataManager: MineAssetDataManager = MineAssetDataManager.getInstance()

    private val moduleList: List<ModuleRender> by lazy {
        listOf(toolbarRender,
            assetModule)
    }

    //未登录关于我们
    private val adpAboutUs by lazy {
        AdpUnLoginAboutUs()
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        (activity as AtyEmpty?)?.setToolbarVisibility(false)
        StatusBarUtil.translucentStatusBar(activity, true)
        binding.vStatusbar.minimumHeight = SysUtils.getStatusBarHeight(activity)
        LogUtils.d("Mine-Frag", "stepAllViews, status bar height:${SysUtils.getStatusBarHeight(activity)}")

        binding.refreshLayout.setOnRefreshListener {
            refreshData()
        }
        initUnlogin()

        binding.layoutLogin.banner.registerLifecycleObserver(lifecycle)
            .setIndicatorMargin(0, 0, 0, DensityUtils.dp2px(5f))
            .setAdapter(object : BannerImageAdp<AdItem>(
                binding.layoutLogin.banner.context,
                null,
                object : DataTransform<AdItem> {
                    override fun transform(data: AdItem): BaseBannerItem {
                        return BaseBannerItem(data.adImg, data.onClick)
                    }
                }
            ) {
                override fun analytics(position: Int, data: AdItem) {
                    HbAnalytics.onClick("610310", data?.adTitle, data?.onClick)
                }

                override fun onItemClick(position: Int, data: AdItem): Boolean {
                    BannerClickMgr.onBannerClick(this@FragMine, data)
                    return true
                }
            })


        UserDataHelper.getDataManager(DataIds.ID_USER_INFO)
            .addObserver(this, true, object : UserDataObserver<Any> {
                override fun onChanged(t: DataWrapper<Any>) {
                    //用户数据
                    mUserReqFinish = true
                    if (isLogined()) {
                        renderAssets()
                    }
                    initViews()
                }
            })

        fetchAssetData()

        moduleList.forEach { module ->
            module.init()
        }
        initViews()
        initClick()
        loadAssetsData()
    }

    private fun fetchAssetData() {
        UserAssetsFetcher().fetch(FetchAction.dataChanged)
            .flatMap { res ->
                val fnl: ResponseValue<UserAssets> = if (res.success) res else ResponseValue(
                    assetDataManager.dataManager.getSync<UserAssets>().data,
                    false,
                    res.error
                )
                Observable.just(fnl)
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object :
                AutoReleaseObserver<ResponseValue<UserAssets>>(this, ClearViewModel::class.java) {
                override fun onNext(t: ResponseValue<UserAssets>) {
                    if (t.success && t.data != null) {
                        //数据正常：
                        //1. 执行渲染
                        //2. 更新缓存
                        assetDataManager.dataManager.update(t.data)
                    } else {
                        //数据异常：
                        // 1. 执行渲染，异常处理
                        if (t.error is WrapException) {
                            val errorMsg =
                                HandleErrorMgr.handErrorMsg(t.error as WrapException, true)
                            LogUtils.pop(errorMsg)
                        } else if (t.error != null) {
                            LogUtils.pop(t.error?.message ?: "")
                        }
                    }
                    mUserAssets = DataWrapper(t.data, false, t.error)
                    assetModule.refreshAssetStateChanged(false)
                    mAssetsReqFinish = true
                    if (isLogined()) {
                        renderAssets()
                    }
                }

                override fun onError(e: Throwable) {
                    //请求失败
                    //1. 执行渲染，异常处理
                    val err: Exception = if (e is Exception) e else RuntimeException(e)
                    mUserAssets = DataWrapper(null, false, err)
                    assetModule.refreshAssetStateChanged(false)
                    mAssetsReqFinish = true
                    if (isLogined()) {
                        renderAssets()
                    }
                }
            })
    }


    /**
     * 加载资产数据
     */
    private fun loadAssetsData() {
        if (!isLogined()) return
        // 获取缓存的资产数据
        //处理，资产模块、开户的显示状态处理
        renderAccountStatus()
        val accountState = AccountStateLogic().resolveState()
        if (!accountState.showModule) {
            //如果显示资产模块，则处理资产数据
            Single.create<DataWrapper<UserAssets>> {
                val dataCache = assetDataManager.dataManager.getSync<UserAssets>()
                DataWrapper(dataCache.data, true, null)
            }.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(object : AutoReleaseSingleObserver<DataWrapper<UserAssets>>(this, ClearViewModel::class.java) {
                    override fun onSuccess(t: DataWrapper<UserAssets>) {
                        assetModule.onAssetReady(t)
                    }

                    override fun onError(e: Throwable) {
                        LogUtils.d("Mine-Frag", "asset cache load fail:$e")
                    }
                })
        }
    }


    private fun initClick() {
        binding.layUnlogin.tvLogin.setOnClickListener {
            login()
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            (activity as AtyEmpty?)?.setToolbarVisibility(false)
            StatusBarUtil.translucentStatusBar(activity, true)
            refreshData()

            pvAnalysis()
        }
    }

    override fun onResume() {
        super.onResume()
        refreshData()
    }

    override fun parseArgment(arg: Bundle?) {
        pvAnalysis()
        observableLoginStatus()
    }

    /**
     * 监听用户登录状态
     */
    private fun observableLoginStatus() {
        //直接使用lifecycle会移除不掉
        LoginManagerImpl.getInstance().addLogoutObserver(unLoginObservable)
        LoginManagerImpl.getInstance().addLoginObserver(loginObservable)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        LoginManagerImpl.getInstance().removeLogoutObserver(unLoginObservable)
        LoginManagerImpl.getInstance().removeLoginObserver(loginObservable)
    }

    private fun pvAnalysis() {
        PvReportUtils.reportPvIfPvInfoExists(
            javaClass,
            context, fundCode, alysContentId, null
        )
    }

    /**退出登录监听*/
    private val unLoginObservable: LogoutObserver = LogoutObserver {
        if (activity == null || activity?.isFinishing == true) {
            return@LogoutObserver
        }
        mAnnounceHelper.loginStateChange = true
        this.assetDataManager.reCreateManager()
        mUserAssets = null
        initViews()
        refreshData()
        //未登录，不显示公告
        VisibleGone.handle(binding.layAnnouncement.root, false)
        LogUtils.d("LoginAccountMgr", "FragMin执行未登录逻辑")
    }

    /**登录监听*/
    private val loginObservable: LoginObserver = LoginObserver {
        if (activity == null || activity?.isFinishing == true) {
            return@LoginObserver
        }
        this.assetDataManager.reCreateManager()
        mAnnounceHelper.loginStateChange = true
        initViews()
        refreshData()
        LogUtils.d("LoginAccountMgr", "FragMin执行已登录逻辑")
    }

    fun refreshAsset() {
        LogUtils.d("Mine-Frag", "refreshAsset")
        assetModule.refreshAssetStateChanged(true)
//        UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_ASSETS, FetchAction.dataChanged)
        fetchAssetData()
    }

    private fun refreshData() {
        mUserReqFinish = false
        mAssetsReqFinish = false
        mAnnounceHelper.queryAnnounce(AnnouncementHelper.REQ_ID_MINE_TOP)
        if (isLogined()) {
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_INFO, FetchAction.dataChanged)
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.dataChanged)
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_HOLD, FetchAction.dataChanged)
            assetModule.refreshAssetStateChanged(true)
//            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_ASSETS, FetchAction.dataChanged)
            fetchAssetData()
            UserRequest.queryBanner("1", 0) {
                if (activity == null || activity?.isFinishing == true) return@queryBanner
                if (it.isSuccess && it.mData is AdResult && !(it.mData as AdResult).advertisings.isNullOrEmpty()) {
                    binding.layoutLogin.banner.visibility = View.VISIBLE
                    val data = it.mData as AdResult
                    binding.layoutLogin.banner.create(data.advertisings)
                } else {
                    binding.layoutLogin.banner.visibility = View.GONE
                }
            }
            UserRequest.reqFundQxnProdList(mCurAmtType?.code ?: AmtType.DOLLAR.code, 1) {
                if (activity == null || activity?.isFinishing == true) {
                    return@reqFundQxnProdList
                }
                if (it.isSuccess && it.mData != null) {
                    mQxnProdBody = it.mData as QxnProdBody
                }
            }
        }
    }

    private fun renderUserInfo() {
//        renderContractFile()
//        renderTradeRecord()
//        renderCashProtoQuery()
//        renderHwPiggyServerQuery()
    }

    /**
     * 开户/入金引导区域
     * //01-去开户；02-继续开户；03-查看开户进度；04-修改开户资料；05-去入金；06-查看入金进度；07- 修改入金资料；08-隐藏开户入金区域
     */
    private fun renderAccountStatus() {
        val accountState = AccountStateLogic().resolveState()
        LogUtils.d("Mine-Frag", "[asset] renderAccountStatus, visible:${!accountState.showModule}")
        VisibleGone.handle(binding.layoutLogin.layAssets.root, !accountState.showModule)
        VisibleGone.handle(binding.layoutLogin.layAccountStatus.root, accountState.showModule)

        if (accountState.showModule) {
            binding.layoutLogin.layAccountStatus.tvAccountHint.text = accountState.hintText
            VisibleGone.handle(binding.layoutLogin.layAccountStatus.tvAccountErrorMsg, accountState.errHintText.isNullOrEmpty())
            binding.layoutLogin.layAccountStatus.tvAccountErrorMsg.text = accountState.errHintText
            binding.layoutLogin.layAccountStatus.btnAccount.text = accountState.btnText
            binding.layoutLogin.layAccountStatus.btnAccount.setOnClickListener {
                if (!TextUtils.isEmpty(accountState.btnText)) {
                    Invoker.getInstance()
                        .navigation(IWebProvider::class.java)
                        .launchWebView(this, "", accountState.btnText, accountState.linkParam, null)
                }
            }
        }
    }

    /**
     * 资产区域
     */
    private fun renderAssets() {
        LogUtils.d("Mine-frag", "renderAssets, mUserReqFinish:$mUserReqFinish, mAssetsReqFinish:$mAssetsReqFinish")
        if (!mUserReqFinish || !mAssetsReqFinish) return
        showAlermDlg(null, 0)
        binding.refreshLayout.finishRefresh()

        if (!isLogined()) return

        renderAccountStatus()
        //刷新资产区域
        this.assetModule.onAssetReady(this.mUserAssets)

        var showAssetLink = false
        if (apiUserInfo().finishOpenAccount()) {
            //已开户显示
            val userAssets = ApiImplUserInfo.getUserAssets()
            val assetsMsgList = mutableListOf<MineAssetsMsg>()
            val inTransitTradeNums = MathUtils.forValI(userAssets?.inTransitTradeNums, 0)
            if (inTransitTradeNums > 0) {
                assetsMsgList.add(
                    MineAssetsMsg(
                        MineAssetsMsg.TYPE_IN_TRANSIT_TRADE_NUMS,
                        count = inTransitTradeNums,
                        inTransitDealNo = userAssets?.inTransitDealNo
                    )
                )
            }
            val fundReceivedNums = MathUtils.forValI(userAssets?.fundReceivedNums, 0)
            if (fundReceivedNums > 0) {
                assetsMsgList.add(
                    MineAssetsMsg(
                        MineAssetsMsg.TYPE_FUND_RECEIVED_NUMS,
                        count = fundReceivedNums
                    )
                )
            }
            //现金审核消息
            val shingNum = MathUtils.forValI(userAssets?.underReviewCount, 0)
            val shNotNum = MathUtils.forValI(userAssets?.notPassCount, 0)
            if (shingNum > 0 || shNotNum > 0) {
                //现金消息,要放在第一条
                assetsMsgList.add(
                    0,
                    MineAssetsMsg(
                        MineAssetsMsg.TYPE_CASH_HINT,
                        cashShenHeingNum = shingNum,
                        cashShenHeNotNum = shNotNum,
                        voucherNo = userAssets?.voucherNo
                    )
                )
            }
            showAssetLink = assetsMsgList.isNotEmpty()
            if (showAssetLink) {
                if (binding.layoutLogin.layAssets.rvAssetsMsg.itemDecorationCount == 0) {
                    binding.layoutLogin.layAssets.rvAssetsMsg.addItemDecoration(
                        XLinearBuilder(
                            context
                        ).setDrawableRes(R.drawable.dash_line).build()
                    )
                }
                binding.layoutLogin.layAssets.rvAssetsMsg.adapter =
                    AdpMineAssetsMsg(assetsMsgList, apiUserInfo().showUserAssetsAmt())
            }
        }
        VisibleGone.handle(binding.layoutLogin.layAssets.ivLinkLeft, showAssetLink)
        VisibleGone.handle(binding.layoutLogin.layAssets.ivLinkRight, showAssetLink)
        VisibleGone.handle(binding.layoutLogin.layAssets.rvAssetsMsg, showAssetLink)
    }


    /**
     * 合同文件查询
     * 校验是否已完成开户且开户方式=线上开户
     * a）若满足，入口按钮展示“合同文件查询”，点击跳转“合同文件查询页”
     * b）若不满足，入口按钮展示“产品合同查询”，点击跳转“产品合同查询页”
     */
    private fun View.renderContractFile() {
        setOnClickListener {
            val bundle = Bundle()
            if (apiUserInfo().finishOpenAccount()) {
                //已开户才有这个弹框逻辑,否则不会有
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
            }
            Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                this, "",
                if (apiUserInfo().finishOpenAccount() && apiUserInfo().onLineOpenAccount()) {
                    H5UrlKey.CONTRACT_LIST
                } else {
                    H5UrlKey.POCT_SEARCH
                }, null, bundle, null
            )
        }
    }

    /**
     * 交易记录
     * 若当前账号已登陆 且 已完成开户
     */
    private fun View.renderTradeRecord() {
        if (apiUserInfo().finishOpenAccount()) {
            setOnClickListener {
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.TRADE_LIST_IDX, null, bundle, null)
            }
        }
    }

    /**
     * 现金存入凭证的查询
     * 若当前账号已登陆 且 已完成开户 显示,否则,隐藏
     */
    private fun View.renderCashProtoQuery() {
        if (apiUserInfo().finishOpenAccount()) {
            setOnClickListener {
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.ASSET_PROOF_LIST, null, bundle, null)
                HbAnalytics.onClick("611250")
            }
        }
    }

    /**
     * 海外储蓄罐服务
     * 若当前账号已登陆 则显示,否则隐藏
     */
    private fun View.renderHwPiggyServerQuery() {
        if (apiUserInfo().isLogined()) {
            setOnClickListener {
                val hasSignPiggyProtol = apiUserInfo().getHwPiggySigned()
                val piggyH5url = if (hasSignPiggyProtol) {
                    //若【客户储蓄罐协议状态】=已签署，则跳转至“海外储蓄罐服务”页
                    H5UrlKey.CXG_ZONE_DETAIL
                } else {
                    //若【客户储蓄罐协议状态】≠已签署 或 【客户储蓄罐协议状态】为空，则跳转至“海外储蓄罐签约页”
                    H5UrlKey.CXG_SIGN_DETAIL
                }
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", piggyH5url, null, null, null)
                HbAnalytics.onClick("611260")
            }
        }
    }

    /**
     * 公告接口回调
     */
    override fun onNoticeInvoke(notices: AnnouncementResult?, notice: Announcement?) {
        if (activity == null || activity?.isFinishing == true) return
        LogUtils.d(TAG, "公告返回---")
        renderAnnouncement(notice)
    }

    /**
     * 公告
     */
    fun renderAnnouncement(notice: Announcement?) {
        if (notice == null || TextUtils.isEmpty(notice.desc) || !isLogined()) {
            binding.layAnnouncement.root.visibility = View.GONE
        } else {
            val isOverWriteNotice = AnnouncementHelper.queryMsgIsClosed(notice)
            if (isOverWriteNotice) {
                binding.layAnnouncement.root.visibility = View.GONE
            } else {
                binding.layAnnouncement.root.visibility = View.VISIBLE
                var showClose = false
                var isStoreCloseAction = false
                if (TextUtils.equals(AnnouncementHelper.Notice_Type_INFO, notice.important)) {
                    showClose = true
                    isStoreCloseAction = true
                } else if (TextUtils.equals(
                        AnnouncementHelper.Notice_Type_WARN,
                        notice.important
                    )
                ) {
                    showClose = true
                }
                binding.layAnnouncement.tvAnnouncementContent.text = notice.desc
                binding.layAnnouncement.tvAnnouncementContent.requestFocus()
                ViewUtils.setVisibility(
                    binding.layAnnouncement.ivAnnouncementClose,
                    if (showClose) View.VISIBLE else View.GONE
                )
                //关闭按钮显示才设置处关闭click事件
                if (showClose) {
                    val finalIsStoreCloseAction = isStoreCloseAction
                    binding.layAnnouncement.ivAnnouncementClose.setOnClickListener {
                        if (finalIsStoreCloseAction) {
                            AnnouncementHelper.storeClosedMsg(notice.productId)
                        }
                        binding.layAnnouncement.root.visibility = View.GONE
                    }
                }
                binding.layAnnouncement.tvAnnouncementContent.setOnClickListener {
                    if (TextUtils.isEmpty(notice.link)) {
                        val contentList = notice.desc?.split("\n")
                        AlertListStringDlg.getInstance(
                            "公 告",
                            ArrayList(contentList ?: arrayListOf())
                        ).show(childFragmentManager, "AnnouncementDlg")
                    } else {
                        PushDispatchHelper.pushDispatch(context, notice.link)
                    }
                }
            }
        }
    }


    private fun initViews() {
        val login = isLogined()
        binding.layUnlogin.root.visibility = if (login) View.GONE else View.VISIBLE
        binding.layoutLogin.root.visibility = if (login) View.VISIBLE else View.GONE
        binding.ivMineMsg.visibility = if (login) View.VISIBLE else View.GONE
        binding.ivMineSettings.visibility = if (login) View.VISIBLE else View.GONE
        if (!login) {
            //未登录,隐藏消息模块, 已登录,需要在消息回调中判断是否有未读消息,才显示
            binding.layoutLogin.layMessageModule.root.visibility = View.GONE
        }
        binding.refreshLayout.isEnableRefresh = login
        moduleList.forEach { module -> module.handleLoginChanged(login) }
    }

    private fun initUnlogin() {
        if (!isLogined()) {
            toolbarRender.handleLoginChanged(false)
        }
        if (binding.layUnlogin.rvList.adapter == null) {
            binding.layUnlogin.rvList.layoutManager =
                LinearLayoutManager(binding.layUnlogin.root.context)
            binding.layUnlogin.rvList.adapter = adpAboutUs
        }

        binding.layUnlogin.tvLogin.background = ShapeCreator()
            .radius(20F)
            .color(ColorUtils.parseColor("#FFC51D25"))
            .create()

        binding.layUnlogin.tvAboutUs.setSpanStyle(2, ColorUtils.parseColor("#FFC51D25"))

        if (adpAboutUs.data.size == 0) {
            val arrayList = ArrayList<AboutUsItemInfo>()
            arrayList.add(
                AboutUsItemInfo(
                    R.mipmap.icon_about_us_world,
                    "以全球视野覆盖各类资产，提供定制化资产配置方案。",
                    "全球配置"
                )
            )
            arrayList.add(
                AboutUsItemInfo(
                    R.mipmap.icon_about_us_study,
                    "超十年深度研究与调研积淀，专注基金理财产品研究。",
                    "专业研究"
                )
            )
            arrayList.add(
                AboutUsItemInfo(
                    R.mipmap.icon_about_us_preferred,
                    "深入挖掘，层层筛选、严格把控，打造具有差异化的产品池。",
                    "优选产品"
                )
            )
            adpAboutUs.setList(arrayList)
        }

        if (binding.layUnlogin.rvList.itemDecorationCount == 0) {
            binding.layUnlogin.rvList.addItemDecoration(
                XLinearBuilder(context)
                    .setSpacing(25F)
                    .setShowFirstTopLine(true)
                    .setShowLastLine(true)
                    .setLastLineExtraSpace(5F)
                    .build()
            )
        }
    }

    fun login() {
        Invoker.getInstance()
            .navigation(ILoginProvider::class.java)
            .login(
                LoginParams(activity ?: return, LoginType.captcha, null),
                "FragMine",
                object : LoginCallback {
                    override fun onComplete(result: LoginResult) {
                        LogUtils.d(
                            "LoginAccountMgr",
                            "FragMine监听回调,登录后,通过LoginObserver实现UI刷新,没有定位tab功能了,result=" + result.success
                        )
                        if (result.success) {
                            moduleList.forEach { moduleRender -> moduleRender.handleLoginChanged(true) }
                        }
                    }
                })

    }


    /**
     * 设置最新一条未读消息,显示内容
     */
    fun setLastedMsgModuleUIData(unReadNum: Int, messageInfo: MessageItem?) {
        binding.layoutLogin.layMessageModule.tvMineMsgModuleUnreadMsgCount.text =
            SpannableUtils.formatStr(
                SpannableItem("您有"),
                SpannableItem(
                    unReadNum.toString(),
                    DensityUtils.dip2px(21f),
                    ColorUtils.parseColor("#C51D25")
                ),
                SpannableItem("条", ColorUtils.parseColor("#C51D25")),
                SpannableItem("未读消息"),
            )
        val msgContent = FundTextUtils.showTextEmpty(messageInfo?.messageTitle) + " "
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgTitle.text = msgContent
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.visibility = View.GONE
        val msgDate = FundTextUtils.formatDate(
            messageInfo?.messageDate,
            DateUtils.DATEF_YMD, DateUtils.DATEF_YMD_9, DateUtils.DATEF_YMD_8
        )
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.text = msgDate
        val leftWidth = SysUtils.getWidth(activity) - DensityUtils.dp2px(60f)
        val dateStrLen =
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.paint.measureText(msgDate)
        val leftSpace = leftWidth - dateStrLen
        val msgStrLen =
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgTitle.paint.measureText(
                msgContent
            )
        if (leftSpace < msgStrLen) {
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.visibility = View.GONE
            val label = RadiusSpan.Builder()
                .setText(msgDate)
                .setTextSize(14f)
                .setBgColor(Color.TRANSPARENT)
                .setTxtColor(ColorUtils.parseColor("#999999"))
                .setRightMargin(DensityUtils.dp2px(1f))
                .build()
            val sp = SpannableString("$msgContent ")
            sp.setSpan(
                label,
                msgContent.length,
                msgContent.length + 1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgTitle.text = sp
        } else {
            binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDate.visibility = View.VISIBLE
        }
        binding.layoutLogin.layMessageModule.tvMineMsgModuleMsgDesc.text =
            FundTextUtils.showTextEmpty(messageInfo?.messageDesc)
        //消息模块点击事件
        binding.layoutLogin.layMessageModule.layMineMsgTitle.setOnClickListener {
            //点击 "未读消息标题",跳转到消息列表页面
            launcherToMsgPage(0)
            HbAnalytics.onClick("611180")
        }
        //窗口消息摘要
        binding.layoutLogin.layMessageModule.root.setOnClickListener {
            if (unReadNum == 1) {
                //若{未读消息数量}=1，则跳转至各场景流程页面
                activity?.let {
                    MessageLauncher.launchToDoDetail(it, messageInfo, isMineClick = true)
                }
            } else {
                //若{未读消息数量}>1，则跳转消息列表页面，默认选中消息列表
                launcherToMsgPage(0)
            }
            HbAnalytics.onClick("611190")
        }
    }



    /**
     * 跳转到消息列表页面(由于模块跳转,这里使用短命令方式跳转实现解耦)
     * @param pageIndex 跳转到消息中心,定位列表下标
     */
    fun launcherToMsgPage(pageIndex: Int) {
        val url = "T=(HWMSG)&V=(${pageIndex})"
        PushDispatchHelper.pushDispatch(activity, url)
    }


    /**
     * 显示资产提示弹框
     */
    private fun showAmtHintDlg(titleType: AmtDescTitle) {
        MineAmtDescDlg.getInstance(
            NavHelper.obtainArg(
                "", ValConfig.IT_TYPE, titleType.type,
                ValConfig.IT_ENTITY, ApiImplUserInfo.getUserAssets(),
                ValConfig.IT_VALUE_1, mQxnProdBody
            )
        ).show(childFragmentManager, null)
    }

}