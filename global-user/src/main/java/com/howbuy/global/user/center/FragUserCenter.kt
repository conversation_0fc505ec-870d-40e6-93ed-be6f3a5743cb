package com.howbuy.global.user.center

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.utils.ResourceUtils
import com.howbuy.fund.base.utils.ShapeCreator
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.util.VisibleGone
import com.howbuy.global.data_api.ApiUserInfo
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.user.MineRouterPath
import com.howbuy.global.user.OpenAccountGuide
import com.howbuy.global.user.R
import com.howbuy.global.user.databinding.FragUserCenterBinding
import com.howbuy.global.user.mine.getAvatarResource
import com.howbuy.global.user.mine.getNickNameToShow
import com.howbuy.h5.H5UrlKey
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import kotlinx.android.synthetic.main.frag_user_center.entryEmail

@Route(path = MineRouterPath.PATH_FRAG_USER_CENTER)
class FragUserCenter : AbsFragViewBinding<FragUserCenterBinding>() {
    private val openAccountLogic: UserCenterOpenAccountLogic by lazy {
        UserCenterOpenAccountLogic()
    }

    override fun getFragLayoutId(): Int {
        return R.layout.frag_user_center
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        //不显示分割线
        activity?.findViewById<View>(R.id.toolbar_line)?.visibility = View.GONE
        setModulesStyle()
    }

    private fun setModulesStyle() {
        val colorBg = ResourceUtils.getColor(R.color.white)
        val radiusPx = DensityUtils.dp2px(15f).toFloat()
        binding.llBlock1.background = ShapeCreator.createRoundRectangle(colorBg, 15f)
        binding.llAccounts.background = ShapeCreator.createRoundRectangle(colorBg, 15f)
        binding.llBlock2.background = ShapeCreator.createRoundRectangle(colorBg, 15f)
        binding.llBlock3.background = ShapeCreator.createRoundRectangle(colorBg, 15f)
        //（3）部分三：持牌人名片
        binding.entryCpr.background = ShapeCreator.createRoundRectangle(colorBg, 15f)
    }

    override fun createViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragUserCenterBinding {
        return FragUserCenterBinding.inflate(inflater, container, false)
    }

    override fun parseArgment(arg: Bundle?) {
        openAccountLogic.resolveAccountStatus()
        rendValues(apiUserInfo())
    }

    private fun rendValues(apiUserInfo: ApiUserInfo) {
        //头像
        binding.ivAvatar.setImageResource(true.getAvatarResource()) //todo 性别取值
        //昵称
        binding.entryNickname.rightText = apiUserInfo.getNickNameToShow("请设置")
        binding.entryNickname.setOnClickListener {
            //todo 点击后进入“昵称设置”页
            RouterHelper.launchFragWithCallback(this,
                "/user/global/FragSetNickname", //todo
                NavHelper.obtainArg("设置昵称"), //todo
                ) { code, data ->
                LogUtils.d("UserCenter", "从设置昵称页返回了, code:$code")
                true
            }
        }

        //手机号
        val areaCode = "86" //TODO【地区码】
        binding.entryMobile.rightText = apiUserInfo.mobileMask().emptyToNull()?.transformTo { "+${areaCode} $it" }.or("未绑定")
        binding.entryMobile.setOnClickListener {
            //① 若交易密码不为空，则跳转到“修改手机号”页面
            //② 若交易密码为空，则toast提示：”您已开户，请先前往安全中心设置交易密码”
            CheckFlow().addChecker(notSetTradePwd, actionNotSetTradePwd).onAllPass {
                // 跳转到“修改手机号”页面
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                //交易密码不为空,则跳转<修改手机号页面>
                Invoker.getInstance()
                    .navigation(IWebProvider::class.java)
                    .launchWebView(
                        this,
                        "",
                        H5UrlKey.UPDATE_MOBILE,
                        null,
                        bundle,
                        null
                    )
            }
        }

        //邮箱
        binding.entryEmail.rightText = apiUserInfo.emailMask().emptyToNull().or("未绑定")
        //在开户完成后才显示
        VisibleGone.handle(entryEmail, apiUserInfo.finishOpenAccount())
        binding.entryEmail.setOnClickListener {
            //跳转icon仅在开户完成后才显示，即【客户状态】= “正常/休眠” 显示
            //（2）跳转icon显示时，点击整行校验交易密码是否为空
            //① 若交易密码不为空，则跳转到“修改邮箱页”页面
            //② 若交易密码为空，则toast提示：”您已开户，请先前往安全中心设置交易密码”
            CheckFlow().addChecker(notSetTradePwd, actionNotSetTradePwd)
                .onAllPass {
                    //则跳转到“修改邮箱页”页面
                    val bundle = Bundle()
                    bundle.putIntArray(
                        ValConfig.IT_INTERCEPT_CODE,
                        intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                    )
                    //交易密码不为空,则跳转<修改邮箱页面>
                    Invoker.getInstance()
                        .navigation(IWebProvider::class.java)
                        .launchWebView(this,
                            "",
                            H5UrlKey.UPDATE_EMAIL,
                            null,
                            bundle,
                            null)
                }
        }

        //香港客户编号
        val hkCustNo = apiUserInfo.getHkCustNo()
        binding.entryHkUserNo.rightText = apiUserInfo.getHkCustNo()
        binding.entryHkUserNo.rightArrowVisible = false
        binding.entryHkUserNo.isEnabled = false
        VisibleGone.handle(binding.entryHkUserNo, !hkCustNo.isNullOrEmpty()) //无值，不显示

        //TODO 账号绑定 依赖2.9
        val hasBindThirdAccount = false //TODO
        val icons = binding.llAccounts.findViewById<View>(R.id.llAccIcons)
        val tvNone = binding.llAccounts.findViewById<View>(R.id.tvRightText)
        VisibleGone.handle(icons, hasBindThirdAccount)
        VisibleGone.handle(tvNone, !hasBindThirdAccount)
        binding.llAccounts.setOnClickListener {
            //todo 页面跳转至“账号绑定页”，后续处理逻辑同产线
        }

        //银行卡
        val bankCardCount = apiUserInfo.bindCardCount()
        binding.entryBankCard.rightText = if (bankCardCount > 0) "$bankCardCount 张" else "未绑定"
        binding.entryBankCard.rightArrowVisible = bankCardCount > 0
        binding.entryBankCard.setOnClickListener { v->
            // 若绑定的银卡张数≥1，点击栏位时校验：
            // （1）校验1：当前登录客户的【客户状态】

            CheckFlow().addChecker(notOpenAccountChecker, {
                // 若【客户状态≠正常/休眠】，则弹窗提示用户未开户，使用线上统一的开户提醒弹窗
                openAccountLogic.showOpenAccountDialog(this@FragUserCenter)
            }).addChecker(notSetTradePwd, actionNotSetTradePwd).onAllPass {
                // 若不为空，则跳转“银行卡列表页”
                val bundle = Bundle()
                bundle.putIntArray(
                    ValConfig.IT_INTERCEPT_CODE,
                    intArrayOf(IGlobalInterceptCode.GLOBAL_CHECK_TRADE_UNACTIVE_DLG)
                )
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    this,
                    "",
                    H5UrlKey.USER_BANK_LIST,
                    null,
                    bundle,
                    null
                )
            }
        }

        //实名信息
        binding.entryRealName.setOnClickListener {
            //（1）若【客户状态=正常/休眠】，跳转<实名信息页>
            //（2）若【客户状态≠正常/休眠】，则显示开户引导弹窗：
            CheckFlow().addChecker(notOpenAccountChecker, {
                openAccountLogic.showOpenAccountDialog(this@FragUserCenter)
            }).onAllPass {
                //跳转<实名信息页>
                Invoker.getInstance()
                    .navigation(IWebProvider::class.java)
                    .launchWebView(this,
                        "",
                        H5UrlKey.REAL_USER_INFO,
                        null,
                        null)
            }
        }

        //风险等级
        //{等级名称}({等级代码})，举例：进取型(C5)
        binding.entryRiskLevel.rightText = apiUserInfo.riskLevel().emptyToNull()?.transformTo {
            if (apiUserInfo.riskToleranceExpire()) "已过期"
            else apiUserInfo().riskLevelTxt()
        }.orEmpty()
        binding.entryRiskLevel.setOnClickListener {
            HbAnalytics.onClick("610300")
            CheckFlow().addChecker(notOpenAccountChecker, {
                openAccountLogic.showOpenAccountDialog(this@FragUserCenter)
            }).addChecker({ apiUserInfo -> apiUserInfo.riskLevel().isNullOrEmpty() }, {
                //引导客户做风险测评（兜底，一般不会到这个场景）
                //风险等级无值，跳转至<风险测评引导页>
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    this,
                    "",
                    H5UrlKey.RISK_EVA_IDX,
                    hashMapOf(Pair("from", "person")),
                    null
                )
            }).onAllPass {
                //若有值，跳转<风险测评结果页面>根据风险测评是否已过期，跳转至不同的结果页（历史需求供参考：点击跳转）
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    this,
                    "",
                    H5UrlKey.RISK_EVA_RESULT,
                    hashMapOf(Pair("from", "person")),
                    null
                )
            }
        }

        //投资者类型
        //仅当客户已完成开户时，显示该项；否则隐藏
        VisibleGone.handle(binding.entryInvestorType, apiUserInfo.finishOpenAccount())
        if (apiUserInfo.finishOpenAccount()) {
            renderInvestor()
            binding.entryInvestorType.setOnClickListener {
                handleInvestorTypeClick()
            }
        }

        // 签名
        //（1）若用户未上传过纸质签名，则显示“去上传”，以及跳转icon
        //（2）若用户已上传纸质签名，则不显示“去上传”，仅显示跳转icon
        val uploadedSign = false //todo 若用户未上传过纸质签名，则显示“去上传”，以及跳转icon
        binding.entrySign.rightArrowVisible  = !uploadedSign
        binding.entrySign.rightText = if (uploadedSign) "" else "去上传"
        binding.entrySign.setOnClickListener {
            //TODO 2、交互：点击跳转“纸质签名”页
        }

        //申请资产证明
        //仅开户完成后展示
        VisibleGone.handle(binding.entryAsset, apiUserInfo.finishOpenAccount())
        binding.entryAsset.setOnClickListener {
            //TODO 点击跳转“申请资产证明”页
        }

        //衍生工具知识测评
        //（1）若用户未做过测评，则显示“去测评”，以及跳转icon
        //（2）若用户已测评，则不显示“去测评”，仅显示跳转icon
        binding.entryTest.setOnClickListener {
            // 交互：点击栏位时校验当前登录客户的【客户状态】
            // （1）若【客户状态=正常/休眠】，跳转<衍生工具知识测评>页
            // （2）若【客户状态≠正常/休眠】，则显示开户引导弹窗
            CheckFlow().addChecker(notOpenAccountChecker, {
                openAccountLogic.showOpenAccountDialog(this@FragUserCenter)
            }).onAllPass {
                // 跳转<衍生工具知识测评>页
                HbAnalytics.onClick("611860")
                //衍生工具知识评估 若用户已完成香港开户：点击入口，进入衍生工具知识评估页面 若用户未完成香港开户：点击入口，弹出开户引导弹窗：
                Invoker.getInstance()
                    .navigation(IWebProvider::class.java)
                    .launchWebView(it.context,
                        "",
                        H5UrlKey.DERIVATIVE_IDX,
                        null,
                        null)
            }
        }

        //持牌人
        //（1）若用户是持牌人，才显示该字段；否则隐藏该字段
        //（2）显示字段时仅显示跳转icon
        val isCpr = false //todo 是否是持牌人
        VisibleGone.handle(binding.entryCpr, isCpr)
        binding.entryCpr.setOnClickListener {
            //TODO 点击跳转“持牌人名片”页
        }
    }

    /**
     * 投资者资质
     * 仅当客户已完成开户时显示
     */
    private fun renderInvestor() {
        val investorType = apiUserInfo().investorType()
        if (TextUtils.equals("PRO", investorType)) {
            //专业投资者
            if (TextUtils.equals("1", apiUserInfo().investorAssetsEffectiveStatus())) {
                //过期状态处理
                binding.entryInvestorType.rightText = SpannableUtils.formatStr(
                    SpannableItem("专业"),
                    SpannableItem(" 已过期", ColorUtils.parseColor("#C91212"))
                )
                binding.entryInvestorType.isEnabled = true
                binding.entryInvestorType.rightArrowVisible = true
            } else {
                binding.entryInvestorType.rightText = "专业"
                binding.entryInvestorType.isEnabled = false
                binding.entryInvestorType.rightArrowVisible = false
            }
            return
        }
        if (TextUtils.equals("NORMAL", investorType)) {
            //普通投资者
            binding.entryInvestorType.rightText = SpannableUtils.formatStr(
                SpannableItem("普通"),
                SpannableItem(" 去认证", ColorUtils.parseColor("#5072D4"))
            )
            binding.entryInvestorType.rightArrowVisible = true
            binding.entryInvestorType.isEnabled = true
            return
        }

        binding.entryInvestorType.rightText = "--"
        binding.entryInvestorType.rightArrowVisible = false
        binding.entryInvestorType.isEnabled = false
    }

    private fun handleInvestorTypeClick() {
        if (TextUtils.equals("PRO", apiUserInfo().investorType())) {
            if (apiUserInfo().investorAuditStatus()) {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.PRO_INVESTORS_CHECK, null, null)
            } else {
                //专业投资者认证页(认证状态页)”（新增页面见下文说明）。
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.PRO_INVESTORS_STATUS, null, null)
            }
        } else {
            if (apiUserInfo().investorAuditStatus()) {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.PRO_INVESTORS_CHECK, null, null)
            } else {
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(this, "", H5UrlKey.PRO_INVESTORS_IDX, null, null)
            }
        }
    }
}

fun String?.emptyToNull(): String? {
    if(isNullOrEmpty()) return null
    return this
}

fun String.transformTo(transform: (String) -> String): String {
    return transform(this)
}


fun String?.or(defaultVal: String): String {
    if (isNullOrEmpty()) return defaultVal
    return this
}

/**校验项*/
typealias ItemChecker = (ApiUserInfo) -> Boolean

/**未开户的校验*/
val notOpenAccountChecker = { apiUserInfo: ApiUserInfo ->
    !apiUserInfo.finishOpenAccount()
}

/**未设置交易密码的校验*/
val notSetTradePwd = { apiUserInfo: ApiUserInfo ->
    !apiUserInfo.hasTradePwd()
}

val actionNotSetTradePwd = {
    //若为空，则toast提示“您已开户，请先前往安全中心设置交易密码”，不进入二级页面
    LogUtils.pop("您已开户，请先前往安全中心设置交易密码")
}

/**
 * 多分枝校验处理流程，按照添加的校验先后顺序执行，一旦满足，立即停止校验并执行对应的处理；
 * 否则，执行全部校验项，并执行全部校验项的actionPass
 */
class CheckFlow {
    private val todoList = mutableListOf<Pair<ItemChecker, () -> Unit>>()

    /**
     * 添加校验逻辑，以及处理响应
     */
    fun addChecker(checker: ItemChecker, actionIntercept: () ->  Unit): CheckFlow {
        todoList.add(Pair(checker, actionIntercept))
        return this
    }

    /**
     * 执行校验，并处理最终结果
     */
    fun onAllPass(actionPass: () -> Unit) {
        for (pair in todoList) {
            if (pair.first(apiUserInfo())) {
                pair.second()
                return
            }
        }
        actionPass()
    }
}