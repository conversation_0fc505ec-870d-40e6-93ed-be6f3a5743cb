package com.howbuy.global.user.message

import android.app.Activity
import android.content.Context
import android.text.TextUtils
import com.howbuy.account.UserDataHelper
import com.howbuy.account.remote.FetchAction
import com.howbuy.dialog.DlgHelper
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.h5.H5UrlKey
import com.howbuy.global.common.LiveDataEventKey
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.entity.MessageStatus
import com.howbuy.lib.utils.JsonUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker

/**
 * 消息跳转
 * <AUTHOR>
 * @Date 2024/7/10
 * @Version V2.2
 */
object MessageLauncher {

    /**
     * 消息跳转页面
     * message      消息
     * isMessage    是否消息、待办（消息点击就标为已读，待办需要等h5 js通知完成）
     * isMineClick  是否我的页面点击跳转，只有我的页面点击后某些场景需要刷新全局消息接口，消息列表页不刷新
     */
    fun launcherMsg(context: Context, message: MessageItem?, isMessage: Boolean, isMineClick: Boolean) {
        if (message == null) return
        if (isMessage && !TextUtils.equals("1", message.readStatus)) {
            //未读消息 已读
            UserRequest.readMessage(false, message.messageId)
        }
        //todoTask = 1 待办消息
        if (TextUtils.equals(message.todoTask, "1")) {
            launchToDoDetail(context, message, isMineClick)
        } else {
            launchMessageDetailH5(context, message)
        }
    }

    /**
     * 跳转待办详情
     */
    fun launchToDoDetail(context: Context, message: MessageItem?, isMineClick: Boolean) {
        if (message == null) return
        if (TextUtils.equals(message.todoStatus, "1")) {
            //待办已完成
            if (TextUtils.equals(message.jump, "1")) {
                launchMessageDetailH5(context, message)
            } else {
                LogUtils.pop("该业务已更新，无需处理！")
            }
            todoDone(message, isMineClick)
        } else {
            val dlgHelper = DlgHelper()
            dlgHelper.showDialog(context, DlgHelper.DlgArg("", true, true), 0)
            UserRequest.queryMessageExpire(message.messageId, 0) {
                if (context is Activity && context.isFinishing) {
                    return@queryMessageExpire
                }
                dlgHelper.closeDialog(context)
                var isExpire = false
                if (it.isSuccess && it.mData != null) {
                    val body = it.mData as MessageStatus
                    isExpire = TextUtils.equals("1", body.status)
                }
                if (isExpire) {
                    //表示待办已完成
                    if (TextUtils.equals(message.jump, "1")) {
                        launchMessageDetailH5(context, message)
                    } else {
                        LogUtils.pop("该业务已更新，无需处理！")
                    }
                    todoDone(message, isMineClick)
                } else {
                    launchMessageDetailH5(context, message)
                }
            }
        }
    }

    /**
     * 跳转H5消息详情页面
     */
    private fun launchMessageDetailH5(context: Context, message: MessageItem) {
        Invoker.getInstance().navigation(IWebProvider::class.java)
            .launchWebView(context, "", message.url ?: "", null) { _, resultData, _ ->
                val data = resultData?.getString(ValConfig.IT_ENTITY)
                val js = JsonUtils.getObject(data)
                var resultStates: String? = ""
                if (js != null) {
                    resultStates = JsonUtils.getString(js, "resultStates")
                }
                if (TextUtils.equals("1", resultStates)) {
                    //待办已完成
                    todoDone(message, false)
                }
            }
    }

    /**
     * 待办完成处理
     * 1. 刷新全局消息数据
     * 2. 通知消息列表本地数据更新(LiveDataBus)
     */
    private fun todoDone(message: MessageItem, needRefreshMessage: Boolean) {
        message.todoStatus = "1"
        if (needRefreshMessage) {
            UserDataHelper.scheduleRemoteFetch(DataIds.ID_USER_MESSAGE, FetchAction.dataChanged)
        }
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_MESSAGE_TODO_DONE, MessageItem::class.java).postValue(message)
    }

}