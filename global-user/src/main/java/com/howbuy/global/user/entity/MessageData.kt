package com.howbuy.global.user.entity

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import kotlinx.android.parcel.Parcelize

/**
 * 消息
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
@Parcelize
data class MessageData(
    var messageInfo: MessageItem?,//最近一条消息
    var unReadCount: String?,//未读消息数量
    var todoCount: String?,//已读消息数量
) : AbsNormalBody(), Parcelable, Cloneable {

    constructor() : this(null, null, null)

    public override fun clone(): MessageData {
        var cloned: MessageData
        try {
            cloned = super.clone() as MessageData
        } catch (e: CloneNotSupportedException) {
            cloned = MessageData()
            e.printStackTrace()
        }
        return cloned
    }
}

@Parcelize
data class MessageList(
    val unReadCount: String?,//未读消息总数
    val todoCount: String?,//待办消息总数
    val messageList: MutableList<MessageItem>?
) : AbsNormalBody(), Parcelable

@Parcelize
data class MessageStatus(
    val status: String?,//消息状态 0未过期 1已过期
    val jump: String?//消息是否可跳转 0否 1是
) : AbsNormalBody(), Parcelable

@Parcelize
data class MessageCategoryList(
    val unReadCount: String?,//未读消息总数
    val todoCount: String?,//待办消息总数
    val categoryList: MutableList<MessageCategory>?//消息分类列表
) : AbsNormalBody(), Parcelable

@Parcelize
data class MessageCategory(
    val categoryId: String?,//分类id
    val categoryName: String?,//分类名称
    var unReadCount: String?,//该分类未读数量
    val latestMessage: String?,//最新一条消息
    val lastUpdateTime: String?,//最后更新时间 yyyyMMdd
) : Parcelable, Cloneable {

    constructor() : this(null, null, null, null, null)

    public override fun clone(): MessageCategory {
        var cloned: MessageCategory
        try {
            cloned = super.clone() as MessageCategory
        } catch (e: CloneNotSupportedException) {
            cloned = MessageCategory()
            e.printStackTrace()
        }
        return cloned
    }
}

@Parcelize
data class MessageItem(
    val messageId: String?,//消息id
    val businessType: String?,//业务类型 1海外储蓄罐自动续期提醒,2海外储蓄罐底层基金更换通知,3资产证明到期提醒,4MQ年度检视函(包含风测)
    val messageTitle: String?,// 消息标题
    val messageDesc: String?,// 消息摘要
    val messageDetail: String?,// 消息详情
    val messageDate: String?,// 消息时间 yyyyMMdd
    var readStatus: String?,// 是否已读 0否1是
    val todoTask: String?,//是否待办任务 0否1是
    var todoStatus: String?,// 待办是否完成 0否1是
    val jump: String?,//消息是否可跳转 0否 1是
    val url: String?,//跳转h5链接
    val categoryId: String?,//所属分类id
) : Parcelable, Cloneable {

    constructor() : this(null, null, null, null, null, null, null, null, null,null, null, null)

    public override fun clone(): MessageItem {
        var cloned: MessageItem
        try {
            cloned = super.clone() as MessageItem
        } catch (e: CloneNotSupportedException) {
            cloned = MessageItem()
            e.printStackTrace()
        }
        return cloned
    }
}
