package com.howbuy.global.user.ad

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.AbsBody
import com.howbuy.fund.net.entity.common.HeaderInfo
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import kotlinx.android.parcel.Parcelize

/**
 * @Description 广告位
 * <AUTHOR>
 * @Date 2024/2/29
 * @Version V1.0
 */
@Parcelize
data class AdResult(
    val advertisings: List<AdItem>?,
) : AbsNormalBody(), Parcelable {
    constructor(headerInfo: HeaderInfo) : this(null)
}

@Parcelize
data class AdItem(
    val adTitle: String?,//标题
    val adOrder: String?,//排序  默认升序，服务端排序
    val adImg: String?,//图片地址
    val onClick: String?,//跳转链接 广告URL
    val verifyLogin: String?,//是否校验登录 枚举包括 0-无需登录/1-需登录
    val verifyAccount: String?,//用于判断是否校验开户，枚举包括 0-无需开户/1-需开户；
) : Parcelable
