package com.howbuy.global.user.entity;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * class description.
 *
 * <AUTHOR>
 * @date 2018/5/18
 */
public class SmHoldItem implements Parcelable {

    private String fundCode;
    private String fundName;

    protected SmHoldItem(Parcel in) {
        fundCode = in.readString();
        fundName = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(fundCode);
        dest.writeString(fundName);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SmHoldItem> CREATOR = new Creator<SmHoldItem>() {
        @Override
        public SmHoldItem createFromParcel(Parcel in) {
            return new SmHoldItem(in);
        }

        @Override
        public SmHoldItem[] newArray(int size) {
            return new SmHoldItem[size];
        }
    };

    public String getFundCode() {
        return fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public SmHoldItem() {
    }

}
