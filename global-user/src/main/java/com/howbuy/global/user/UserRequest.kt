package com.howbuy.global.user

import android.text.TextUtils
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.net.HttpCaller
import com.howbuy.fund.net.cache.CacheMode
import com.howbuy.fund.net.entity.common.SimpleDto
import com.howbuy.fund.net.entity.common.normal.CommonDto
import com.howbuy.fund.net.http.ReqParams
import com.howbuy.fund.net.http.RequestContentType
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.net.util.XUtils
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.apiUserMessage
import com.howbuy.global.user.ad.AdResult
import com.howbuy.global.user.amtdesc.ExcRateBody
import com.howbuy.global.user.amtdesc.QxnProdBody
import com.howbuy.global.user.announcement.AnnouncementResult
import com.howbuy.global.user.entity.LoginExpiredStatusBody
import com.howbuy.global.user.entity.LoginRecordBody
import com.howbuy.global.user.entity.MessageCategoryList
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.entity.MessageStatus
import com.howbuy.global.user.feedback.SmTgInfo
import java.lang.reflect.Type
import kotlin.math.max

/**
 * @Description 用户模块接口请求
 * <AUTHOR>
 * @Date 2024/2/23
 * @Version V1.0
 */
object UserRequest {

    /**公告*/
    const val HK_V200_BASE_GETANNOUNCEMENT = "HK_V200_BASE_GETANNOUNCEMENT"

    /**广告位*/
    const val HK_V200_BASE_GETBANNER = "HK_V200_BASE_GETBANNER"

    /**用户信息*/
    const val CRM_CGI_HKACCOUNT_CUST_APP_GETPERSONALCENTERINFO = "CRM_CGI_HKACCOUNT_CUST_APP_GETPERSONALCENTERINFO"

    /**资产*/
    const val CRM_CGI_HKACCOUNT_BALANCE_QUERY = "CRM_CGI_HKACCOUNT_BALANCE_QUERY"

    /**保存资产小眼睛状态*/
    const val CRM_CGI_HKACCOUNT_COMMON_SAVEASSETSTATUS = "CRM_CGI_HKACCOUNT_COMMON_SAVEASSETSTATUS"

    /**退出登录*/
    const val CRM_CGI_HKACCOUNT_LOGIN_APPLOGOUT = "CRM_CGI_HKACCOUNT_LOGIN_APPLOGOUT"

    /**验证登录密码*/
    const val CRM_CGI_HKACCOUNT_PASSWORD_LOGINVERIFY = "CRM_CGI_HKACCOUNT_PASSWORD_LOGINVERIFY"

    /**登录记录*/
    const val CRM_CGI_HKACCOUNT_LOGIN_LOGLIST = "CRM_CGI_HKACCOUNT_LOGIN_LOGLIST"

    /**查询是否需要退出登录*/
    const val CRM_CGI_HKACCOUNT_LOGIN_NEEDLOGINEXIT = "CRM_CGI_HKACCOUNT_LOGIN_NEEDLOGINEXIT"

    /**消息数量+最近一条消息*/
    const val HK_V220_MESSAGEWINDOW = "HK_V220_MESSAGEWINDOW"

    /**TODO wy 消息分类列表*/
    const val HK_V300_MESSAGECATEGORYLIST = "https://m1.apifoxmock.com/m1/2829913-1212853-default/HK_V300_MESSAGECATEGORYLIST"

    /**消息列表*/
    const val HK_V220_MESSAGELIST = "HK_V220_MESSAGELIST"

    /**消息-已读*/
    const val HK_V220_DEALSTATUS = "HK_V220_DEALSTATUS"

    /**消息状态-是否已过期*/
    const val HK_V220_QUERYMESSAGESTAUTS = "HK_V220_QUERYMESSAGESTAUTS"
    /**获取资产-弹框 -基金持仓中 ,是否存在产品在”千禧年类产品配置表“*/
    const val CRM_CGI_HKACCOUNT_BALANCE_FUNDTAB_QUERY = "CRM_CGI_HKACCOUNT_BALANCE_FUNDTAB_QUERY"
    /**获取资产-弹框 -现金余额 汇率 信息查询“*/
    const val CRM_CGI_HKACCOUNT_BALANCE_CASHTAB_QUERY = "CRM_CGI_HKACCOUNT_BALANCE_CASHTAB_QUERY"

    /**
     * 消息是否过期
     */
    fun queryMessageExpire(messageId: String?, handType: Int, callback: IReqNetFinished) {
        if (TextUtils.isEmpty(messageId)) return
        HttpCaller.getInstance().requestNormal(
            HK_V220_QUERYMESSAGESTAUTS, MessageStatus::class.java, true, null,
            handType, callback, "hkCustNo", apiUserInfo().getHkCustNo(), "messageId", messageId
        )
    }

    /**
     * 消息分类列表
     */
    fun queryMessageCategoryList(handType: Int, callback: IReqNetFinished) {
        HttpCaller.getInstance().requestNormal(
            HK_V300_MESSAGECATEGORYLIST, MessageCategoryList::class.java, true, null,
            handType, callback, "hkCustNo", apiUserInfo().getHkCustNo()
        )
    }

    /**
     * 消息列表
     * type         消息类型 0全部 1待办
     * needCount    是否需要未读消息数 0不需要 1需要
     * id           最好一条消息id，传空就获取最新10条，用于分页
     * pageSize     每页大小（默认每页10条）
     */
    fun queryMessageList(type: String, needCount: Boolean, id: String?, pageSize: String, handType: Int, callback: IReqNetFinished) {
        HttpCaller.getInstance().requestNormal(
            HK_V220_MESSAGELIST, MessageList::class.java, true, null,
            handType, callback, "hkCustNo", apiUserInfo().getHkCustNo(), "type", type,
            "needCount", if (needCount) "1" else "0", "id", id, "pageSize", pageSize
        )
    }

    /**
     * 消息已读
     * isAll 是否全部已读
     * messageId 单条已读的消息id
     */
    fun readMessage(isAll: Boolean, messageId: String? = null) {
        HttpCaller.getInstance().requestNormal(
            HK_V220_DEALSTATUS, CommonDto::class.java, true, null,
            0, {}, "hkCustNo", apiUserInfo().getHkCustNo(), "type", if (isAll) "0" else "1",
            "messageId", messageId
        )
        if (isAll) {
            apiUserMessage().updateUnReadCount(0)
        } else {
            val count = apiUserMessage().unReadCount()
            apiUserMessage().updateUnReadCount(max(count - 1, 0))
        }
    }

    /**持仓列表*/
    const val CRM_CGI_FUND_HOLD_QUERY_FUND_LIST = "CRM_CGI_FUND_HOLD_QUERY_FUND_LIST"

    /**
     * 公告
     * 多个|分割
     * position： 1:个人中心公告位
     */
    fun reqAnnouncement(position: String?, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["position"] = position

        HttpCaller.getInstance().requestNormal(
            HK_V200_BASE_GETANNOUNCEMENT,
            AnnouncementResult::class.java,
            true,
            null,
            handType,
            callback,
            "hkCustNo",
            apiUserInfo().getHkCustNo(),
            "position",
            position
        )
    }

    /**
     * 广告位
     * position： 1:个人中心广告位
     */
    fun queryBanner(position: String?, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["position"] = position.orEmpty()

        HttpCaller.getInstance().requestNormal(
            HK_V200_BASE_GETBANNER,
            AdResult::class.java,
            true,
            null,
            handType,
            callback,
            "hkCustNo",
            apiUserInfo().getHkCustNo(),
            "position",
            position
        )
    }

    /**
     * 设置资产小眼睛状态
     */
    fun saveShowUserAssetsAmt(isShow: Boolean, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["showAsset"] = if (isShow) "0" else "1"
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_COMMON_SAVEASSETSTATUS, SimpleDto::class.java, true, null, true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取资产-弹框 -基金持仓中 ,
     * 是否存在产品在”千禧年类产品配置表“，且该产品【待投金额】＞0的产品列表数据
     * @param currency 币种 156 : 人民币  840-美元 344-港元 978-欧元 392-日元 826-英镑
     */
    fun reqFundQxnProdList(currency: String, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["currency"] = currency
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_BALANCE_FUNDTAB_QUERY,
            QxnProdBody::class.java, true, null, true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 获取资产-弹框 -现金余额 汇率 信息查询 ,
     * @param currency 币种 156 : 人民币  840-美元 344-港元 978-欧元 392-日元 826-英镑
     */
    fun reqCashExcRateInfoList(currency: String, handType: Int, callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["currency"] = currency
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_BALANCE_CASHTAB_QUERY,
            ExcRateBody::class.java, true, null, true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 退出登录
     */
    fun logout(handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_LOGIN_APPLOGOUT, SimpleDto::class.java, true, null, true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 验证登录密码
     * 验证失败返回异常码-C011131
     * 验证成功返回异常码-C010000
     */
    fun validateLoginPwd(password: String, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["password"] = password
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_PASSWORD_LOGINVERIFY, SimpleDto::class.java,
            true, null, true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 登录记录
     */
    fun reqUserLoginRecord(pageSize: String, pageNo: String, handType: Int, callback: IReqNetFinished?) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        map["size"] = pageSize
        map["page"] = pageNo
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_LOGIN_LOGLIST,
            LoginRecordBody::class.java,
            true, null, true, handType, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 查询是否需要退出app
     */
    fun fetchLoginExpiredStatus(callback: IReqNetFinished) {
        val map = HashMap<String, Any?>()
        map["hkCustNo"] = apiUserInfo().getHkCustNo()
        val params = createTradeReqParams(
            CRM_CGI_HKACCOUNT_LOGIN_NEEDLOGINEXIT,
            LoginExpiredStatusBody::class.java,
            true, null, true, 0, callback, map
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 投顾或者客服信息
     */
    fun reqSmTgInfo(pageId: String?, hbSceneId: String?, handType: Int, callback: IReqNetFinished?) {
        HttpCaller.getInstance().requestNormal(
            "https://m1.apifoxmock.com/m1/2829913-1212853-default/simu/v776/simuWorkWechatAd.json",
            SmTgInfo::class.java,
            false,
            null,
            handType,
            callback,
            "hkCustNo",
            apiUserInfo().getHkCustNo(),
            ValConfig.pageIdName,
            pageId,
            ValConfig.sceneIdName,
            hbSceneId
        )
    }

    /**
     * 创建请求参数对象
     */
    fun createTradeReqParams(
        uri: String?,
        clazz: Type?,
        post: Boolean,
        cacheMode: CacheMode?,
        tradeParseMode: Boolean,
        handType: Int,
        callback: IReqNetFinished?,
        paramsMap: HashMap<String, Any?>?
    ): ReqParams {
        val url = XUtils.getUrl(uri)
        val safePolicy = XUtils.getSafePolicy(uri)
        val encrypt = XUtils.isNeedEncryption(uri)
        val needEnvelope = XUtils.isNeedEnvelope(uri)
        val needSign = XUtils.isNeedSign(uri)
        val reqParams = ReqParams()
        reqParams.url = url
        reqParams.uriKey = uri
        reqParams.cls = clazz
        reqParams.isPost = post
        reqParams.reqTag = uri
        reqParams.isTradeParseMode = tradeParseMode
        //固定拼接公共参数
        reqParams.needPublicParams = true
        reqParams.safePolicy = safePolicy
        //post by json 格式
        reqParams.requestContentType = RequestContentType.JSON
        reqParams.isEncrypt = encrypt
        reqParams.cacheMode = cacheMode
        reqParams.params = paramsMap
        reqParams.bytes = null
        reqParams.handType = handType
        reqParams.reqNetFinished = callback
        reqParams.startTime = System.currentTimeMillis()
        reqParams.isNeedEnvelope = needEnvelope
        reqParams.isNeedSign = needSign
        return reqParams
    }
}