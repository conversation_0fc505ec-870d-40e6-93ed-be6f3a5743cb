package com.howbuy.fund.base.vm

import android.app.Application
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.View
import androidx.annotation.CallSuper
import androidx.annotation.MainThread
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LifecycleOwner
import com.howbuy.dialog.DlgHelper
import com.howbuy.fund.base.utils.SingleLiveEvent
import com.howbuy.fund.base.vm.entity.EmptyViewEntity
import com.howbuy.fund.base.vm.entity.LoadingDialogEntity
import com.howbuy.fund.base.vm.entity.RouterFragEntity
import com.howbuy.fund.base.vm.entity.StartActivityEntity
import com.howbuy.fund.base.vm.function.*
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.*
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

open class BaseViewModel<M : BaseModel>(app: Application) : AndroidViewModel(app), IViewModel,
        IProgress, IRouterFrag, IEmptyView, IFunctions, IFragment {

    lateinit var mModel: M//可能存在没有仓库的 vm，但这里也不要是可 null 的。如果 vm 没有提供仓库，说明此变量不可用，还去使用的话自然就报错。

    private var isAutoCreateRepo = true//是否自动创建仓库，默认是 true

    protected open fun isCacheRepo() = true//是否缓存自动创建的仓库，默认是 true

    protected lateinit var mCoroutineScope: CoroutineScope

    private lateinit var mCompositeDisposable: CompositeDisposable

    val uiChangeLiveData: UIChangeLiveData by lazy { UIChangeLiveData() }


    constructor(app: Application, model: M) : this(app) {
        isAutoCreateRepo = false
        mModel = model
    }

    @CallSuper
    override fun onCreate(owner: LifecycleOwner) {
        if (isAutoCreateRepo) {
            if (!this::mModel.isInitialized) {
                val modelClass: Class<M>?
                val type: Type? = javaClass.genericSuperclass
                modelClass = if (type is ParameterizedType) {
                    @Suppress("UNCHECKED_CAST")
                    type.actualTypeArguments[0] as? Class<M>
                } else null
                if (modelClass != null && modelClass != BaseModel::class.java) {
                    mModel = RepositoryManager.getInstance().getRepo(modelClass, isCacheRepo())
                }
            }
        }
    }

    /**
     * 发起协程，让协程和 UI 相关
     */
    fun launchUI(block: suspend CoroutineScope.() -> Unit) {
        initCoroutineScope()
        mCoroutineScope.launch { block() }
    }

    private fun initCoroutineScope() {
        if (!this::mCoroutineScope.isInitialized) {
            mCoroutineScope = CoroutineScope(Job() + Dispatchers.Main)
        }
    }

    /**
     * 给 Rx 使用的，如果项目中有使用到 Rx 异步相关的，在订阅时需要把订阅管理起来。
     * 通常异步操作都是在 vm 中进行的，管理起来的目的是让异步操作在界面销毁时也一起销毁，避免造成内存泄露
     */
    fun addSubscribe(disposable: Disposable) {
        if (disposable.isDisposed) {
            return
        }
        if (!this::mCompositeDisposable.isInitialized) {
            mCompositeDisposable = CompositeDisposable()
        }
        mCompositeDisposable.add(disposable)
    }

    @CallSuper
    override fun onCleared() {
        // 可能 mModel 是未初始化的
        if (this::mModel.isInitialized) {
            mModel.onCleared()
        }

        cancelAsyncTask()
    }

    /**
     * 取消耗时任务，比如在界面销毁时，或者在对话框消失时
     */
    private fun cancelAsyncTask() {
        // ViewModel销毁时会执行，同时取消所有异步任务
        if (this::mCompositeDisposable.isInitialized) {
            mCompositeDisposable.clear()
        }
        if (this::mCoroutineScope.isInitialized) {
            mCoroutineScope.cancel()
        }
    }

    @MainThread
    override fun showProgress() {
        uiChangeLiveData.showProgressEvent?.value = null
    }

    @MainThread
    override fun hideProgress() {
        uiChangeLiveData.hideProgressEvent?.call()
    }

    @MainThread
    override fun showLoadingDialog(msg: String?, backCancelable: Boolean, touchCancelable: Boolean) {
        uiChangeLiveData.showLoadingDialogEvent?.value = LoadingDialogEntity(msg, backCancelable, touchCancelable)
    }

    @MainThread
    override fun dismissLoadingDialog() {
        uiChangeLiveData.dismissLoadingDialogEvent?.call()
    }

    @MainThread
    override fun launchFrag(atyPath: String, fragPath: String, bundle: Bundle?, atyFlag: Int, requestCode: Int) {
        uiChangeLiveData.routerFragEvent?.value = RouterFragEntity(atyPath, fragPath, bundle, atyFlag, requestCode)
    }

    @MainThread
    override fun showEmptyView() {
        uiChangeLiveData.showEmptyViewEvent?.value = null
    }

    @MainThread
    override fun showEmptyView(text: String?, drawable: Drawable?, drawablePosition: Int, marginTop: Int) {
        uiChangeLiveData.showEmptyViewEvent?.value = EmptyViewEntity(text, drawable, drawablePosition, marginTop, null)
    }

    @MainThread
    override fun showEmptyView(view: View?) {
        uiChangeLiveData.showEmptyViewEvent?.value = EmptyViewEntity(view)
    }

    @MainThread
    override fun hideEmptyView() {
        uiChangeLiveData.hideEmptyViewEvent?.call()
    }

    @MainThread
    override fun showDialog(arg: DlgHelper.DlgArg, dialogId: Int, callback: DlgHelper.IDlgHelper) {
        uiChangeLiveData.dialogEvent?.value = Triple(dialogId, arg, callback)
    }

    @MainThread
    override fun finish() {
        uiChangeLiveData.finishEvent?.call()
    }

    @MainThread
    override fun onBackPressed() {
        uiChangeLiveData.onBackPressedEvent?.call()
    }

    @MainThread
    override fun setToolbarTitle(title: String) {
        uiChangeLiveData.changeTitleEvent?.value = title
    }

    @MainThread
    override fun startActivity(intent: Intent, requestCode: Int, options: Bundle?) {
        uiChangeLiveData.startActivityEvent?.value = StartActivityEntity(intent, requestCode, options)
    }


    class UIChangeLiveData : SingleLiveEvent<Any?>() {
        var showProgressEvent: SingleLiveEvent<Void>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var hideProgressEvent: SingleLiveEvent<Void>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var showLoadingDialogEvent: SingleLiveEvent<LoadingDialogEntity>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var dismissLoadingDialogEvent: SingleLiveEvent<Void>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var dialogEvent: SingleLiveEvent<Triple<Int, DlgHelper.DlgArg, DlgHelper.IDlgHelper>>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var routerFragEvent: SingleLiveEvent<RouterFragEntity>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var showEmptyViewEvent: SingleLiveEvent<EmptyViewEntity?>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var hideEmptyViewEvent: SingleLiveEvent<Void>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var finishEvent: SingleLiveEvent<Void>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var onBackPressedEvent: SingleLiveEvent<Void>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var changeTitleEvent: SingleLiveEvent<String>? = null
            get() = createLiveData(field).also { field = it }
            private set
        var startActivityEvent: SingleLiveEvent<StartActivityEntity>? = null
            get() = createLiveData(field).also { field = it }
            private set

        private fun <T> createLiveData(liveData1: SingleLiveEvent<T>?): SingleLiveEvent<T> {
            var liveData = liveData1
            if (liveData == null) {
                liveData = SingleLiveEvent()
            }
            return liveData
        }
    }

}