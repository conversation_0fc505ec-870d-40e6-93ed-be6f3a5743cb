package com.howbuy.fund.base.arch;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProviders;

import java.lang.ref.SoftReference;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

public abstract class AutoReleaseObserver<T> implements Observer<T>{
    private static final String TAG = "AutoReleaseObserver";

    private SoftReference<ClearViewModel> viewModelRef;
    public AutoReleaseObserver(ClearViewModel viewModel) {
        initViewModel(viewModel);
    }

    public AutoReleaseObserver(Fragment fragment, @NonNull Class<? extends ClearViewModel> clearVM) {
        initViewModel(ViewModelProviders.of(fragment).get(clearVM));
    }

    public AutoReleaseObserver(FragmentActivity fragmentActivity, @NonNull Class<? extends ClearViewModel> clearVM) {
        initViewModel(ViewModelProviders.of(fragmentActivity).get(clearVM));
    }

    private void initViewModel(ClearViewModel viewModel){
        viewModelRef = new SoftReference<>(viewModel);
    }

    @Override
    public void onSubscribe(Disposable d) {
        if (viewModelRef.get() != null) {
            viewModelRef.get().accept(d);
        }
    }

    @Override
    public void onComplete() {
    }
}
