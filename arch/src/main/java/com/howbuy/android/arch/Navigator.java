package com.howbuy.android.arch;

import android.app.Activity;
import android.content.Intent;

import androidx.annotation.UiThread;

/**
 * 页面跳转封装，可以直接以回调方式处理onActivityResult
 */
public interface Navigator {
    /**
     * 生产一个requestCode
     */
    int generateRequestCode();

    /**
     * 设置跳转发七点
     */
    Navigator from(Activity activity);

    /**
     * 设置跳转的目标页面
     */
    Navigator to(Intent target);

    /**
     * 设置从目标页面返回后的结果回调(处理[Activity.onActivityResult]的相关逻辑)
     */
    Navigator listenResult(ResultListener resultConsumer);

    /**
     * 发起跳转，该方法必须在[from]和[to]调用之后再执行，否则将抛异常[RuntimeException]
     */
    @UiThread
    void execute();
}
