static String parseVersion(String repository) {
    return repository.split(":").last()
}
static String parseGroup(String repository) {
    return repository.split(":").first()
}

allprojects {
    Map<String, String> repositoryMap = rootProject.ext.componentExternalLibs

    //模块最终版本
    Map<String, String> versionMap = new HashMap<>()
    //使用源码的库ID与模块Map
    Map<String, String> sourceRepositoryMap = new HashMap<>()
    repositoryMap.entrySet().forEach{ entry ->
        String key = entry.key
        String group = parseGroup(entry.value)
        String version = parseVersion(entry.value)
        versionMap.put(group + ":" + key, version)
    }
    /**保存使用源码编译的模块*/
    String[] sourceModules = rootProject.ext.depUtil.getSourceModules()
    if (null != sourceModules && sourceModules.length > 0) {
        for (String project : sourceModules) {
            if (project != null && project.length() > 0) {
                String repository = repositoryMap.get(project)
                if (repository != null) {
                    String group = parseGroup(repository)
                    sourceRepositoryMap.put(group+":"+project, project)
                }
            }
        }
    }

    configurations.all {
        resolutionStrategy {
            if (rootProject.ext.allSourceBuild != true) {
                //对齐组件的版本
                resolutionStrategy.eachDependency { details ->
                    String prefix = details.requested.group + ":" + details.requested.name
                    String version = versionMap.get(prefix)
                    String sourceProjectName = sourceRepositoryMap.get(prefix)
                    if (version != null && null == sourceProjectName) {
//                        println("$prefix use version:$version")
                        details.useVersion version
                    }
                }
            }
        }

        if (rootProject.ext.allSourceBuild != true) {
            // 主要用于处理处理以下场景:
            // 某个module A被其他module B依赖，
            // 但是A在开发中要使用源码编译，B不需要使用源码编译。
            // 这个时候，需要将B对A的依赖强制替换为A的源码project
            resolutionStrategy.dependencySubstitution {
                all { DependencySubstitution dependency ->
                    if (dependency.requested instanceof ModuleComponentSelector) {
                        def mavenRepositoryId = "${dependency.requested.group}:${dependency.requested.module}" //{group}:{name}
                        //匹配源码project与maven库的逻辑：
                        def projectMatch = rootProject.allprojects.find { p ->
                            //匹配project，并且project需要使用源码编译
                            p.group == dependency.requested.group && p.name == dependency.requested.module && null != sourceRepositoryMap[mavenRepositoryId]
                        }
                        if (null != projectMatch) {
                            println("replace maven item:'${mavenRepositoryId}:${dependency.requested.version}' with project(\'${projectMatch.path}\')")
                            dependency.useTarget(projectMatch, 'selected local project')
                        }
                    }
                }
            }
        }
    }
}