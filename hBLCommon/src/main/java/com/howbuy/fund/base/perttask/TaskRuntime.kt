package com.howbuy.fund.base.perttask

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import com.howbuy.fund.base.perttask.util.PertTaskUtils.compareTask
import com.howbuy.fund.base.perttask.task.Task
import com.howbuy.fund.base.perttask.task.TaskRuntimeInfo
import com.howbuy.lib.utils.LogUtils

import java.util.*
import java.util.concurrent.*

/**
 * class description.
 * application 锚点管理
 * application UIThreadTask 运行管理
 * 调试配置
 * 线程池配置
 * 运行时 Task 信息收集
 * <AUTHOR>
 * @date 2022/2/24
 */
class TaskRuntime(executor: ExecutorService? = null) {

    //线程池，支持自定义
    private val pool: TaskThreadPool = TaskThreadPool(executor)
    //添加锁机制
    private val objTask = Object()
    private val objAnchorTasks = Object()
    private val obj3 = Object()

    //如果存在锚点任务，则同步的任务都所有锚点任务都完成前，在 UIThread 上运行
    //ps: 后续解除锚点之后，所有UI线程上的 Task 都通过 handle 发送执行，不保证业务逻辑的同步。
    @Volatile
    var runBlockTask: MutableList<Task> = mutableListOf()

    //设置锚点任务，当且仅当所有锚点任务都完成时, application 不在阻塞 UIThread
    @Volatile
    internal var anchorTaskIds: MutableSet<String> = mutableSetOf()

    //所有 task 运行时信息
    private val runtimeInfo: MutableMap<String, TaskRuntimeInfo> = HashMap()
    internal var debuggable = false
    internal val handler = Handler(Looper.getMainLooper())

    //Task 比较逻辑
    internal val taskComparator: Comparator<Task> = Comparator { lhs, rhs -> compareTask(lhs, rhs) }

    /**
     * 重置/清除资源
     */
    internal fun clear() {
        debuggable = false
        anchorTaskIds.clear()
        runBlockTask.clear()
        runtimeInfo.clear()
    }

    /**
     * 添加多个钩子的Task
     */
    internal fun addAnchorTasks(ids: Set<String>) {
        synchronized(objAnchorTasks) {
            if (ids.isNotEmpty()) {
                anchorTaskIds.addAll(ids)
            }
        }
    }

    /**
     * 移除钩子
     */
    internal fun removeAnchorTask(id: String) {
        synchronized(objAnchorTasks) {
            if (!TextUtils.isEmpty(id)) {
                anchorTaskIds.remove(id)
                synchronized(obj3) {
                    obj3.notify()
                }
            }
        }
    }

    /**
     * 判断是否设置了钩子
     */
    internal fun hasAnchorTasks(): Boolean {
        synchronized(objAnchorTasks) {
            return anchorTaskIds.isNotEmpty()
        }
    }

    /**
     * 添加阻塞的Task
     */
    private fun addRunTasks(task: Task) {
        synchronized(objTask) {
            if (!runBlockTask.contains(task)) {
                runBlockTask.add(task)
                synchronized(obj3) {
                    obj3.notify()
                }
            }
        }
    }

    /**
     * 执行阻塞的Task
     */
    internal fun tryRunBlockTask() {
        while (hasAnchorTasks()) {
            synchronized(obj3) {
                if (runBlockTask.isEmpty()) {
                    obj3.wait()
                }
            }
            while (runBlockTask.isNotEmpty()) {
                synchronized(objTask) {
                    if (runBlockTask.isNotEmpty()) {
                        Collections.sort(runBlockTask, taskComparator)
                        runBlockTask.removeAt(0).let {
                            if (hasAnchorTasks()) {
                                it.run()
                            } else {
                                handler.post(it)
                                for (blockItem in runBlockTask) {
                                    handler.post(blockItem)
                                }
                                runBlockTask.clear()
                            }
                        }
                    }
                }
            }
        }
    }

    private fun hasTaskRuntimeInfo(taskId: String): Boolean {
        return runtimeInfo[taskId] != null
    }

    internal fun getTaskRuntimeInfo(taskId: String): TaskRuntimeInfo? {
        return runtimeInfo[taskId]
    }

    internal fun setThreadName(task: Task, threadName: String) {
        val taskRuntimeInfo = runtimeInfo[task.id]
        if (taskRuntimeInfo != null) {
            taskRuntimeInfo.threadName = threadName
        }
    }

    internal fun setStateInfo(task: Task) {
        val taskRuntimeInfo = runtimeInfo[task.id]
        taskRuntimeInfo?.setStateTime(task.state, System.currentTimeMillis())
    }

    /**
     * 执行已添加的Task任务
     */
    internal fun executeTask(task: Task) {
        if (task.isAsyncTask) {
            pool.getExecutorService().execute(task)
        } else {
            if (!hasAnchorTasks()) {
                handler.post(task)
            } else {
                addRunTasks(task)
            }
        }
    }

    /**
     * 遍历依赖树并完成启动前的初始化
     * 1.获取依赖树最大深度
     * 2.遍历初始化运行时数据并打印log
     * 3.如果锚点不存在，则移除
     * 4.提升锚点链的优先级
     *
     * @param task
     */
    internal fun traversalDependenciesAndInit(task: Task) {
        val traversalVisitor: LinkedHashSet<Task> = linkedSetOf()
        traversalVisitor.add(task)
        traversalDependenciesAndInit(task, traversalVisitor)
        val iterator = anchorTaskIds.iterator()
        while (iterator.hasNext()) {
            val taskId = iterator.next()
            if (!hasTaskRuntimeInfo(taskId)) {
                if (debuggable) {
                    Log.d("AnchorsRuntime", "anchor $taskId no found !")
                }
                iterator.remove()
            } else {
                val info = getTaskRuntimeInfo(taskId)
                traversalMaxTaskPriority(info?.task)
            }
        }
    }

    /**
     * 递归算法
     * 遍历依赖树，初始化任务，并记录log
     * 如果单条依赖线上存在重复依赖将抛出异常（会造成依赖回环）
     */
    private fun traversalDependenciesAndInit(task: Task, traversalVisitor: LinkedHashSet<Task>) {
        task.bindRuntime(this)
        val taskRuntimeInfo = getTaskRuntimeInfo(task.id)
        if (taskRuntimeInfo == null) {
            // 如果没有初始化则初始化runtimeInfo
            val info = TaskRuntimeInfo(task)
            //标记当前Task是否设置为钩子
            if (anchorTaskIds.contains(task.id)) {
                info.isAnchor = true
            }
            runtimeInfo[task.id] = info
        } else {
            if (!taskRuntimeInfo.isTaskInfo(task)) {
                Log.e("Anchors",
                    "Multiple different tasks are not allowed to contain the same id (${task.id})!")
            }
        }

        for (nextTask in task.behindTasks) {
            if (!traversalVisitor.contains(nextTask)) {
                traversalVisitor.add(nextTask)
            } else {
                Log.e("Anchors",
                    "Do not allow dependency graphs to have a loopback！Related task'id is ${task.id} !")
            }

            if (debuggable && nextTask.behindTasks.isEmpty()) {
                val iterator = traversalVisitor.iterator()
                val builder = StringBuilder()
                while (iterator.hasNext()) {
                    builder.append(iterator.next().id)
                    builder.append("\n")
                }
                Log.d("AnchorsRuntime", builder.toString())
            }

            traversalDependenciesAndInit(nextTask, traversalVisitor)

            traversalVisitor.remove(nextTask)
        }
    }

    /**
     * 递归向上设置优先级
     *
     * @param task
     */
    private fun traversalMaxTaskPriority(task: Task?) {
        if (task == null) {
            return
        }
        task.priority = Int.MAX_VALUE
        for (dependence in task.dependTasks) {
            traversalMaxTaskPriority(dependence)
        }
    }
}