package com.howbuy.fund.base.perttask.task.lock

import android.os.Handler

/**
 * class description.
 * 管理 LockTask,
 * 它提供了lock()和unlock(), 其实就是利用 synchronized(obj)中的obj对象的 wait()和notify()方法机制实现的
 * <AUTHOR>
 * @date 2022/2/24
 */
class LockableAnchor internal constructor(private val handler: <PERSON><PERSON>) {
    private var lockListener: LockListener? = null
    var lockId: String? = null
        private set
    private val mLockObject = Object()
    private var releaseListeners = mutableListOf<ReleaseListener>()
    private var unlock = false

    /**
     * 外部监听上锁时机，在监听到上锁之后处理业务逻辑，并完成解锁或者破坏锁行为。
     * @param lockListener
     */
    fun setLockListener(lockListener: LockListener) {
        this.lockListener = lockListener
    }

    internal fun addReleaseListener(releaseListener: ReleaseListener) {
        if (!releaseListeners.contains(releaseListener)) {
            releaseListeners.add(releaseListener)
        }
    }

    internal fun setTargetTaskId(id: String?) {
        lockId = id
    }

    internal fun successToUnlock(): <PERSON><PERSON><PERSON> {
        return unlock
    }

    /**
     * 解锁，任务链后续任务继续执行
     */
    fun unlock() {
        handler.post {
            unlockInner()
        }
    }

    @Synchronized
    internal fun unlockInner() {
        synchronized(mLockObject) {
            unlock = true
            mLockObject.notify()
            for (releaseListener in releaseListeners) {
                releaseListener.release()
            }
            releaseListeners.clear()
        }
    }

    @Synchronized
    internal fun smashInner() {
        synchronized(mLockObject) {
            unlock = false
            mLockObject.notify()
            for (releaseListener in releaseListeners) {
                releaseListener.release()
            }
            releaseListeners.clear()
        }
    }

    /**
     * 破坏锁，任务链后续任务无法继续执行
     */
    fun smash() {
        handler.post {
            //Log.d(PertTaskLogConstants.LOCK_TAG, Thread.currentThread().name + "- smash( " + lockId + " )")
            //Log.d(PertTaskLogConstants.LOCK_TAG, "Terminate task chain !")
            smashInner()
        }
    }

    /**
     * 上锁
     */
    internal fun lock() {
        //Log.d(PertTaskLogConstants.LOCK_TAG, Thread.currentThread().name + "- lock( " + lockId + " )")
        try {
            synchronized(mLockObject) {
                handler.post(Runnable {
                    lockListener?.lockUp()
                })
                mLockObject.wait()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    interface LockListener {
        fun lockUp()
    }

    interface ReleaseListener {
        fun release()
    }

}