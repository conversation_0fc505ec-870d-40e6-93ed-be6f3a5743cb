package com.howbuy.global.login_impl.password

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import com.howbuy.fund.base.BaseDialogFragment
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.global.login_impl.databinding.DlgLoginSelectLoginTypeLayoutBinding
import com.howbuy.lib.utils.SysUtils

/**
 * description.
 * 密码登录, 选择登录的证件类型(手机号,开户证件号, 邮箱)
 * tao.liang
 * 2024/2/20
 */
class DlgSelectAccountType : BaseDialogFragment() {

    private var binding: DlgLoginSelectLoginTypeLayoutBinding?=null

    companion object {
        var mCloseListener: ISelectedListener? = null
        fun getInstance(bundle: Bundle, closeListener: ISelectedListener?): DlgSelectAccountType {
            mCloseListener = closeListener
            val dialog = DlgSelectAccountType()
            dialog.arguments = bundle
            return dialog
        }
    }

    override fun onCreateDialog(): Dialog? {
        val dialog = activity?.let { Dialog(it, com.howbuy.fund.base.R.style.pwdDialog) }
        activity?.layoutInflater?.let {
            binding = DlgLoginSelectLoginTypeLayoutBinding.inflate(it).apply {
                dialog?.setContentView(root)
            }
        }
        initView()
        dialog?.window?.setWindowAnimations(com.howbuy.fund.base.R.style.AnimBottom)
        dialog?.window?.setGravity(Gravity.BOTTOM)
        dialog?.window?.setLayout(SysUtils.getWidth(activity), ViewGroup.LayoutParams.WRAP_CONTENT)
        dialog?.setCanceledOnTouchOutside(true)
        isCancelable = true
        return dialog
    }

    private fun initView() {
        val itemTitle = arguments?.getString(ValConfig.IT_NAME) ?: ""
        binding?.ivSelectedPhone?.visibility = View.GONE
        binding?.ivSelectedCard?.visibility = View.GONE
        binding?.ivSelectedEmail?.visibility = View.GONE
        when (itemTitle) {
            AccountType.PHONE.desc -> {
                binding?.ivSelectedPhone?.visibility = View.VISIBLE
            }

            AccountType.EMAIL.desc -> {
                binding?.ivSelectedEmail?.visibility = View.VISIBLE
            }

            else -> {
                binding?.ivSelectedCard?.visibility = View.VISIBLE
            }
        }
        binding?.tvSelectedPhone?.setOnClickListener {
            mCloseListener?.onSelected(AccountType.PHONE)
            dismiss()
        }

        binding?.tvSelectedCard?.setOnClickListener {
            mCloseListener?.onSelected(AccountType.CARD)
            dismiss()
        }

        binding?.tvSelectedEmail?.setOnClickListener {
            mCloseListener?.onSelected(AccountType.EMAIL)
            dismiss()
        }

        binding?.tvCancel?.setOnClickListener { dismiss() }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mCloseListener = null
    }

    interface ISelectedListener {
        /**
         * 当前选中item的标题
         */
        fun onSelected(accountType: AccountType)
    }
}