package com.howbuy.global.login_impl.entity

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.AbsBody
import com.howbuy.libindexbar.IndexableEntity
import kotlinx.android.parcel.Parcelize

/**
 * Created by tao.liang on 2017/9/7.
 * 登录国家实体类
 */
@Parcelize
data class CountryBody(

    val countryVOList: MutableList<MutableList<Item>>?

) : AbsBody(), Parcelable {

    @Parcelize
    data class Item(
        val countryCode: String?,
        val chineseName: String?,
        var index: String?, //字母索引(常用, A,B,C ...)
        val englishName: String?
    ) : Parcelable, IndexableEntity {


        override fun getFieldIndexBy(): String? {
            return index
        }

        override fun setFieldIndexBy(indexField: String?) {
            this.index = index
        }

        override fun setFieldPinyinIndexBy(pinyin: String?) {
            this.index = pinyin
        }
    }
}