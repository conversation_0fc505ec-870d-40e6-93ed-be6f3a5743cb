package com.howbuy.global.upgrade.base

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.AbsBody
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import kotlinx.android.parcel.Parcelize

/**
 * @Description App版本更新
 * 注意: 海外app更新接口是交易的接口, 所以它的基类要继承 AbsBody
 * <AUTHOR>
 * @Date 2024/2/29
 * @Version V1.0
 */
@Parcelize
data class AppUpdate(
    val fileSize: String?,//文件大小(单位字节)
    val updateUrl: String?,//更新地址
    val updateDesc: String?,//更新描述
    val versionNum: String?,//版本号
    val versionNeedUpdate: String?,//版本是否需要更新(0:通知更新 1:强制更新 2:不通知 3:维护通知 4:检测更新)
) : AbsNormalBody(), Parcelable