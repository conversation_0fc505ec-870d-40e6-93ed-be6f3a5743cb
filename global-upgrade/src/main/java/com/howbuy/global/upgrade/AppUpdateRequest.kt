package com.howbuy.global.upgrade

import com.howbuy.fund.net.HttpCaller
import com.howbuy.fund.net.cache.CacheMode
import com.howbuy.fund.net.http.ReqParams
import com.howbuy.fund.net.http.RequestContentType
import com.howbuy.fund.net.interfaces.IReqNetFinished
import com.howbuy.fund.net.util.XUtils
import com.howbuy.global.upgrade.base.AppUpdate
import java.lang.reflect.Type

/**
 * @Description App版本更新接口请求
 * <AUTHOR>
 * @Date 2024/3/1
 * @Version V1.0
 */
object AppUpdateRequest {

    private const val HK_V260_CHECKUPDATE = "https://m1.apifoxmock.com/m1/2829913-1212853-default/hk/v260/checkupdate.json"

    fun requestAppUpdate(callback: IReqNetFinished?) {
        val params = createNormalReqParams(
            HK_V260_CHECKUPDATE, AppUpdate::class.java, true, null,
            false, 0, callback, null
        )
        HttpCaller.getInstance().request(params, callback)
    }

    /**
     * 创建请求参数对象 表单提交
     */
    private fun createNormalReqParams(
        uri: String?,
        clazz: Type?,
        post: Boolean,
        cacheMode: CacheMode?,
        tradeParseMode: Boolean,
        handType: Int,
        callback: IReqNetFinished?,
        paramsMap: HashMap<String, Any?>?,
    ): ReqParams {
        val url = XUtils.getUrl(uri)
        val safePolicy = XUtils.getSafePolicy(uri)
        val encrypt = XUtils.isNeedEncryption(uri)
        val needEnvelope = XUtils.isNeedEnvelope(uri)
        val needSign = XUtils.isNeedSign(uri)
        val reqParams = ReqParams()
        reqParams.url = url
        reqParams.uriKey = uri
        reqParams.cls = clazz
        reqParams.isPost = post
        reqParams.reqTag = uri
        reqParams.isTradeParseMode = tradeParseMode
        //固定拼接公共参数
        reqParams.needPublicParams = true
        reqParams.safePolicy = safePolicy
        //post by json 格式
        reqParams.requestContentType = RequestContentType.NORMAL
        reqParams.isEncrypt = encrypt
        reqParams.cacheMode = cacheMode
        reqParams.params = paramsMap
        reqParams.bytes = null
        reqParams.handType = handType
        reqParams.reqNetFinished = callback
        reqParams.startTime = System.currentTimeMillis()
        reqParams.isNeedEnvelope = needEnvelope
        reqParams.isNeedSign = needSign
        return reqParams
    }
}