<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="shareDialog" parent="android:style/Theme.Dialog">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:windowFrame">@null</item>
        <!-- Dialog的windowFrame框为无 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否漂现在activity上 -->
        <item name="android:windowIsTranslucent">false</item>
        <!-- 是否半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 除去title -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 除去背景色@null,设置背景图片 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 设置显示时周边是否为灰色，若要灰色，就就将上面的属性去掉 ,或者变成true android:background="@drawable/dhy_back" -->
    </style>

    <style name="shareAnimBottom" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/frag_up_in</item>
        <item name="android:windowExitAnimation">@anim/frag_down_out</item>
    </style>

</resources>