package com.howbuy.share;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.text.TextUtils;

import androidx.core.content.FileProvider;

import com.howbuy.lib.interfaces.IShareActionListener;
import com.howbuy.lib.utils.LogUtils;
import com.howbuy.lib.utils.SysUtils;
import com.umeng.socialize.PlatformConfig;
import com.umeng.socialize.ShareAction;
import com.umeng.socialize.UMShareListener;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.umeng.socialize.media.UMImage;
import com.umeng.socialize.media.UMWeb;

import java.io.File;

/**
 * 封装分享类
 */
public class HBShareAction implements UMShareListener {

    private static HBShareAction mHBShareAction;

    private IShareActionListener mCallBack;


    public static HBShareAction getInstance() {
        if (mHBShareAction == null) {
            mHBShareAction = new HBShareAction();
        }
        return mHBShareAction;
    }


    private HBShareAction() {
    }

    /**
     * 添加QQ平台支持
     */
    private void addQQQZonePlatform(String appId, String appKey, String packageName) {
        PlatformConfig.setQQZone(appId, appKey);
        PlatformConfig.setQQFileProvider(String.format("%s.fileProvider", packageName));
    }

    /**
     * 添加微信平台分享
     */
    public void addWXPlatform(String appId, String appSecret, String packageName) {
        PlatformConfig.setWeixin(appId, appSecret);
        if (TextUtils.isEmpty(packageName)) return;
        PlatformConfig.setWXFileProvider(String.format("%s.fileProvider", packageName));
    }

    /**
     * 添加企业微信平台分享
     *
     * @param appId
     * @param appSecret
     * @param agentId
     * @param schema
     * @param packageName
     */
    public void addWWPlatform(String appId, String appSecret, String agentId, String schema, String packageName) {
        PlatformConfig.setWXWork(appId, appSecret, agentId, schema);
        PlatformConfig.setWXWorkFileProvider(String.format("%s.fileProvider", packageName));
    }

    /**
     * 添加新浪微博平台分享
     */
    private void addSinaWbPlatform(String appId, String appSecret, String redirectUrl) {
        PlatformConfig.setSinaWeibo(appId, appSecret, redirectUrl);
    }

    /**
     * 分享到微信
     */
    public void shareWeiXin(Activity act, String title, String content, String targetUrl, Object bmp, IShareActionListener callBack) {
        if (SysUtils.checkAPK("com.tencent.mm", act)) {
            doShare(act, title, content, targetUrl, bmp, callBack, SHARE_MEDIA.WEIXIN);
        } else {
            LogUtils.pop("未安装微信");
        }
    }

    /**
     * 分享到微信朋友圈
     */
    public void shareWeiXinCircle(Activity act, String title, String content, String targetUrl, Object bmp, IShareActionListener callBack) {
        if (SysUtils.checkAPK("com.tencent.mm", act)) {
            doShare(act, title, content, targetUrl, bmp, callBack, SHARE_MEDIA.WEIXIN_CIRCLE);
        } else {
            LogUtils.pop("未安装微信");
        }
    }

    /**
     * 分享到企业微信
     *
     * @param act
     * @param title
     * @param content
     * @param targetUrl
     * @param bmp
     * @param callBack
     */
    public void shareWWork(Activity act, String title, String content, String targetUrl, Object bmp, IShareActionListener callBack) {
        if (SysUtils.checkAPK("com.tencent.wework", act)) {
            doShare(act, title, content, targetUrl, bmp, callBack, SHARE_MEDIA.WXWORK);
        } else {
            LogUtils.pop("未安装企业微信");
        }
    }

    /**
     * 分享到微博
     */
    public void shareWeiBo(Activity act, String title, String content, String targetUrl, Object bmp, IShareActionListener callBack) {
        doShare(act, title, content, targetUrl, bmp, callBack, SHARE_MEDIA.SINA);
    }

    /**
     * 分享到QQ好友
     */
    public void shareQQ(Activity act, String title, String content, String targetUrl, Object bmp, IShareActionListener callBack) {
        doShare(act, title, content, targetUrl, bmp, callBack, SHARE_MEDIA.QQ);
    }

    /**
     * 分享到QQ空间
     */
    public void shareQQZONE(Activity act, String title, String content, String targetUrl, Object bmp, IShareActionListener callBack) {
        doShare(act, title, content, targetUrl, bmp, callBack, SHARE_MEDIA.QZONE);
    }

    /**
     * 包含四种类型， 即单纯的文字、图片、音乐、视频
     *
     * @param title     要分享标题
     * @param content   要分享的文字概述
     * @param targetUrl 网页地址必须以"http://"开头
     * @param bmp       图片
     */
    private void doShare(Activity act, String title, String content, String targetUrl, Object bmp,
                         IShareActionListener callBack, SHARE_MEDIA platform) {
        this.mCallBack = callBack;
        if (!TextUtils.isEmpty(targetUrl)) { //分享的链接不为空
            UMImage umImage = getUmengImg(bmp, act);
            UMWeb web = new UMWeb(targetUrl);
            web.setTitle(title);//标题
            if (TextUtils.isEmpty(content)) {
                web.setDescription("上好买全球APP，开启全球资产配置");//防止sdk 默认给个“这里是描述”
            } else {
                web.setDescription(content);//描述
            }
            web.setThumb(umImage);
            new ShareAction(act).setPlatform(platform)
                    .withMedia(web)
                    .setCallback(this)
                    .share();
        } else {//分享图文
            UMImage umImage = getUmengImg(bmp, act);
            UMImage umImage2 = getUmengImg(bmp, act);
            umImage.setThumb(umImage2);
            new ShareAction(act).setPlatform(platform)
                    .withText(title)
                    .withMedia(umImage)
                    .setCallback(this)
                    .share();
        }
    }


    /**
     * 直接调用系统分享
     *
     * @param cx
     * @param title        标题(在分享出去的内容上是看不到的, 需要给intent设置一个title用)
     * @param shareContent 拼接好的分享的内容, 格式: 文本 + 链接 (eg: 这是一段文案: http://www.baidu.com/ )
     * @param bmp
     */
    public void shareSys(Context cx, String title, String shareContent, Object bmp) {
        if (cx == null) {
            return;
        }
        File file = null;
        if (bmp instanceof File) {
            file = (File) bmp;
        } else if (bmp instanceof String) {
            file = new File((String) bmp);
        }

        boolean hasImage = false;
        Intent intent = new Intent(Intent.ACTION_SEND);
        intent.putExtra(Intent.EXTRA_SUBJECT, title);
        intent.putExtra(Intent.EXTRA_TEXT, shareContent); // 附带的说明信息
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (file != null && file.exists()) {
            String type = file.getAbsolutePath().toLowerCase();
            int n = type.lastIndexOf(".");
            if (n != -1) {
                hasImage = isImage(type.substring(n + 1));
            }
            Uri uri = FileProvider.getUriForFile(cx, cx.getPackageName() + ".fileProvider", file);
            intent.putExtra(Intent.EXTRA_STREAM, uri); // 传输图片或者文件
        }
        if (hasImage) {
            intent.setType("image/*"); // 分享图片
        } else {
            intent.setType("text/plain");
        }
        try {
            cx.startActivity(Intent.createChooser(intent, title));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private static boolean isImage(String type) {
        if (type != null
                && ("jpg".equals(type) || "gif".equals(type) || "png".equals(type)
                || "jpeg".equals(type) || "bmp".equals(type) || "wbmp".equals(type)
                || "ico".equals(type) || "jpe".equals(type))) {
            return true;
        }
        return false;
    }


    public static UMImage getUmengImg(Object bmp, Context cx) {
        if (bmp != null) {
            UMImage um = null;
            if (bmp instanceof File) {
                um = new UMImage(cx, (File) bmp);
            } else if (bmp instanceof Bitmap) {
                um = new UMImage(cx, (Bitmap) bmp);
            } else if (bmp instanceof String) {
                um = new UMImage(cx, bmp.toString());
            } else if (bmp instanceof byte[]) {
                um = new UMImage(cx, (byte[]) bmp);
            } else if (bmp instanceof Integer) {
                um = new UMImage(cx, Integer.parseInt(bmp.toString()));
            }
            return um;
        }
        return null;
    }


    private int getPlatformType(SHARE_MEDIA media) {
        if (SHARE_MEDIA.WEIXIN.equals(media))
            return 1;
        else if (SHARE_MEDIA.WEIXIN_CIRCLE.equals(media))
            return 2;
        else if (SHARE_MEDIA.SINA.equals(media))
            return 3;
        else if (SHARE_MEDIA.QQ.equals(media))
            return 5;
        else if (SHARE_MEDIA.QZONE.equals(media))
            return 6;
        else
            return 4;
    }


    @Override
    public void onStart(SHARE_MEDIA share_media) {

    }


    @Override
    public void onResult(SHARE_MEDIA share_media) {
        mCallBack.onSuccess(getPlatformType(share_media), null);

    }


    @Override
    public void onError(SHARE_MEDIA share_media, Throwable throwable) {
        throwable.printStackTrace();
        mCallBack.onError(getPlatformType(share_media));
    }


    @Override
    public void onCancel(SHARE_MEDIA share_media) {
        mCallBack.onCancel(getPlatformType(share_media));
    }
}
