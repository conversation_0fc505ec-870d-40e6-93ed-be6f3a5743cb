package com.howbuy.fund.base.dialog

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import com.howbuy.fund.base.BaseDialogFragment
import com.howbuy.fund.base.databinding.DialogGlobalHintLayoutBinding
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.SysUtils

/**
 * description.
 * 好买全球App中的提示弹框:
 *      标题
 *    提示内容文案
 *  左边按钮  右边按钮
 *
 * 目前只实现 当前已有的样式, 待后期有需要, 可以自选拓展
 * 由于统一封装的 dialog库中的提示类弹框 样式是掌基的样式,在海外中不适用,所以 在海外app中单独
 * 实现一个 通用的 提示类弹框
 * tao.liang
 * 2024/7/10
 */
class GlobalHintDialog : BaseDialogFragment() {

    private var binding: DialogGlobalHintLayoutBinding? = null

    private var mTitle: String? = ""
    private var mMsg: String? = ""
    private var mBtnCancelText: String? = ""
    private var mBtnSureText: String? = ""
    private var mCancelable: Boolean = true
    private var mCancelTouchOutside: Boolean = true


    companion object {
        var mCloseListener: IGlobalHintDlgListener? = null
        fun getInstance(bundle: Bundle?, closeListener: IGlobalHintDlgListener?): GlobalHintDialog {
            mCloseListener = closeListener
            val dialog = GlobalHintDialog()
            dialog.arguments = bundle
            return dialog
        }
    }

    /**
     * 设置弹框标题, 如果无值, 不显示
     */
    fun setTitle(title: String?): GlobalHintDialog {
        this.mTitle = title
        return this
    }

    /**
     * 设置弹框提示文案, 如果无值, 不显示
     */
    fun setMessage(msg: String?) : GlobalHintDialog{
        this.mMsg = msg
        return this
    }

    /**
     * 设置弹框左边按钮, 如果无值, 不显示
     */
    fun setBtnCancelText(btnCancelText: String?) : GlobalHintDialog{
        this.mBtnCancelText = btnCancelText
        return this
    }

    /**
     * 设置弹框右边按钮, 如果无值, 不显示
     */
    fun setBtnSureText(btnSureText: String?) : GlobalHintDialog{
        this.mBtnSureText = btnSureText
        return this
    }

    /**
     * 设置弹框是否可以 处理 返回按钮 消失(默认为true, 可以按返回键消失)
     * 说明: 如果设置了false, 那 TouchOutside就会生效了, 返回和触摸都不会消失
     *      如果设置了true, 那 TouchOutside的设置只会影响 触摸状态
     */
    fun setDlgCancelable(cancel:Boolean): GlobalHintDialog{
        this.mCancelable = cancel
        return this
    }

    /**
     * 设置弹框是否可以 触摸屏幕 消失(默认为true, 会消失)
     */
    fun setDlgCancelTouchOutside(cancel:Boolean): GlobalHintDialog{
        this.mCancelTouchOutside = cancel
        return this
    }

    override fun onCreateDialog(): Dialog? {
        val dialog = activity?.let { Dialog(it, com.howbuy.fund.base.R.style.pwdDialog) }
        activity?.layoutInflater?.let {
            binding = DialogGlobalHintLayoutBinding.inflate(it).apply {
                dialog?.setContentView(root)
            }
        }
        initView()
        dialog?.window?.setGravity(Gravity.CENTER)
        dialog?.window?.setLayout(
            SysUtils.getWidth(activity) - DensityUtils.dp2px(60f),
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        dialog?.setCanceledOnTouchOutside(this.mCancelTouchOutside)
        isCancelable = this.mCancelable
        return dialog
    }

    private fun initView() {
        binding?.tvTitle?.visibility =
            if (TextUtils.isEmpty(this.mTitle)) View.GONE else View.VISIBLE
        binding?.tvTitle?.text = this.mTitle

        binding?.tvMessage?.visibility =
            if (TextUtils.isEmpty(this.mMsg)) View.GONE else View.VISIBLE
        binding?.tvMessage?.text = this.mMsg

        binding?.tvCancel?.visibility =
            if (TextUtils.isEmpty(this.mBtnCancelText)) View.GONE else View.VISIBLE
        binding?.tvCancel?.text = this.mBtnCancelText

        binding?.tvSure?.visibility =
            if (TextUtils.isEmpty(this.mBtnSureText)) View.GONE else View.VISIBLE
        binding?.tvSure?.text = this.mBtnSureText

        if (TextUtils.isEmpty(this.mBtnCancelText) || TextUtils.isEmpty(this.mBtnSureText)) {
            binding?.vSpace?.visibility = View.GONE
            //如果只有一个按钮, 按钮的宽度固定
            if (TextUtils.isEmpty(this.mBtnCancelText)){
                //只显示右边按钮
                (binding?.tvSure?.layoutParams as LinearLayout.LayoutParams)?.also {
                    it.width = DensityUtils.dp2px(170f)
                    it.weight = 0f
                }
            }else{
                //只显示左边按钮
                (binding?.tvCancel?.layoutParams as LinearLayout.LayoutParams)?.also {
                    it.width = DensityUtils.dp2px(170f)
                    it.weight = 0f
                }
            }
        } else {
            binding?.vSpace?.visibility = View.VISIBLE
        }

        binding?.tvCancel?.setOnClickListener {
            mCloseListener?.onClick(CLiCK_DLG_BTN_TYPE.CLICK_CANCEL)
            dismiss()
        }

        binding?.tvSure?.setOnClickListener {
            mCloseListener?.onClick(CLiCK_DLG_BTN_TYPE.CLICK_SURE)
            dismiss()
        }

    }


    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        mCloseListener?.onClick(CLiCK_DLG_BTN_TYPE.ON_DISMISS)
        mCloseListener = null
    }

    interface IGlobalHintDlgListener {
        fun onClick(clickType: CLiCK_DLG_BTN_TYPE)
    }

    /**
     * 点击弹框按钮定义
     */
    enum class CLiCK_DLG_BTN_TYPE {

        /**
         * onDismiss 回调关闭事件
         */
        ON_DISMISS,

        /**
         * 关闭按钮(点击 'X' 按钮, 目前暂无)
         */
        CLICK_CLOSE,

        /**
         * 取消按钮
         */
        CLICK_CANCEL,

        /**
         * 确定按钮
         */
        CLICK_SURE
    }

}