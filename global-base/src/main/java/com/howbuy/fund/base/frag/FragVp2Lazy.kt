package com.howbuy.fund.base.frag

import androidx.viewbinding.ViewBinding

/**
 * @Description ViewPager2 fragment 懒加载
 * <AUTHOR>
 * @Date 2022/12/19 23:17
 * @Version V806
 */
abstract class FragVp2Lazy<VB : ViewBinding> : AbsFragViewBinding<VB>() {

    private var isFirstLoad = true

    override fun onResume() {
        super.onResume()
        if (isFirstLoad) {
            fetchData()
            isFirstLoad = false
        }
    }

    abstract fun fetchData()

}