package com.howbuy.fund.base.utils

import com.howbuy.lib.utils.LogUtils
import java.util.Objects
import java.util.UUID
import java.util.concurrent.atomic.AtomicInteger

object SceneIdFactory {

    /**
     * 生成规则：根据对象的hash值字符串，使用UUID生成，保证因子[source]不变的情况下(对应的hashCode不变)，生成的值相同
     * @param source 生成因子
     */
    fun createSceneId(source:Any?): String {
        source?:return ""
        val sourceHash = source.hashCode().toString().toByteArray()
        val arr = UUID.nameUUIDFromBytes(sourceHash).toString().split("-")
        return arr[0]
    }
}

/**
 * SceneId 因子，业务层可以根据业务需要，刷新因子[refresh].
 * 刷新后，使用该因子对象创建的sceneId将会发生改变
 * 可以实现，在原始因子不变的情况下，刷新因子，也能生成新的sceneId
 */
@Suppress("MemberVisibilityCanBePrivate")
class SceneIdFactor(
    /**原始因子，生命周期较长*/
    private val raw: Any
) {
    /**用于更新因子，该值的更新将导致hashCode的值对应变更*/
    private val times = AtomicInteger(0)

    /**
     * 刷新因子
     */
    fun refresh() {
        val timesValue = times.incrementAndGet()
        LogUtils.d("SceneIdFactor", "refresh call, timesValue:$timesValue")
    }

    override fun hashCode(): Int {
        return Objects.hash(raw, times.get())
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as SceneIdFactor

        if (raw != other.raw) return false
        if (times != other.times) return false

        return true
    }
}