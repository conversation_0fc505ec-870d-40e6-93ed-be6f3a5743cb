package com.howbuy.fund.base.widget.tag;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.os.Build;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.DragEvent;
import android.view.HapticFeedbackConstants;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.howbuy.fund.base.R;
import com.howbuy.lib.utils.LogUtils;


public class TagLayout extends ViewGroup {
    private static final String TAG = "TagLayout";

    /**
     * view size param
     */
    private int mWidth;

    /**
     * custom layout param
     */
    private int lineMargin;
    private int tagMargin;
    /**
     * lines limit, <=0: no limit
     */
    private int lines;
    private int tagHeight;
    /**
     * max rate of item width:[0,1] : 1: no limit
     */
    public float maxItemWeight;

    /**
     * only for measure child
     */
    private LayoutParams tempLay;

    private TagAdapter tagAdapter;
    /**
     * all children show in one line
     */
    private boolean childrenInOneLine;
    /**
     * 是否可以拖拽tag
     */
    private boolean itemDragEnable = false;
    /**
     * 所有位置均可拖拽
     */
    private static final int NO_FROZEN_INDEX = -1;

    /**
     * 通过该方式，创建自定义DragShadowBuilder
     */
    private DragShadowBuilderFactory dragShadowBuilderFactory;
    /**
     * shadow缩放因子：1为不缩放; 小于1为缩小; 大于1为放大.
     */
    private float dragShadowScale;
    /**
     * 拖拽开始时，是否需要有震动提示.默认有
     */
    private boolean dragFeedback;
    /**
     * 固定不能拖拽的index。（设置以后，该index对应的TAG无法改变位置）
     */
    private int indexFrozen = NO_FROZEN_INDEX;

    private int linesShow;

    @Nullable
    private OnLongClickListener longClickListener;
    private EditCompleteListener editCompleteListener;
    private boolean tagsHorCenter;
    /**
     * 在单行显示，且设置了“tagsHorCenter”为true的情况下，左侧的偏移量，以满足整体水平居中
     */
    private int leftLayOffset = 0;

    public TagLayout(Context ctx) {
        super(ctx, null);
        initialize(ctx, null, 0);
    }


    public TagLayout(Context ctx, AttributeSet attrs) {
        super(ctx, attrs);
        initialize(ctx, attrs, 0);
    }


    public TagLayout(Context ctx, AttributeSet attrs, int defStyle) {
        super(ctx, attrs, defStyle);
        initialize(ctx, attrs, defStyle);
    }

    public int getTagHeight() {
        return tagHeight;
    }

    public void setTagHeight(int tagHeight) {
        this.tagHeight = tagHeight;
    }

    public int getLinesShow() {
        return linesShow;
    }

    public static final float DEFAULT_LINE_MARGIN = 0;
    public static final float DEFAULT_TAG_MARGIN = 5;
    public static final int DEFAULT_LINES_NO_LIMIT = -1;
    public static final int DEFAULT_TAG_HEIGHT = LayoutParams.WRAP_CONTENT;
    public static final float DEFAULT_MAX_ITEM_WEIGHT = 1.0F;

    private void initialize(Context ctx, AttributeSet attrs, int defStyle) {
        // get AttributeSet
        TypedArray typeArray = ctx.obtainStyledAttributes(attrs, R.styleable.TagLayout, defStyle, defStyle);
        this.lineMargin = typeArray.getDimensionPixelSize(R.styleable.TagLayout_lineMargin, dipToPx(DEFAULT_LINE_MARGIN));
        this.tagMargin = typeArray.getDimensionPixelSize(R.styleable.TagLayout_tagMargin, dipToPx(DEFAULT_TAG_MARGIN));
        this.lines = typeArray.getInteger(R.styleable.TagLayout_lines, DEFAULT_LINES_NO_LIMIT);
        this.tagHeight = typeArray.getDimensionPixelSize(R.styleable.TagLayout_tagHeight, DEFAULT_TAG_HEIGHT);
        this.maxItemWeight = typeArray.getFloat(R.styleable.TagLayout_maxItemWeight, DEFAULT_MAX_ITEM_WEIGHT);
        this.childrenInOneLine = typeArray.getBoolean(R.styleable.TagLayout_childrenInOneLine, false);
        this.itemDragEnable = typeArray.getBoolean(R.styleable.TagLayout_itemDragEnable, false);
        this.dragFeedback = typeArray.getBoolean(R.styleable.TagLayout_dragFeedback, true);
        this.indexFrozen = typeArray.getInt(R.styleable.TagLayout_indexFrozen, -1);
        this.dragShadowScale = typeArray.getFloat(R.styleable.TagLayout_dragShadowScale, 1.0f);
        this.tagsHorCenter = typeArray.getBoolean(R.styleable.TagLayout_tagsHorCenter, false);
        typeArray.recycle();

        tempLay = new LayoutParams(
                LayoutParams.WRAP_CONTENT,
                LayoutParams.WRAP_CONTENT);
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWidth = w;
    }

    @SuppressLint("DefaultLocale")
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        mWidth = MeasureSpec.getSize(widthMeasureSpec);
        if (getChildCount() <= 0) {
            setMeasuredDimension(mWidth, getMinimumHeight());
            return;
        }

        final int paddingLeft = getPaddingLeft();
        final int paddingRight = getPaddingRight();
        int totalWidth = paddingLeft + paddingRight;
        int line = getChildCount() > 0 ? 1 : 0;

        int maxItemWidth = -1;
        if (null != tagAdapter) {
            int limitWidth = (int) (mWidth * maxItemWeight);
            if (limitWidth < 0) {
                limitWidth = mWidth;
            }
            maxItemWidth = limitWidth - 2 * tagMargin;
        }

        int itemMaxHeight = 0;
        for (int i = 0; i < getChildCount() && null != tagAdapter; i++) {
            View child = getChildAt(i);
            child.setLayoutParams(tempLay);
            measureChild(child, widthMeasureSpec, heightMeasureSpec);
            int itemWidth = child.getMeasuredWidth();
            MarginLayoutParams layoutParams;
            int childWidthSpec = widthMeasureSpec;
            int childHeightSpec = tagHeight != DEFAULT_TAG_HEIGHT ?
                    MeasureSpec.makeMeasureSpec(tagHeight, MeasureSpec.EXACTLY) :
                    heightMeasureSpec;
            if (itemWidth > 0) {
                if (itemWidth > maxItemWidth && !childrenInOneLine) {
                    itemWidth = maxItemWidth;
                }
                childWidthSpec = MeasureSpec.makeMeasureSpec(itemWidth, MeasureSpec.EXACTLY);
                layoutParams = new MarginLayoutParams(itemWidth, tagHeight);
            } else {
                layoutParams = new MarginLayoutParams(LayoutParams.WRAP_CONTENT, tagHeight);
            }
            layoutParams.leftMargin = tagMargin;
            layoutParams.rightMargin = tagMargin;
            layoutParams.topMargin = lineMargin;
            layoutParams.bottomMargin = lineMargin;
            child.setLayoutParams(layoutParams);
            measureChild(child, childWidthSpec, childHeightSpec);

            int childWidth = child.getMeasuredWidth();
            int childHeight = child.getMeasuredHeight();
            if (childHeight > itemMaxHeight) {
                itemMaxHeight = childHeight;
            }
            int tempChildNeed = layoutParams.leftMargin + layoutParams.rightMargin + childWidth;
            if (childrenInOneLine) {
                totalWidth += tempChildNeed;
                continue;
            }

//            LogUtils.d(TAG,
//                    String.format("measure, i:%d, tempChildNeed:%d, totalWidth:%d, width:%d",
//                            i,
//                            tempChildNeed,
//                            totalWidth,
//                            mWidth)
//            );
            if ((tempChildNeed + totalWidth) > mWidth) {
                if (0 < this.lines && line >= this.lines) {
                    for (int j = getChildCount()-1; j >= i; j--) {
                        removeViewAt(j);
                    }
                    break;
                }

                //new line
                totalWidth = tempChildNeed + getPaddingLeft() + getPaddingRight();
                itemMaxHeight = childHeight;
                line++;
            } else {
                totalWidth += childWidth + layoutParams.leftMargin + layoutParams.rightMargin;
            }
        }
        int maxWidthAvailable = mWidth;
        if (childrenInOneLine) {
            mWidth = totalWidth;
        }
        int totalHeight = line * (itemMaxHeight + 2 * lineMargin) + getPaddingBottom() + getPaddingTop();

        //如果单行显示，宽度未被填满，且设置了水平居中，那么计算出标签的左侧起始偏移量，否则其偏移量为0
        if (line == 1 && tagsHorCenter && maxWidthAvailable > totalWidth) {
            leftLayOffset = (maxWidthAvailable - totalWidth) / 2;
        } else {
            leftLayOffset = 0;
        }
        setMeasuredDimension(mWidth, totalHeight);
    }

    public void setChildrenInOneLine(boolean childrenInOneLine) {
        this.childrenInOneLine = childrenInOneLine;
        invalidate();
    }

    public void setTagAdapter(TagAdapter tagAdapter) {
        this.tagAdapter = tagAdapter;
    }

    public TagAdapter getTagAdapter() {
        return tagAdapter;
    }

    public void notifyDataSetChanged() {
        addAllChildren();
        invalidate();
    }

    public static int dipToPx(float dipValue) {
        DisplayMetrics metrics = Resources.getSystem().getDisplayMetrics();
//        LogUtils.d(TAG, "metrics.density: " + metrics.density);
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dipValue, metrics);
    }


    @SuppressLint({"DrawAllocation", "DefaultLocale"})
    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        linesShow = 0;
        if (null == tagAdapter) return;

        int childCount = getChildCount();
//        LogUtils.d(TAG, String.format("onLayout, %d child need to layout", childCount));

        int mTotalHeight = 0;
        int mTotalWidth = 0;
        int mTempHeight = 0;
        int widthH = mWidth - getPaddingLeft() - getPaddingRight();
        if (childCount > 0) {
            linesShow = 1;
        }
        for (int i = 0; i < childCount; i++) {
            final View childView = getChildAt(i);
            int measureHeight = childView.getMeasuredHeight();
            int measuredWidth = childView.getMeasuredWidth();
//            LogUtils.d(TAG, "onLayout：index=" + i + " childWidth: " + measuredWidth + ", childHeight: " + measureHeight);
            mTempHeight = Math.max(measureHeight, mTempHeight);

            int leftMargin = tagMargin;
            int rightMargin = tagMargin;
            int topMargin = lineMargin;
            int bottomMargin = lineMargin;
            if (childView.getLayoutParams() instanceof MarginLayoutParams) {
                MarginLayoutParams itemParams = (MarginLayoutParams) childView.getLayoutParams();
                leftMargin = itemParams.leftMargin;
                rightMargin = itemParams.rightMargin;
                topMargin = itemParams.topMargin;
                bottomMargin = itemParams.bottomMargin;
            }

            int widthNeed = measuredWidth + leftMargin + rightMargin;
            if ((widthNeed + mTotalWidth) > widthH) {
                mTotalWidth = 0;
                mTotalHeight += mTempHeight + topMargin + bottomMargin;
                mTempHeight = 0;
                linesShow++;
            }
            int left = mTotalWidth + leftMargin + getPaddingLeft();
            int right = left + measuredWidth;

            int top = mTotalHeight + topMargin + getPaddingTop();
            int bottom = top + measureHeight;
            childView.layout(left + leftLayOffset, top, right + leftLayOffset, bottom);
//            LogUtils.d(TAG, "left=" + left + "；right=" + right + "; top=" + top + "；bottom=" + bottom);
            mTotalWidth += widthNeed;
        }
    }


    private synchronized void addAllChildren() {
        LogUtils.d(TAG, "addAllChildren, invoked");
        removeAllViews();
        if (null == tagAdapter) return;
        for (int i = 0; i < tagAdapter.getCount(); i++) {
            View tagView = tagAdapter.getTagView(TagLayout.this, i);
            addView(tagView);
        }
        if (this.itemDragEnable) {
            addDragListeners();
        } else {
            setOnDragListener(null);
        }
    }

    public void setLineMargin(int lineMargin) {
        if (this.lineMargin == lineMargin) return;
        this.lineMargin = lineMargin;
        invalidate();
    }

    public void setTagMargin(int tagMargin) {
        if (this.tagMargin == tagMargin) return;
        this.tagMargin = tagMargin;
        invalidate();
    }

    public int getTagMargin() {
        return tagMargin;
    }

    public void setLines(int lines) {
        if (this.lines == lines) return;
        if (this.lines < lines) {
            addAllChildren();
        }
        this.lines = lines;
        invalidate();
    }

    public boolean isItemDragEnable() {
        return itemDragEnable;
    }

    public void setItemDragEnable(boolean itemDragEnable) {
        boolean stateBeforeChange = this.itemDragEnable;
        this.itemDragEnable = itemDragEnable;
        if (!stateBeforeChange && itemDragEnable) {
            //remove all
            addDragListeners();
        }
    }


    /**
     * @param x
     * @param y
     * @return -1:not found; [0, size-1]
     */
    @SuppressLint("DefaultLocale")
    public int obtainIndexByPosition(int x, int y) {
        for (int i = 0; i < getChildCount(); i++) {
            View child = getChildAt(i);
            int left = child.getLeft();
            int right = child.getRight();
            int top = child.getTop();
            int bottom = child.getBottom();
            if (left < x && x < right && top < y && y < bottom) {
                LogUtils.d(TAG, String.format("child position:%d, left:%d, right:%d, top:%d, bottom:%d", i, left, right, top, bottom));
                return i;
            }
        }
        return -1;
    }

    public DragShadowBuilderFactory getDragShadowBuilderFactory() {
        return dragShadowBuilderFactory;
    }

    public void setDragShadowBuilderFactory(DragShadowBuilderFactory dragShadowBuilderFactory) {
        this.dragShadowBuilderFactory = dragShadowBuilderFactory;
        if (!itemDragEnable) {
            return;
        }
        invalidate();
    }

    public float getDragShadowScale() {
        return dragShadowScale;
    }

    public void setDragShadowScale(float dragShadowScale) {
        this.dragShadowScale = dragShadowScale;
        if (!itemDragEnable) {
            return;
        }
        invalidate();
    }

    public boolean isDragFeedback() {
        return dragFeedback;
    }

    public void setDragFeedback(boolean dragFeedback) {
        this.dragFeedback = dragFeedback;
    }

    public int getIndexFrozen() {
        return indexFrozen;
    }

    public void setIndexFrozen(int indexFrozen) {
        this.indexFrozen = indexFrozen;
    }

    /**
     * 是否居中显示（仅支持一行的场景）
     */
    public boolean isTagsHorCenter() {
        return tagsHorCenter;
    }

    /**
     * 是否居中显示（仅支持一行的场景）
     */
    public void setTagsHorCenter(boolean tagsHorCenter) {
        this.tagsHorCenter = tagsHorCenter;
        invalidate();
    }

    public void setOnItemLongClickListener(@NonNull OnLongClickListener onItemLongClickListener) {
        this.longClickListener = onItemLongClickListener;
    }

    public void setEditCompleteListener(EditCompleteListener editCompleteListener) {
        this.editCompleteListener = editCompleteListener;
    }

    private DragShadowBuilder createShadowBuilder(View view) {
        if (null != dragShadowBuilderFactory) {
            return dragShadowBuilderFactory.create(view);
        }
        return new DefaultShadowBuilder(view, dragShadowScale);
    }

    private void removeAllDragListeners() {
        if (getChildCount() <= 0) return;
        for (int i = 0; i < getChildCount(); i++) {
            View child = getChildAt(i);
            child.setOnLongClickListener(null);
            child.setOnDragListener(null);
        }
    }

    private void addDragListeners() {
        if (getChildCount() <= 0) return;
        for (int i = 0; i < getChildCount(); i++) {
            View child = getChildAt(i);
            child.setOnLongClickListener(new OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    if (null != longClickListener) {
                        longClickListener.onLongClick(v);
                    }
                    if (!itemDragEnable) {
                        return false;
                    }
                    ClipData dragData = ClipData.newPlainText("Label", "tag-" + indexOfChild(v));
                    DragShadowBuilder shadowBuilder = createShadowBuilder(v);
                    // Starts the drag
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        v.startDragAndDrop(dragData, shadowBuilder, v, 0);
                    } else {
                        v.startDrag(dragData, shadowBuilder, v, 0);
                    }
                    if (dragFeedback) {
                        v.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS,
                                HapticFeedbackConstants.FLAG_IGNORE_GLOBAL_SETTING);
                    }
                    return true;
                }
            });
        }
        setOnDragListener(new DragEventListener(TagLayout.this));
    }

    private static class DragEventListener implements OnDragListener {
        private float xLocation;
        private float yLocation;
        private View drag;
        private TagLayout tagLayout;

        public DragEventListener(TagLayout tagLayout) {
            this.tagLayout = tagLayout;
        }

        public boolean onDrag(View v, DragEvent event) {
            //获取事件
            int action = event.getAction();
            switch (action) {
                case DragEvent.ACTION_DRAG_STARTED:
                    if (event.getLocalState() instanceof View) {
                        this.drag = (View) event.getLocalState();
                        drag.setVisibility(View.INVISIBLE);
                    }
                    break;
                case DragEvent.ACTION_DRAG_LOCATION:
                    xLocation = event.getX();
                    yLocation = event.getY();
                    handleMove();
                    break;
                case DragEvent.ACTION_DROP:
                    if (null != drag) drag.setVisibility(View.VISIBLE);
                    xLocation = yLocation = 0;
                    break;
                case DragEvent.ACTION_DRAG_ENDED:
                    if (null != drag) drag.setVisibility(View.VISIBLE);
                    if (null != tagLayout.editCompleteListener) {
                        tagLayout.editCompleteListener.onEditComplete();
                    }
                    break;
                default:
                    break;
            }
            return true;
        }

        private void handleMove() {
            if (null == drag) {
                return;
            }
            int currentIndex = tagLayout.indexOfChild(drag);
            int targetIndex = currentIndex;

            int index = tagLayout.obtainIndexByPosition((int) xLocation, (int) yLocation);
            LogUtils.d(TAG, "drag to index: " + index + ", x:" + xLocation + ", y:" + yLocation);
            if (index == -1) {
                LogUtils.d(TAG, "location error");
                return;
            }
            View stub = tagLayout.getChildAt(index);
            int leftStub = stub.getLeft();
            int midStubX = (stub.getRight() + leftStub) / 2;
            int rightStub = stub.getRight();

            if (xLocation >= leftStub && xLocation < midStubX) {
                targetIndex = index;
            } else if (xLocation >= midStubX && xLocation < rightStub) {
                targetIndex = index;
            }
            if (currentIndex == targetIndex) {
                return;
            }

            if (tagLayout.indexFrozen != NO_FROZEN_INDEX && targetIndex == tagLayout.indexFrozen) {
                LogUtils.d(TAG, "target index frozen");
//                drag.setVisibility(View.VISIBLE);
                return;
            }

            //move
            tagLayout.removeView(drag);
            tagLayout.addView(drag, targetIndex);
            drag.setVisibility(View.INVISIBLE);
        }
    }

    public interface DragShadowBuilderFactory {
        DragShadowBuilder create(View view);
    }

    public interface EditCompleteListener {
        void onEditComplete();
    }
}
