package com.howbuy.fund.base.utils;

import android.text.Html;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.URLSpan;
import android.view.View;
import android.widget.TextView;

import com.howbuy.fund.base.config.ValConfig;
import com.howbuy.lib.utils.DateUtils;
import com.howbuy.lib.utils.HtmlClickSpan;
import com.howbuy.lib.utils.MathUtils;
import com.howbuy.lib.utils.StrUtils;
import com.howbuy.lib.utils.ViewUtils;

import java.util.Calendar;

/**
 * 文字格式化（空值、数字最大值等处理）
 * Created by shuming.zhao on 2018/4/13.
 */
public class FundTextUtils {

    private FundTextUtils() {
    }

    /**
     * 格式化空数据（不为空的时候显示 内容，为空显示--）
     *
     * @param tv   TextView
     * @param data 内容
     * @return 格式化后文字
     */
    public static String showTextEmpty(TextView tv, String data) {
        return showTextEmpty(tv, data, 1);
    }

    /**
     * set文本
     *
     * @param tv          TextView
     * @param content     真正的内容
     * @param defaultType 没有内容，默认值类型
     *                    0 "暂无数据"
     *                    1 "--"
     *                    -1 ""
     *                    -2 view gone
     * @return 实际显示的内容
     */
    public static String showTextEmpty(TextView tv, String content, int defaultType) {
        if (TextUtils.isEmpty(content)) {
            if (defaultType == -2) {
                ViewUtils.setVisibility(tv, View.GONE);
            } else if (defaultType == 0) {
                content = ValConfig.NULL_TXT0;
            } else if (defaultType == 1) {
                content = ValConfig.NULL_TXT1;
            } else {
                content = "";
            }
        }
        if (tv != null) {
            tv.setText(content);
        }
        return content;
    }

    /**
     * 格式化空数据（不为空的时候显示 内容，为空显示--）
     *
     * @param data 内容
     * @return 格式化后文字
     */
    public static String showTextEmpty(String data) {
        return showTextEmpty(data, 1);
    }

    /**
     * 格式化内容
     *
     * @param content     真正的内容
     * @param defaultType 没有内容，默认值类型
     *                    0 "暂无数据"
     *                    1 "--"
     *                    -1 ""
     *                    -2 view gone
     * @return 实际显示的内容
     */
    public static String showTextEmpty(String content, int defaultType) {
        return showTextEmpty("", content, "", defaultType);
    }

    /**
     * 格式化空数据（不为空的时候显示 前缀+内容+后缀，为空显示--）
     *
     * @param startText 内容不为空时候前面文案
     * @param data      内容
     * @param endText   内容不为空时候后面文案
     * @return 格式化后文字
     */
    public static String showTextEmpty(String startText, String data, String endText) {
        return showTextEmpty(startText, data, endText, 1);
    }

    /**
     * 格式化空数据（不为空的时候显示 前缀+内容+后缀，为空显示--）
     *
     * @param startText   内容不为空时候前面文案
     * @param content     内容
     * @param endText     内容不为空时候后面文案
     * @param defaultType 没有内容，默认值类型
     *                    0 "暂无数据"
     *                    1 "--"
     *                    -1 ""
     * @return 格式化后文字
     */
    public static String showTextEmpty(String startText, String content, String endText, int defaultType) {
        if (TextUtils.isEmpty(content)) {
            if (defaultType == 0) {
                return ValConfig.NULL_TXT0;
            } else if (defaultType == 1) {
                return ValConfig.NULL_TXT1;
            } else {
                return "";
            }
        }
        return startText + content + endText;
    }

    /**
     * 格式化空数据（不为空的时候显示 内容，为空显示emptyText）
     *
     * @param content
     * @param emptyText
     * @return 格式化后文字
     */
    public static String showTextEmpty(String content, String emptyText) {
        return showTextEmpty("", content, "", emptyText);
    }

    /**
     * 格式化空数据（不为空的时候显示 前缀+内容+后缀，为空显示emptyText）
     *
     * @param startText 内容不为空时候前面文案
     * @param content   内容
     * @param endText   内容不为空时候后面文案
     * @param emptyText 为空显示
     * @return 格式化后文字
     */
    public static String showTextEmpty(String startText, String content, String endText, String emptyText) {
        if (TextUtils.isEmpty(content)) {
            return emptyText;
        }
        return startText + content + endText;
    }

    /**
     * 为空显示emptyText 大于max字符显示...最少显示3个字符
     *
     * @param content   数据
     * @param maxLen    设置最大显示字体个数 大于3才有效
     * @param emptyText 默认值
     */
    public static String showMaxText(String content, int maxLen, String emptyText) {
        if (TextUtils.isEmpty(content)) {
            return emptyText == null ? content : emptyText;
        } else {
            content = content.trim();
            if (maxLen > 3) {
                int n = content.length();
                if (n > maxLen) {
                    return content.substring(0, maxLen - 3) + "...";
                }
            }
        }
        return content;
    }

    /**
     * 为空显示emptyText 最大显示 maxLen-1 个字符 + …
     *
     * @param content
     * @param maxLen
     * @param emptyText
     * @return
     */
    public static String showMaxLength(String content, int maxLen, String emptyText) {
        if (TextUtils.isEmpty(content)) {
            return emptyText;
        } else {
            if (content.length() > maxLen) {
                return content.substring(0, maxLen - 1) + "…";
            }
        }
        return content;
    }

    /**
     * 向下取整（eg：93.5 -> 93）
     *
     * @param valueStr
     * @return
     */
    public static int floor(String valueStr) {
        double value = 0;
        if (!TextUtils.isEmpty(valueStr)) {
            try {
                value = Math.floor(MathUtils.forValF(valueStr, 0));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return (int) value;
    }

    /**
     * 向上取整（eg：93.5 -> 94）
     *
     * @param valueStr
     * @return
     */
    public static int ceil(String valueStr) {
        double value = 0;
        if (!TextUtils.isEmpty(valueStr)) {
            try {
                value = Math.ceil(MathUtils.forValF(valueStr, 0));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return (int) value;
    }

    /**
     * 为内容中html超链接添加颜色、点击事件以及下划线,不知道超链接位置时使用
     *
     * @param rich_tvmsg      TextView
     * @param content         文本内容
     * @param color           超链接颜色
     * @param onClickListener 回调,为空则没点击事件
     * @param underlineText   是否要下划线
     */
    public static void renderH5Link(TextView rich_tvmsg, String content, int color, View.OnClickListener onClickListener, boolean underlineText) {
        if (StrUtils.isEmpty(content)) return;
        content = content.replaceAll("\r\n", "<br/>");
        Spannable sp = (Spannable) Html.fromHtml(content);
        URLSpan[] urls = sp.getSpans(0, sp.length(), URLSpan.class);
        SpannableStringBuilder style = new SpannableStringBuilder(sp);
        style.clearSpans();
        for (URLSpan url : urls) {
            HtmlClickSpan myURLSpan = new HtmlClickSpan(url.getURL(), color, onClickListener, underlineText);
            style.setSpan(myURLSpan, sp.getSpanStart(url), sp.getSpanEnd(url), Spannable.SPAN_EXCLUSIVE_INCLUSIVE);
        }
        rich_tvmsg.setText(style);
        if (onClickListener != null) {
            rich_tvmsg.setMovementMethod(LinkMovementMethod.getInstance());
        }
    }

    /**
     * 根据最大值格式化数字（不带后缀、默认最大值9999 eg：9999+）
     *
     * @param number 给定数字
     * @return 格式化后文字
     */
    public static String formatMaxNum(String number) {
        return formatMaxNum(number, 9999, "");
    }

    /**
     * 根据最大值格式化数字（带后缀、默认最大值9999 eg：9999+阅读）
     *
     * @param number 给定数字
     * @param endTxt 格式化后文字后缀 （eg: 9999+阅读）
     * @return 格式化后文字
     */
    public static String formatMaxNum(String number, String endTxt) {
        return formatMaxNum(number, 9999, endTxt);
    }

    /**
     * 根据最大值格式化数字（无后缀 eg: 999+）
     *
     * @param number 给定数字
     * @param max    最大值
     * @return 格式化后文字
     */
    public static String formatMaxNum(String number, int max) {
        return formatMaxNum(number, max, "");
    }

    /**
     * 根据最大值格式化数字（带后缀 eg：999+阅读）
     * number为空时候显示（-- + 后缀）
     *
     * @param number 给定数字
     * @param max    最大值
     * @param endTxt 格式化后文字后缀 （eg: 999+阅读）
     * @return 格式化后文字
     */
    public static String formatMaxNum(String number, int max, String endTxt) {
        return formatMaxNum("", number, max, endTxt);
    }

    /**
     * 根据最大值格式化数字（带前缀后缀 eg：xxx999+阅读）
     * number为空时候显示（前缀 + -- + 后缀）
     *
     * @param startTxt 格式化后文字前缀 （eg: xxx999+）
     * @param number   给定数字
     * @param max      最大值
     * @param endTxt   格式化后文字后缀 （eg: 999+阅读）
     * @return 格式化后文字
     */
    public static String formatMaxNum(String startTxt, String number, int max, String endTxt) {
        if (TextUtils.isEmpty(number)) {
            return startTxt + "--" + endTxt;
        } else {
            if (MathUtils.forValF(number, 0f) > max) {
                return startTxt + max + "+" + endTxt;
            } else {
                return startTxt + number + endTxt;
            }
        }
    }

    /**
     * 资讯页面-格式化阅读数显示 为0不显示
     *
     * @param tv       TextView
     * @param countStr 阅读数
     * @return 格式化后的内容
     */
    public static String setReadCount(TextView tv, String countStr) {
        int count = 0;
        String readCount;
        try {
            count = Integer.parseInt(countStr);
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        if (count == 0) {
            ViewUtils.setVisibility(tv, View.GONE);
        }
        readCount = count > 9999 ? "9999+" : String.valueOf(count);
        if (tv != null) {
            tv.setText("阅读数(" + readCount + ")");
        }
        return readCount;
    }

    /**
     * 格式化日期 为空默认显示--（今年格式、以往格式）
     *
     * @param tv      TextView
     * @param date    数据
     * @param format1 数据的形式
     * @param format2 今年的格式
     * @param format3 以往的格式
     * @return 格式化后的文字
     */
    public static String setDate(TextView tv, String date, String format1, String format2, String format3) {
        if (TextUtils.isEmpty(date)) {
            date = ValConfig.NULL_TXT1;
        } else {
            long timeLong = DateUtils.getTimeFormatLong(date, format1);
            Calendar ca = Calendar.getInstance();
            Calendar curCalendar = Calendar.getInstance();
            curCalendar.setTimeInMillis(System.currentTimeMillis());
            ca.setTimeInMillis(timeLong);
            if (curCalendar.get(Calendar.YEAR) == ca.get(Calendar.YEAR)) {
                date = DateUtils.timeFormat(date, format1, format2);
            } else {
                date = DateUtils.timeFormat(date, format1, format3);
            }
        }
        if (tv != null) {
            tv.setText(date);
        }
        return date;
    }

    /**
     * 格式化日期 为空默认显示--（今天、昨天、今年格式、以往格式）
     *
     * @param date    数据
     * @param format1 数据的形式
     * @param format2 今年的格式
     * @param format3 以往的格式
     * @return 格式化后的文字
     */
    public static String getDateDes(String date, String format1, String format2, String format3) {
        if (TextUtils.isEmpty(date)) {
            date = ValConfig.NULL_TXT1;
        } else {
            try {
                long timeLong = DateUtils.getTimeFormatLong(date, format1);
                long curLong = System.currentTimeMillis();
                Calendar ca = Calendar.getInstance();
                Calendar curCalendar = Calendar.getInstance();
                curCalendar.setTimeInMillis(curLong);
                ca.setTimeInMillis(timeLong);
                String jt = DateUtils.timeFormat(curLong, DateUtils.DATEF_YMD);
                String zt = DateUtils.timeFormat(curLong - 86400000, DateUtils.DATEF_YMD);
                String dd = DateUtils.timeFormat(timeLong, DateUtils.DATEF_YMD);
                if (!TextUtils.isEmpty(jt) && StrUtils.equals(jt, dd)) {
                    return "今天";
                } else if (!TextUtils.isEmpty(zt) && StrUtils.equals(zt, dd)) {
                    return "昨天";
                } else if (curCalendar.get(Calendar.YEAR) == ca.get(Calendar.YEAR)) {
                    date = DateUtils.timeFormat(date, format1, format2);
                } else {
                    date = DateUtils.timeFormat(date, format1, format3);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return date;
    }

    /**
     * 格式化日期 为空默认显示--（今天 HH:mm、昨天 HH:mm、今年格式、以往格式）
     *
     * @param date    数据
     * @param format1 数据的形式
     * @param format2 今年的格式
     * @param format3 以往的格式
     * @return 格式化后的文字
     */
    public static String setDateTime(TextView tv, String date, String format1, String format2, String format3) {
        if (!TextUtils.isEmpty(date)) {
            try {
                long timeLong = DateUtils.getTimeFormatLong(date, format1);
                long curLong = System.currentTimeMillis();
                Calendar ca = Calendar.getInstance();
                Calendar curCalendar = Calendar.getInstance();
                curCalendar.setTimeInMillis(curLong);
                ca.setTimeInMillis(timeLong);
                String jt = DateUtils.timeFormat(curLong, DateUtils.DATEF_YMD);//今天
                String zt = DateUtils.timeFormat(curLong - 86400000, DateUtils.DATEF_YMD);//昨天
                String dd = DateUtils.timeFormat(timeLong, DateUtils.DATEF_YMD);//传入的日期
                String HH_mm = DateUtils.timeFormat(timeLong, DateUtils.DATEF_HM_);//小时分钟
                if (!TextUtils.isEmpty(jt) && StrUtils.equals(jt, dd)) {
                    date = "今天 " + HH_mm;
                } else if (!TextUtils.isEmpty(zt) && StrUtils.equals(zt, dd)) {
                    date = "昨天 " + HH_mm;
                } else if (curCalendar.get(Calendar.YEAR) == ca.get(Calendar.YEAR)) {//今年
                    date = DateUtils.timeFormat(date, format1, format2);
                } else {//非今年
                    date = DateUtils.timeFormat(date, format1, format3);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (tv != null) {
            tv.setText(date);
        }
        return date;
    }

    /**
     * 今年   MM-dd
     * 非今年 yyyy-MM-dd
     */
    public static String formatDate(String date, String formatIn) {
        return formatDate(date, formatIn, "MM-dd", "yyyy-MM-dd");
    }

    /**
     * 今年   formatOut
     * 非今年 formatOutFull
     */
    public static String formatDate(String date, String formatIn, String formatOut, String formatOutFull) {
        if (TextUtils.isEmpty(date)) return "--";
        String currentYear = DateUtils.getYear() + "";
        String year = DateUtils.dateFormat(date, formatIn, "yyyy");
        if (TextUtils.equals(year, currentYear)) {
            return DateUtils.timeFormat(date, formatIn, formatOut);
        } else {
            return DateUtils.timeFormat(date, formatIn, formatOutFull);
        }
    }
}
