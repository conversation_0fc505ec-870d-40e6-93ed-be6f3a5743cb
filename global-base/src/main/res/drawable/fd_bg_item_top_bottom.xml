<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!--顶部+底部边框(有pressed效果)-->
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/fd_item_frame"/>
                    <padding
                        android:bottom="@dimen/fd_frame_width"
                        android:top="@dimen/fd_frame_width"/>
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/fd_item_pressed"/>
                </shape>
            </item>
        </layer-list>
    </item>
    <item android:state_enabled="false">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/fd_item_frame"/>
                    <padding
                        android:bottom="@dimen/fd_frame_width"
                        android:top="@dimen/fd_frame_width"/>
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/fd_item_unable"/>
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/fd_item_frame"/>
                    <padding
                        android:bottom="@dimen/fd_frame_width"
                        android:top="@dimen/fd_frame_width"/>
                </shape>
            </item>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/white"/>
                </shape>
            </item>
        </layer-list>
    </item>
</selector>