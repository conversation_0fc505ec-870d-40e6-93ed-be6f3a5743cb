<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" android:exitFadeDuration="@android:integer/config_shortAnimTime">
<!--    <item android:drawable="@drawable/bg_tab_right_cornes_sm_eb4547" android:state_enabled="true" android:state_pressed="true" />-->
    <item android:drawable="@drawable/bg_tab_right_cornes_sm_eb4547" android:state_checked="true" android:state_enabled="true" />
    <item android:drawable="@drawable/sm_img_disable_bg" android:state_enabled="false" />
    <item android:drawable="@android:color/transparent" android:state_enabled="true" />

</selector>