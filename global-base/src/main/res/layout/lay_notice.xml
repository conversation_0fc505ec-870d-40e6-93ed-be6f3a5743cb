<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/lay_notice"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_notice_yellow"
    android:gravity="center_vertical"
    android:minHeight="35dp"
    android:paddingLeft="15dp"
    android:paddingRight="5dp"
    android:visibility="visible">

    <com.howbuy.fund.base.widget.AlwaysMarqueeTextView
        android:id="@+id/tv_notice_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_weight="1"
        android:clickable="true"
        android:ellipsize="marquee"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:onClick="onXmlBtClick"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/cl_fd9425"
        android:textSize="12sp"
        tools:text="消息推送已关闭,将无法及时接收基金动态,最新活动等信息。去开启" />

    <ImageButton
        android:id="@+id/ib_notice_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:minHeight="34dp"
        android:onClick="onXmlBtClick"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:src="@drawable/icon_home_notice_close" />

</LinearLayout>