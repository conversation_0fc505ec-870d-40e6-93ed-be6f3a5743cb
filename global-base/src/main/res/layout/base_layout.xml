<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_height"
        android:background="?attr/colorPrimary"
        android:navigationIcon="@drawable/ic_action_back"
        app:navigationIcon="@drawable/ic_action_back"
        app:popupTheme="@style/ThemeOverlay.AppCompat"
        app:theme="@style/ThemeOverlay.AppCompat.Light">

        <!--针对简单的 Text Title 样式-->
        <TextView
            android:id="@+id/toolbar_title"
            style="@style/TextAppearance.AppCompat.Widget.ActionBar.Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/margin_15"
            android:layout_marginEnd="@dimen/margin_15"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/fd_title"
            android:textSize="18sp" />

        <!--针对 SegmentGroup 样式 的title-->
        <FrameLayout
            android:id="@+id/lay_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center" />
    </androidx.appcompat.widget.Toolbar>

    <View
        android:id="@+id/toolbar_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/fd_min_width"
        android:layout_below="@id/toolbar"
        android:background="@color/fd_vertical_line" />

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar_line" />

</RelativeLayout>