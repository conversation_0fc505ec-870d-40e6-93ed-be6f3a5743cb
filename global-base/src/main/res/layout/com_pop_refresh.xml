<?xml version="1.0" encoding="UTF-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:gravity="center_horizontal"
              android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:duplicateParentState="true"
        android:gravity="center_horizontal"
        android:text="网络加载失败"
        android:textColor="#999"
        android:textSize="24sp"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="19dp"
        android:duplicateParentState="true"
        android:gravity="center_horizontal"
        android:text="请确保信号良好，然后刷新试试"
        android:textColor="#999"
        android:textSize="17sp"/>

    <com.howbuy.component.widgets.DrawableCenterTextView
        android:id="@+id/tv_refresh"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="34dp"
        android:background="@drawable/xml_bt_trade_orange"
        android:clickable="true"
        android:drawableLeft="@drawable/icon_refresh"
        android:gravity="center_vertical"
        android:minWidth="120dp"
        android:onClick="onXmlBtClick"
        android:paddingBottom="10dp"
        android:paddingTop="10dp"
        android:text=" 刷新"
        android:textColor="#fff"
        android:textSize="17sp"/>
    <!--xml_ib_back_gray-->
</LinearLayout>
 