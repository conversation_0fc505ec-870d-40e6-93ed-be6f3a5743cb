<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/lay_download_status"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#7c97ff"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="安全证书加载"
        android:textColor="#ffffff"
        android:textSize="20sp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="48dp"
        android:layout_marginTop="@dimen/window_margin"
        android:layout_marginRight="48dp"
        android:text="尊敬的用户，为了您的账户安全，根据监管规定，正在为您加载安全证书。"
        android:textColor="#ffffff"
        android:textSize="15sp" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/window_margin"
        android:src="@drawable/gm_safe_loading"
        tools:ignore="MissingClass" />

    <TextView
        android:id="@+id/tvProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/window_margin"
        android:gravity="left"
        android:text="已加载 1%"
        android:textColor="#ffffff"
        android:textSize="22sp" />

    <ProgressBar
        android:id="@+id/progress"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="255dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/window_margin"
        ></ProgressBar>

    <TextView
        android:id="@+id/tvDownCertTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="48dp"
        android:layout_marginTop="@dimen/window_margin"
        android:layout_marginRight="48dp"
        android:text="同时已为您配置PIN码,（初始PIN码是您的证件号后6位，字母用数字1代替）.可至App的安全中心进行修改."
        android:textColor="#ffffff"
        android:textSize="15sp"
        android:visibility="gone"
        />
</LinearLayout>
