plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'

    id 'com.alibaba.arouter' //arouter
}

apply from: "${rootProject.projectDir}/arouter-kotlin-config.gradle"

android {
    namespace 'com.howbuy.fund.base'
    compileSdkVersion rootProject.ext.commonVersions.compileSdkVersion
    buildToolsVersion rootProject.ext.commonVersions.buildToolsVersion
    defaultConfig {
        minSdkVersion rootProject.ext.commonVersions.minSdkVersion
        targetSdkVersion rootProject.ext.commonVersions.targetSdkVersion
    }
    packagingOptions {
        exclude 'META-INF/beans.xml'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation project(':login-api')
    implementation project(path: ':analytics_api')
    implementation project(path: ':arch')

    rootProject.ext.depUtil.addDependency('hBComponent', dependencies)
    rootProject.ext.depUtil.api('hBLCommon', dependencies)
    implementation project(path:':hBRouter')
//    rootProject.ext.depUtil.addDependency('account_api', dependencies)

    rootProject.ext.depUtil.api('arch', dependencies)
//    rootProject.ext.depUtil.addDependency('share_api', dependencies)
    rootProject.ext.depUtil.implementation('agentweb_api', dependencies)

    rootProject.ext.depUtil.implementation("net_api", dependencies)
//    rootProject.ext.depUtil.addDependency('analytics_api', dependencies)



//    implementation rootProject.ext.dependencies["support-multidex"]
//    implementation rootProject.ext.dependencies["support:recyclerview"]
//    implementation rootProject.ext.dependencies['constraint-layout']
//    api("androidx.lifecycle:lifecycle-runtime:2.2.0") { //AGP7.1版本，必须2.2.0以上的lifecycle，否则将打包失败
//        force(true)
//    }
    api "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation rootProject.ext.dependencies['hb-dialog']
    implementation rootProject.ext.dependencies['hb-refresh']
    implementation rootProject.ext.dependencies['hb-imgloader']
    implementation rootProject.ext.dependencies['hb-h5zip']
    implementation rootProject.ext.dependencies['hb-gesture']
//    implementation(rootProject.ext.dependencies["permission"])
//    implementation rootProject.ext.dependencies['lifecycle-extensions']
//    implementation(rootProject.ext.dependencies['loginobserver'])
    implementation rootProject.ext.dependencies["constraint-layout"]
    implementation(rootProject.ext.dependencies['hb-analysis'])
    //recyclerview的databinding套装
    implementation(rootProject.ext.dependencies['bindingcollectionadapter'])
    implementation(rootProject.ext.dependencies['bindingcollectionadapter-recyclerview'])
    //万能adapter
    implementation(rootProject.ext.dependencies["recyclerView-adapter-helper"])
    //wall 多渠道打包
    implementation rootProject.ext.dependencies['walle']
    implementation rootProject.ext.dependencies['delayclick_annotation']
//    implementation rootProject.ext.dependencies['push_hub']
    implementation rootProject.ext.dependencies['user_data_api']

    implementation rootProject.ext.dependencies["activity-guard-api"]   //该模块必须和插件‘com.howbuy.android.activity-guard’一起使用
    implementation(rootProject.ext.dependencies["activity-guard-core"]) //该模块必须和插件‘com.howbuy.android.activity-guard’一起使用


    //PV埋点注解
    implementation rootProject.ext.dependencies['analytics-annotation']
//banner
//    implementation rootProject.ext.dependencies['hbBanner']
//    //网络诊断
    implementation rootProject.ext.dependencies['net_diagnose']


    //解除依赖传递---------------------------------------
    implementation(rootProject.ext.dependencies['hb-net'])
    implementation rootProject.ext.dependencies['rxjava']
    implementation(rootProject.ext.dependencies['rxandroid'])

    //vhall直播
    api 'com.github.vhall.android.library:vh-saas-sdk:6.19.3'
    api 'com.github.vhall.android.library:vh-saas-interactive:6.19.3'
    //投屏
    api 'com.github.vhall.android.library:vh-saas-sdk-support:2.0.1'
    api "org.eclipse.jetty.orbit:javax-servlet:3.0.0.v201112011016"
}