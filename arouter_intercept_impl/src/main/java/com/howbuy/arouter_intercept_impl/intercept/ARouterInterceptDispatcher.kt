package com.howbuy.arouter_intercept_impl.intercept

import android.content.Context
import androidx.core.util.Consumer
import com.howbuy.arouter_intercept_api.ARouterInterceptNode
import com.howbuy.arouter_intercept_api.IInterceptTreatment
import com.howbuy.arouter_intercept_api.INavEventListener
import com.howbuy.lib.utils.LogUtils
import com.howbuy.router.proxy.Invoker

private const val TAG = "ARouterInterceptDispatcher"

/**
 *@desc 拦截实现
 *<AUTHOR>
 *@date 2023/07/31
 **/
open class ARouterInterceptDispatcher(interceptArrayPara: IntArray?, val context: Context?) {

    private var interceptArray: IntArray? = null

    private var interceptCode = 0
    private var interceptNode: ARouterInterceptNode? = null

    init {
        val filterInterceptLit = mutableListOf<Int>()
        val interceptCodeList = mutableListOf<Int>()
        interceptArrayPara?.forEachIndexed { _, i ->
            val interceptCodeSplitList = Invoker.getInstance().navigation(IInterceptTreatment::class.java).dispatchInterceptCode(i)
            interceptCodeList.addAll(interceptCodeSplitList.first)
            filterInterceptLit.addAll(interceptCodeSplitList.second)
        }

        interceptArray = filterInterceptLit.toIntArray()

        interceptCodeList.forEach {
            interceptCode = interceptCode or it
        }
        interceptNode = ARouterInterceptNode(context)
        interceptNode?.interceptCode = interceptCode
        //LogUtils.d(TAG, "最终的拦截码 = ${interceptArray.contentToString()}, 拦截码 = $interceptCode")
    }




    /**
     * 执行拦截分发
     * @param consumer true继续往下执行
     */
    fun execute(consumer: Consumer<Boolean>?) {
        if (interceptArray == null || interceptArray!!.isEmpty()) {
            consumer?.accept(true)
            return
        }

        var navEventListenerTemp: INavEventListener? = null
        //添加监听成功(重复或者失败都是false)
        var addTreatListenerResult = false

        val allFinishCallback = Runnable {
            //LogUtils.d(TAG, "所有拦截执行完毕-继续跳转-navEventListenerTemp为null = ${navEventListenerTemp == null}, 添加监听结果-${addTreatListenerResult}")
            if (addTreatListenerResult){
                val removeTreatListener = Invoker.getInstance().navigation(IInterceptTreatment::class.java).removeTreatListener(interceptCode)
                //LogUtils.d(TAG, "所有拦截执行完毕-移除监听-$removeTreatListener")
            }
            Invoker.getInstance().navigation(IInterceptTreatment::class.java).launchComplete(interceptCode)
            consumer?.accept(true)
        }
        val navEventListener = object : INavEventListener {
            init {
                navEventListenerTemp = this
            }
            override fun  onIntercept() {
                LogUtils.d(TAG, "跳转中断")
                consumer?.accept(false)
            }

            override fun complete(interceptCode: Int, allInterceptCode: Int?) {

                //不是当前的拦截码
                if (allInterceptCode != <EMAIL>) {
                    return
                }
                //完成了某个拦截处理
                val elementIndex = interceptArray?.indexOf(interceptCode) ?: -1
                //LogUtils.d(TAG, "单个拦截完成-interceptCode = $interceptCode, 完成index = $elementIndex, size = ${interceptArray?.size}")
                if (elementIndex == -1) {
                    //不存在的拦截码,中断
                    consumer?.accept(false)
                    //Throwable("intercept by user with interceptArray = ${Arrays.toString(interceptArray)}, but interceptCode = $interceptCode")
                    Invoker.getInstance().navigation(IInterceptTreatment::class.java).removeTreatListener(<EMAIL>)
                } else {

                    if (elementIndex == (interceptArray?.size ?: 0) - 1){
                        //LogUtils.d(TAG, "所有拦截执行完毕")
                        allFinishCallback.run()
                    } else {
                        //LogUtils.d(TAG, "准备执行下一个-${elementIndex + 1}, code = ${interceptArray?.getOrNull(elementIndex + 1)}")
                        executeIntercept(interceptArray, elementIndex + 1, allFinishCallback)
                    }

                }
            }
        }

        addTreatListenerResult = Invoker.getInstance().navigation(IInterceptTreatment::class.java).addTreatListener(navEventListener, interceptCode)
        executeIntercept(interceptArray, 0, allFinishCallback)
    }

    private fun executeIntercept(intArray: IntArray?, startIndex: Int, allFinishCallback: Runnable? = null){
        if (intArray?.isEmpty() == true){
            allFinishCallback?.run()
            return
        }
        val element = intArray?.getOrNull(startIndex) ?: -1
        if (element == -1) {
            allFinishCallback?.run()
            return
        }
        Invoker.getInstance().navigation(IInterceptTreatment::class.java).treatment(element, interceptNode)

    }




}