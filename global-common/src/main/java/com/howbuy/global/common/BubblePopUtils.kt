package com.howbuy.global.common

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.text.TextUtils
import android.view.*
import android.widget.PopupWindow
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.listener.OnItemClickListener
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.component.widgets.MyPopupWindow
import com.howbuy.fund.base.widget.BubbleView
import com.howbuy.fund.base.widget.MaxHeightRecyclerView
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.SysUtils
import com.howbuy.lib.utils.ViewUtils
import kotlin.math.min


/**
 * @Description 气泡提示文案popwindows
 * <AUTHOR>
 * @Date 2022/6/8 11:25
 * @Version V785
 */
object BubblePopUtils {

    /**
     * 在控件顶部居中显示提示文案
     */
    fun showTip(context: Context, anchorView: View, maxWidth: Float, content: String?) {
        if (TextUtils.isEmpty(content)) return
        val popupView: View = View.inflate(context, R.layout.pop_txt, null)
        val tv = popupView.findViewById<View>(R.id.tv) as TextView
        tv.text = content
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        mPopupWindow.showAsDropDown(
            anchorView,
            (min(anchorSize[0], maxWidth.toInt()) - popSize[0]) / 2,
            -popSize[1] - anchorSize[1]
        )
    }

    /**
     * 持债明细占比pop
     * anchorView是通过canvas绘制的,比较特殊
     *
     */
    fun showTipsInCzmx(context: Context, anchorView: View, maxWidth: Float, content: String?, offX: Int, offY: Int) {
        if (TextUtils.isEmpty(content)) return
        val popupView: View = View.inflate(context, R.layout.pop_txt, null)
        val tv = popupView.findViewById<View>(R.id.tv) as TextView
        tv.text = content
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.isFocusable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        mPopupWindow.showAtLocation(anchorView, Gravity.TOP, offX, offY)

    }

    /**
     * @param handleAction 自己计算xOff
     */
    fun showTip(context: Context, anchorView: View, content: String?, handleAction: ((Int, Int) -> Int)? = null) {
        if (TextUtils.isEmpty(content)) return
        val popupView: View = View.inflate(context, R.layout.pop_txt, null)
        val tv = popupView.findViewById<View>(R.id.tv) as TextView
        tv.text = content
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        val popSize = IntArray(2)

        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        val xOff = handleAction?.invoke(popSize[0], anchorSize[0]) ?: 0
        mPopupWindow.showAsDropDown(anchorView, xOff, -popSize[1] - anchorSize[1])
    }

    fun showWhiteCenterTips(
        context: Context?,
        anchorView: View,
        content: String?
    ) {
        if (TextUtils.isEmpty(content)) return
        showCenterTips(
            context, anchorView,
            ColorUtils.parseColor("#ffffff"),
            ColorUtils.parseColor("##262a3050"),
            ColorUtils.parseColor("#000000"),
            content
        )
    }

    fun showBlackCenterTips(
        context: Context,
        anchorView: View,
        content: String?
    ) {
        showCenterTips(
            context, anchorView,
            ColorUtils.parseColor("#cc000000"),
            ColorUtils.parseColor("#cc000000"),
            ColorUtils.parseColor("#ffffff"),
            content
        )
    }

    /**
     * 提示气泡
     * 默认显示在锚点控件上方，中间对齐，左侧显示不下则改为左对齐，右侧显示不下则右对齐，箭头在锚点控件中间
     */
    fun showCenterTips(
        context: Context?,
        anchorView: View,
        bgColor: Int,
        bgShadowColor: Int,
        txtColor: Int,
        content: String?
    ) {
        if (TextUtils.isEmpty(content)) return
        val popupView: View = View.inflate(context, R.layout.pop_black_txt, null)
        val bubble = popupView.findViewById<BubbleView>(R.id.bubble)
        bubble.bubbleColor = bgColor
        bubble.shadowColor = bgShadowColor
        bubble.indicatorHeight = 5
        bubble.indicatorWidth = 10
        val tv = popupView.findViewById<View>(R.id.tv) as TextView
        tv.setTextColor(txtColor)
        tv.text = content
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context?.resources, null as Bitmap?))
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        anchorSize[0] = anchorView.right - anchorView.left
        anchorSize[1] = anchorView.bottom - anchorView.top
        //判断anchorView是在屏幕左半边还是右半边
        val screenWidth = SysUtils.getWidth(context)
        val anchorLocation = IntArray(2)
        anchorView.getLocationOnScreen(anchorLocation)
        tv.maxWidth = screenWidth - DensityUtils.dp2px(25f)
        var xOff = 0
        if (anchorLocation[0] + anchorSize[0] / 2 < popSize[0] / 2) {
            //左侧 pop靠左显示 pop箭头在左侧
            val maxWidth = min(popSize[0], screenWidth - anchorLocation[0])
            tv.maxWidth = maxWidth - DensityUtils.dp2px(15f)
            ViewUtils.measureView(popupView, 0, 0, popSize)
            val x: Int = DensityUtils.px2dp(anchorSize[0] / 2f - bubble.indicatorWidth).toInt()
            if (x <= 0) {
                bubble.setIndicatorLocation(1)
                xOff = -anchorSize[0]
            } else {
                bubble.setIndicatorLocation(x)
                xOff = 0
            }
        } else if (anchorLocation[0] + anchorSize[0] / 2 > (screenWidth - popSize[0] / 2)) {
            val maxWidth = min(popSize[0], anchorLocation[0] + anchorSize[0])
            tv.maxWidth = maxWidth - DensityUtils.dp2px(15f)
            ViewUtils.measureView(popupView, 0, 0, popSize)
            //右侧
            val x: Int = DensityUtils.px2dp(anchorSize[0] / 2f - bubble.indicatorWidth).toInt()
            if (x <= 0) {
                bubble.setIndicatorLocation(-1)
                xOff = -(popSize[0] - anchorSize[0] - bubble.indicatorWidth / 2)
            } else {
                bubble.setIndicatorLocation(-x)
                xOff = -(popSize[0] - anchorSize[0])
            }
        } else {
            //中间 pop居中显示 pop箭头在中间
            bubble.setIndicatorLocation(0.5f)
            xOff = -(popSize[0] - anchorSize[0]) / 2
        }
        val yOff = if (anchorLocation[1] <= popSize[1]) {
            //锚点控件顶部空间不够，pop显示在锚点控件下方
            bubble.indicatorDirection = BubbleView.IndicatorDirection.TOP
            0
        } else {
            bubble.indicatorDirection = BubbleView.IndicatorDirection.BOTTOM
            -popSize[1] - anchorSize[1]
        }
        mPopupWindow.showAsDropDown(anchorView, xOff, yOff)
    }

    /**
     * 控件顶部显示tips，黑色半透明背景，指示箭头位置可设置
     * @param popGravity  Gravity 左对齐  居中  右对齐
     * @param popXOffset  pop 横向偏移距离 popGravity 左对齐则为右移距离 右对齐则为左移距离 居中不生效 单位：pd
     * @param arrowLocation 气泡箭头位置 0:中间，正数：从开始位置向中间偏移，负数：从结束位置中间偏移 单位：pd
     */
    @SuppressLint("RtlHardcoded")
    fun showBlackTopTips(
        context: Context,
        anchorView: View,
        content: String?,
        popGravity: Int,
        popXOffset: Float,
        arrowLocation: Int,
    ) {
        if (TextUtils.isEmpty(content)) return
        val popupView: View = View.inflate(context, R.layout.pop_black_txt, null)
        val bubble = popupView.findViewById<BubbleView>(R.id.bubble)
        bubble.setIndicatorLocation(arrowLocation)
        val tv = popupView.findViewById<View>(R.id.tv) as TextView
        tv.text = content
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        val xoff = when (popGravity) {
            Gravity.LEFT, Gravity.START -> {
                DensityUtils.dip2px(popXOffset)
            }

            Gravity.RIGHT, Gravity.END -> {
                (anchorSize[0] - popSize[0]) - DensityUtils.dip2px(popXOffset)
            }

            else -> {
                (anchorSize[0] - popSize[0]) / 2
            }
        }
        mPopupWindow.showAsDropDown(anchorView, xoff, -popSize[1] - anchorSize[1])
    }

    fun showListMenu(
        context: Context,
        anchorView: View,
        popGravity: Int,
        popXOffset: Float,
        popYOffset: Float,
        arrowLocation: Int,
        adapter: BaseQuickAdapter<*, BaseViewHolder>,
        mListener: OnItemClickListener?,
        onDismissListener: PopupWindow.OnDismissListener? = null
    ) {
        showListMenu(
            context,
            anchorView,
            popGravity,
            popXOffset,
            popYOffset,
            arrowLocation,
            adapter,
            mListener,
            onDismissListener,
            -1
        )
    }

    /**
     * 下拉pop菜单
     * @param popGravity  Gravity 左对齐  居中  右对齐
     * @param popXOffset  pop 横向偏移距离 popGravity 左对齐则为右移距离 右对齐则为左移距离 居中不生效 单位：pd
     * @param popYOffset  pop 纵向偏移距离 大于0 下移 小于0 上移 单位：pd
     * @param arrowLocation 气泡箭头位置 0:中间，正数：从开始位置向中间偏移，负数：从结束位置中间偏移 单位：pd
     */
    @SuppressLint("RtlHardcoded")
    fun showListMenu(
        context: Context,
        anchorView: View,
        popGravity: Int,
        popXOffset: Float,
        popYOffset: Float,
        arrowLocation: Int,
        adapter: BaseQuickAdapter<*, BaseViewHolder>,
        mListener: OnItemClickListener?,
        onDismissListener: PopupWindow.OnDismissListener? = null,
        maxHeight: Int = -1
    ) {
        val popupView: View = View.inflate(context, R.layout.pop_list_menu, null)
        val bubble = popupView.findViewById<BubbleView>(R.id.bubble)
        bubble.setIndicatorLocation(arrowLocation)
        val rv = popupView.findViewById<MaxHeightRecyclerView>(R.id.rv)
        rv.apply {
            layoutManager = LinearLayoutManager(context)
            addItemDecoration(
                XLinearBuilder(context).setSpacing(0.5f).setColor(ContextCompat.getColor(context, R.color.cl_cccccc))
                    .build()
            )
            setAdapter(adapter)
            if (maxHeight > 0) {
                setMaxHeight(maxHeight)
            }
        }
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        adapter.setOnItemClickListener { a, view, position ->
            mPopupWindow.dismiss()
            mListener?.onItemClick(a, view, position)
        }
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        mPopupWindow.setOnDismissListener(onDismissListener)
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        val anchorViewLoc = IntArray(2)
        anchorView.getLocationOnScreen(anchorViewLoc)
        val xoff = when (popGravity) {
            Gravity.LEFT, Gravity.START -> {
                DensityUtils.dip2px(popXOffset)
            }

            Gravity.RIGHT, Gravity.END -> {
                (anchorSize[0] - popSize[0]) - DensityUtils.dip2px(popXOffset)
            }

            else -> {
                (anchorSize[0] - popSize[0]) / 2
            }
        }
        if (SysUtils.getHeight(context) - anchorViewLoc[1] - anchorSize[1] - popSize[1] > DensityUtils.dp2px(20f)) {
            //屏幕底部空间足够 朝下显示
            mPopupWindow.showAsDropDown(anchorView, xoff, DensityUtils.dip2px(popYOffset))
        } else {
            //屏幕底部空间不够 朝上显示
            bubble.indicatorDirection = BubbleView.IndicatorDirection.BOTTOM
            mPopupWindow.showAsDropDown(anchorView, xoff, -popSize[1] - anchorSize[1] - DensityUtils.dip2px(popYOffset))
        }
    }

    /**
     * 私募优选tab下拉选择
     */
    fun showSmBestTabSelect(
        context: Context, anchorView: View, popGravity: Int, popXOffset: Float, popYOffset: Float,
        arrowLocation: Int, selectIndex: Int, adapter: BaseQuickAdapter<*, BaseViewHolder>,
        mListener: OnItemClickListener?, onDismissListener: PopupWindow.OnDismissListener? = null
    ) {
        val popupView: View = View.inflate(context, R.layout.pop_list_menu, null)
        val bubble = popupView.findViewById<BubbleView>(R.id.bubble)
        bubble.setIndicatorLocation(arrowLocation)
        bubble.setBubbleColor(ColorUtils.parseColor("#E6303133"))
        bubble.indicatorHeight = 6
        bubble.indicatorWidth = 12
        val rv = popupView.findViewById<MaxHeightRecyclerView>(R.id.rv)
        rv.apply {
            layoutManager = LinearLayoutManager(context)
            setAdapter(adapter)
            setMaxHeight(DensityUtils.dp2px(250f))
            if (selectIndex > 0) {
                ((layoutManager) as? LinearLayoutManager)?.scrollToPositionWithOffset(selectIndex, DensityUtils.dip2px(15f))
            }
        }
        val mPopupWindow =
            MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        adapter.setOnItemClickListener { a, view, position ->
            mPopupWindow.dismiss()
            mListener?.onItemClick(a, view, position)
        }
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        mPopupWindow.setOnDismissListener(onDismissListener)
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        val anchorViewLoc = IntArray(2)
        anchorView.getLocationOnScreen(anchorViewLoc)
        val xoff = when (popGravity) {
            Gravity.LEFT, Gravity.START -> {
                DensityUtils.dip2px(popXOffset)
            }

            Gravity.RIGHT, Gravity.END -> {
                (anchorSize[0] - popSize[0]) - DensityUtils.dip2px(popXOffset)
            }

            else -> {
                (anchorSize[0] - popSize[0]) / 2
            }
        }
        if (SysUtils.getHeight(context) - anchorViewLoc[1] - anchorSize[1] - popSize[1] > DensityUtils.dp2px(20f)) {
            //屏幕底部空间足够 朝下显示
            mPopupWindow.showAsDropDown(anchorView, xoff, DensityUtils.dip2px(popYOffset))
        } else {
            //屏幕底部空间不够 朝上显示
            bubble.indicatorDirection = BubbleView.IndicatorDirection.BOTTOM
            mPopupWindow.showAsDropDown(anchorView, xoff, -popSize[1] - anchorSize[1] - DensityUtils.dip2px(popYOffset))
        }
    }

    fun showBubblePop(context: Context, anchorView: View, listener: PopClickListener?, popLayout: Int, viewInitBlock: ((View) -> Unit)?) {
        val popupView = LayoutInflater.from(context).inflate(popLayout, null)
        val mPopupWindow = MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        viewInitBlock?.invoke(popupView)
        popupView.setOnClickListener {
            listener?.onClick(popupView)
            mPopupWindow.dismiss()
        }
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        mPopupWindow.showAsDropDown(anchorView, -popSize[0] + anchorSize[0] / 2, 0)
    }

    /**
     * 控件顶部显示tips，黑色半透明背景，指示箭头位置可设置
     * @param layoutId 可以设置布局文件 必须包含R.id.tv 显示文案textView
     * @param popGravity  Gravity 左对齐  居中  右对齐
     * @param popXOffset  pop 横向偏移距离 popGravity 左对齐则为右移距离 右对齐则为左移距离 居中不生效 单位：pd
     * @param arrowLocation 气泡箭头位置 0:中间，正数：从开始位置向中间偏移，负数：从结束位置中间偏移 单位：pd
     */
    fun showPopText(
        context: Context,
        anchorView: View,
        @LayoutRes layoutId: Int = R.layout.pop_black_txt,
        content: String?,
        popGravity: Int = Gravity.CENTER,
        popXOffset: Float = 0f,
        popYOffset: Float = 0f
    ):PopupWindow? {
        if (TextUtils.isEmpty(content)) return null
        val popupView: View = View.inflate(context, layoutId, null)
        val tv = popupView.findViewById<View>(com.howbuy.fund.base.R.id.tv) as TextView
        tv.text = content
        val mPopupWindow = MyPopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true)
        mPopupWindow.isTouchable = true
        mPopupWindow.isOutsideTouchable = true
        mPopupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE //不遮盖虚拟键盘
        mPopupWindow.setBackgroundDrawable(BitmapDrawable(context.resources, null as Bitmap?))
        val popSize = IntArray(2)
        ViewUtils.measureView(popupView, 0, 0, popSize)
        val anchorSize = IntArray(2)
        ViewUtils.measureView(anchorView, 0, 0, anchorSize)
        val xoff = when (popGravity) {
            Gravity.LEFT, Gravity.START -> {
                DensityUtils.dip2px(popXOffset)
            }
            Gravity.RIGHT, Gravity.END -> {
                (anchorSize[0] - popSize[0]) - DensityUtils.dip2px(popXOffset)
            }
            else -> {
                (anchorSize[0] - popSize[0]) / 2
            }
        }
        mPopupWindow.showAsDropDown(anchorView, xoff, -popSize[1] - anchorSize[1] - DensityUtils.dip2px(popYOffset))
        return mPopupWindow
    }

    interface PopClickListener {
        fun onClick(view: View)
    }
}