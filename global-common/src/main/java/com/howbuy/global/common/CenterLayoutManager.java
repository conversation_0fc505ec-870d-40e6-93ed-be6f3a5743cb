package com.howbuy.global.common;

import android.content.Context;
import android.util.DisplayMetrics;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

/**
 * class description.
 * 点击tab, item居中
 *
 * <AUTHOR>
 * @date 2023/4/14
 */

public class CenterLayoutManager extends LinearLayoutManager {

    private int mLastPosition = 0;
    private int mTargetPosition = 0;

    private float scrollDuration = 300f;

    public CenterLayoutManager(Context context, int orientation, boolean reverseLayout) {
        super(context, orientation, reverseLayout);
    }

    public CenterLayoutManager(Context context, int orientation, boolean reverseLayout, float scrollDuration) {
        super(context, orientation, reverseLayout);
        this.scrollDuration = scrollDuration;
    }

    @Override
    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
        CenterSmoothScroller smoothScroller = new CenterSmoothScroller(recyclerView.getContext(),scrollDuration);
        smoothScroller.setTargetPosition(position);
        startSmoothScroll(smoothScroller);
    }

    public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int lastPosition, int position) {
        mLastPosition = lastPosition;
        mTargetPosition = position;
        smoothScrollToPosition(recyclerView, state, position);
    }

    public class CenterSmoothScroller extends LinearSmoothScroller {
        private float duration;

        public CenterSmoothScroller(Context context, float scrollDuration) {
            super(context);
            duration = scrollDuration;
        }

        @Override
        public int calculateDtToFit(int viewStart, int viewEnd, int boxStart, int boxEnd, int snapPreference) {
            return (boxStart + (boxEnd - boxStart) / 2) - (viewStart + (viewEnd - viewStart) / 2);
        }

        @Override
        protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
            float newDuration = duration / (Math.abs(mLastPosition - mTargetPosition));
            return newDuration / displayMetrics.densityDpi;
        }

        @Override
        protected int calculateTimeForScrolling(int dx) {
            return super.calculateTimeForScrolling(dx);
        }
    }
}