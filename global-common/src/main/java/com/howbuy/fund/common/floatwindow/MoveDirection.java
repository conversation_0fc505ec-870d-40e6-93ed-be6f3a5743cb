package com.howbuy.fund.common.floatwindow;

import androidx.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class MoveDirection {
    public static final int all = 0;
    public static final int left = 1;
    public static final int right = 2;

    @IntDef({all, left, right})
    @Retention(RetentionPolicy.SOURCE)
    @interface MoveDirection_TYPE {
    }
}