package com.howbuy.fund.common.floatwindow;

import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Build;
import android.view.View;
import android.view.WindowManager;

import com.howbuy.lib.utils.LogUtils;

public class FloatPhone extends FloatView {
    private final WindowManager mWindowManager;
    private final WindowManager.LayoutParams mLayoutParams;
    private View mView;
    private int mX, mY;
    private boolean isRemove = false;

    FloatPhone(Context applicationContext) {
        mWindowManager = (WindowManager) applicationContext.getSystemService(Context.WINDOW_SERVICE);
        mLayoutParams = new WindowManager.LayoutParams();
        mLayoutParams.format = PixelFormat.RGBA_8888;
        mLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        mLayoutParams.windowAnimations = 0;
    }

    @Override
    protected void setSize(int width, int height) {
        mLayoutParams.width = width;
        mLayoutParams.height = height;
    }

    public void setFlag(int flag){
        mLayoutParams.flags = flag;
    }

    @Override
    protected void setView(View view) {
        mView = view;
    }

    @Override
    protected void setGravity(int gravity, int xOffset, int yOffset) {
        mLayoutParams.gravity = gravity;
        mLayoutParams.x = mX = xOffset;
        mLayoutParams.y = mY = yOffset;
    }

    @Override
    public void init() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            req();
        } else {
            try {
                mLayoutParams.type = WindowManager.LayoutParams.TYPE_TOAST;
                mWindowManager.addView(mView, mLayoutParams);
            } catch (Exception e) {
                mWindowManager.removeView(mView);
                LogUtils.e("TYPE_TOAST 失败");
                req();
            }
        }
    }

    private void req() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            mLayoutParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        mWindowManager.addView(mView, mLayoutParams);
    }

    @Override
    public void remove() {
        if (mView == null){
            return;
        }
        if (mView.getWindowToken() == null){
            return;
        }
        isRemove = true;
        mWindowManager.removeView(mView);

    }

    @Override
    protected void updateX(int x) {
        if (isRemove) return;
        mLayoutParams.x = mX = x;
        mWindowManager.updateViewLayout(mView, mLayoutParams);
    }

    @Override
    public void updateXY(int x, int y) {
        if (isRemove) return;
        mLayoutParams.x = mX = x;
        mLayoutParams.y = mY = y;
        mWindowManager.updateViewLayout(mView, mLayoutParams);
    }

    @Override
    protected void updateWidth(int width) {
        if (isRemove) return;
        mLayoutParams.width = width;
        mWindowManager.updateViewLayout(mView, mLayoutParams);
    }

    @Override
    protected void updateWidthAndHeight(int width, int height) {
        if (isRemove) return;
        mLayoutParams.width = width;
        mLayoutParams.height = height;
        mWindowManager.updateViewLayout(mView, mLayoutParams);
    }

    @Override
    protected void updateFlag(int flag) {
        mLayoutParams.flags = flag;
        mWindowManager.updateViewLayout(mView, mLayoutParams);
    }

    @Override
    protected int getX() {
        return mX;
    }

    @Override
    protected int getY() {
        return mY;
    }
}