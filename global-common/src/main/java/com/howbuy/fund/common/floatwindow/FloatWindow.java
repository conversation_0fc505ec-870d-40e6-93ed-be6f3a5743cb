package com.howbuy.fund.common.floatwindow;

import android.animation.TimeInterpolator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import androidx.annotation.LayoutRes;
import androidx.annotation.MainThread;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.howbuy.fund.base.utils.Optional;
import com.howbuy.lib.utils.LogUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

public class FloatWindow {
    private static Map<String, IFloatWindow> mFloatWindowMap;
    private static IFloatWindowImpl floatWindowImpl;

    @Nullable
    public static IFloatWindow get(@NonNull String tag) {
        return mFloatWindowMap == null ? null : mFloatWindowMap.get(tag);
    }

    @SuppressLint("StaticFieldLeak")
    private static B mBuilder = null;

    @MainThread
    public static B with(@NonNull Context applicationContext) {
        return mBuilder = new B(applicationContext);
    }

    public static B getB() {
        return mBuilder;
    }

    public static void destroy(String tag) {
        if (mFloatWindowMap != null && mFloatWindowMap.get(tag) != null) {
            Objects.requireNonNull(mFloatWindowMap.get(tag)).destory(tag);
            mBuilder = null;
        }
    }

    /**
     * 销毁小窗,同时,销毁播放器
     *
     * @param tag
     */
    public static void destroyPlayer(String tag) {
        if (mFloatWindowMap != null && mFloatWindowMap.get(tag) != null) {
            Objects.requireNonNull(mFloatWindowMap.get(tag)).destoryPlayer(tag);
            mBuilder = null;
        }
    }


    public static void removeView(String tag) {
        if (mFloatWindowMap != null && mFloatWindowMap.get(tag) != null) {
            Objects.requireNonNull(mFloatWindowMap.get(tag)).remove(tag);
        }
    }

    public static class B {
        Context mApplicationContext;
        View mView;
        private int mLayoutId;
        int mWidth = ViewGroup.LayoutParams.WRAP_CONTENT;
        int mHeight = ViewGroup.LayoutParams.WRAP_CONTENT;
        int gravity = Gravity.TOP | Gravity.START;
        int xOffset;
        int yOffset;
        int mMoveType = MoveType.slide;
        int mMoveDirection = MoveDirection.right;
        boolean mCanBackground;
        int mSlideLeftMargin;
        int mSlideRightMargin;
        int mSlideTopMargin;
        int mSlideBottomMargin;
        long mDuration = 300;
        int mFlag = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                | WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON;
        TimeInterpolator mInterpolator;
        String mTag = "default_tag";
        ViewStateListener mViewStateListener;

        B(Context applicationContext) {
            mApplicationContext = applicationContext;
        }

        public B setView(@NonNull View view) {
            mView = view;
            return this;
        }

        public B setView(@LayoutRes int layoutId) {
            mLayoutId = layoutId;
            return this;
        }

        public B setWidth(int width) {
            mWidth = width;
            return this;
        }

        public B setHeight(int height) {
            mHeight = height;
            return this;
        }

        public B setXOffset(int x) {
            xOffset = x;
            return this;
        }

        public B setYOffset(int y) {
            yOffset = y;
            return this;
        }

        public B setFlag(int mFlag) {
            this.mFlag = mFlag;
            return this;
        }

        public B setMoveType(@MoveType.MOVE_TYPE int moveType) {
            mMoveType = moveType;
            return this;
        }

        public B setMoveDirection(@MoveDirection.MoveDirection_TYPE int moveDirection) {
            mMoveDirection = moveDirection;
            return this;
        }

        /**
         * 退出app是否可继显示小窗播放
         *
         * @param canBackground
         * @return
         */
        @Deprecated
        public B setCanBackground(boolean canBackground) {
            mCanBackground = canBackground;
            return this;
        }

        /**
         * @param slideLeftMargin   贴边动画左边距，默认为 0
         * @param slideRightMargin  贴边动画右边距，默认为 0
         * @param slideTopMargin    顶部边距，默认为0
         * @param slideBottomMargin 底部边距 默认为0
         */
        public B setSlideMargin(int slideLeftMargin, int slideRightMargin, int slideTopMargin, int slideBottomMargin) {
            mSlideLeftMargin = slideLeftMargin;
            mSlideRightMargin = slideRightMargin;
            mSlideTopMargin = slideTopMargin;
            mSlideBottomMargin = slideBottomMargin;
            return this;
        }

        public B setMoveStyle(long duration, @Nullable TimeInterpolator interpolator) {
            mDuration = duration;
            mInterpolator = interpolator;
            return this;
        }

        public B setTag(@NonNull String tag) {
            mTag = tag;
            return this;
        }

        public B setViewStateListener(ViewStateListener listener) {
            mViewStateListener = new StaticProxyViewStateListener(listener);
            return this;
        }

        public void build() {
            if (mFloatWindowMap == null) {
                mFloatWindowMap = new HashMap<>();
            }
            //调起当前小窗显示前,关闭其它小窗
            removeOtherWindow(mTag);
            if (mFloatWindowMap.containsKey(mTag)) {
                LogUtils.e("FloatWindow", "FloatWindow of this tag has been added, Please set a new tag for the new FloatWindow");
                return;
            }
            if (mView == null && mLayoutId == 0) {
                LogUtils.e("FloatWindow", "View has not been set!");
                return;
            }
            if (mView == null) {
                mView = ((LayoutInflater) mApplicationContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(mLayoutId, null);
            }
            floatWindowImpl = new IFloatWindowImpl(this);
            floatWindowImpl.onVisible();
        }

    }

    /**
     * 移除非当前tag的小窗
     *
     * @param tag
     */
    public static void removeOtherWindow(String tag) {
        try {
            if (mFloatWindowMap != null) {
                Iterator<HashMap.Entry<String, IFloatWindow>> iterator = mFloatWindowMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry entry = iterator.next();
                    String tempTag = entry.getKey().toString();
                    if (!TextUtils.equals(tag, tempTag)) {
                        IFloatWindow floatWindow = (IFloatWindow) entry.getValue();
                        floatWindow.destoryPlayer(tempTag);
                        floatWindow.destory(tempTag);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    static class StaticProxyViewStateListener implements ViewStateListener {
        ViewStateListener viewStateListener;

        StaticProxyViewStateListener(ViewStateListener viewStateListener) {
            this.viewStateListener = viewStateListener;
        }

        @Override
        public void onActionMove() {
            Optional.ofNullable(viewStateListener).ifPresent(ViewStateListener::onActionMove);

        }

        @Override
        public void onActionUp() {
            Optional.ofNullable(viewStateListener).ifPresent(ViewStateListener::onActionUp);
        }

        @Override
        public void onVisible() {
            if (mFloatWindowMap == null || getB() == null) {
                return;
            }
            LogUtils.e("FloatWindow", "FloatWindow onVisible " + getB().mTag);
            mFloatWindowMap.put(getB().mTag, floatWindowImpl);
            Optional.ofNullable(viewStateListener).ifPresent(ViewStateListener::onVisible);
        }

        @Override
        public void onHomeToDeskTop() {
            Optional.ofNullable(viewStateListener).ifPresent(ViewStateListener::onHomeToDeskTop);
        }

        @Override
        public void onWakeFromDestTop() {
            Optional.ofNullable(viewStateListener).ifPresent(ViewStateListener::onWakeFromDestTop);
        }

        @Override
        public void onRemove(String tag) {
            Optional.ofNullable(viewStateListener).ifPresent(viewStateListener -> viewStateListener.onRemove(tag));
            if (mFloatWindowMap == null || getB() == null || !mFloatWindowMap.containsKey(tag)) {
                LogUtils.e("FloatWindow", "FloatWindow onRemove Exception");
                return;
            }
            LogUtils.e("FloatWindow", "FloatWindow onRemove " + tag);
            mFloatWindowMap.remove(tag);
            floatWindowImpl = null;
        }

        @Override
        public void onDestory(String tag) {
            if (viewStateListener != null) {
                viewStateListener.onDestory(tag);
            }
            this.viewStateListener = null;
            if (mFloatWindowMap == null || tag == null || !mFloatWindowMap.containsKey(tag)) {
                LogUtils.e("FloatWindow", "FloatWindow onDestory Exception");
                return;
            }
            LogUtils.e("FloatWindow", "FloatWindow onDestory " + tag);
            mFloatWindowMap.remove(tag);
            floatWindowImpl = null;
        }

        @Override
        public void onDestoryPlayer(@Nullable String tag) {
            if (viewStateListener != null) {
                viewStateListener.onDestoryPlayer(tag);
            }
        }
    }
}