package html5

import android.os.Bundle
import android.view.View
import android.webkit.WebSettings
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.gson.GsonUtils
import com.howbuy.fund.logupload.LogBuilder
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.StrUtils
import html5.action.JsFuncAddEventListenerHelper

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/2/20
 * @Version V1.0
 */
@Route(path = WebProvider.PATH_GLOBAL_WEB)
open class FragGlobalWebView : FragWebView() {

    private var mStartLoadTime = 0L

    /**
     * 函数: 函数：function addEventListener(var event, var callback);
     * event: WebPageToAppear  每次返回原生h5容器页面时调用
     * appearMap: js function的callback回传参数
     */
    private val webPageToAppear = "WebPageToAppear"
    val appearMap: MutableMap<String, String?> = mutableMapOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        mStartLoadTime = System.currentTimeMillis()
        super.onCreate(savedInstanceState)
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        mWebView.settings.cacheMode = WebSettings.LOAD_NO_CACHE
    }

    override fun onResumeSecond() {
        super.onResumeSecond()
        onPageToAppear()
    }

    /**
     * 页面在onResume时调用,告知H5, 可以刷新当前页面
     * @param webview
     * @return
     */
    open fun onPageToAppear() {
        val cb = JsFuncAddEventListenerHelper.haveEvent(webPageToAppear)
        if (!StrUtils.isEmpty(cb) && mWebView != null) {
            WebViewUtils.loadJs(mWebView, cb, GsonUtils.toJson(appearMap))
            appearMap.clear()
        }
    }

    override fun handlePageStarted() {
        super.handlePageStarted()
        this.mStartLoadTime = System.currentTimeMillis()
    }

    override fun handlePageFinished(url: String?) {
        super.handlePageFinished(url)
        LogUtils.d(
            "WebViewWhiteScreenDetector",
            "加载完成===" + (System.currentTimeMillis() - this.mStartLoadTime) + "，加载进度=" + mWebView.progress + "%"
        )
        if (mWebView.progress == 100) {
            // 先执行一次JS检查页面是否准备好
            mWebView.evaluateJavascript("(function() { return document.readyState; })();") { readyState ->
                if (readyState.contains("complete")) {
                    if(mWebView == null) return@evaluateJavascript
                    WebViewWhiteScreenDetector.detectAndReport(mWebView) {
                        LogBuilder.uploadBuzExceptionToElk("business_trace", "H5页面疑似白屏", url, false)
                    }
                }
            }
        }
    }

}