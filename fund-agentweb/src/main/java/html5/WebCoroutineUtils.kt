package html5

import android.text.TextUtils
import android.webkit.WebView
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.SysUtils
import html5.impl.HbValueCallback
import kotlinx.coroutines.*
import java.io.ByteArrayOutputStream

/**
 * class description.
 * WebView 协程处理工具
 * <AUTHOR>
 * @date 2023/7/11
 */
object WebCoroutineUtils {
    private var onlyForAppJsContent: String = ""


    /**
     * 判断H5中js是否定义，直接从app内置的asset中读取"only-for-app.js"文件，替换从资源包文件中加载(8.5.0)
     */
    fun isH5GlobalFunctionDefined(
        webView: WebView?,
        method: String?,
        hbValueCallback: HbValueCallback?
    ) {
        CoroutineScope(Job() + Dispatchers.Main).launch {
            val wholeJS = if (onlyForAppJsContent.isNotEmpty()) {
                onlyForAppJsContent
            } else
                withContext(Dispatchers.IO) {
                    var jsContent = ""
                    try {
                        val stream = GlobalApp.getApp().resources.assets.open("only-for-app.js")
                        val buff = ByteArray(1024)
                        val fromFile = ByteArrayOutputStream()
                        do {
                            val numread = stream.read(buff)
                            if (numread <= 0) {
                                break
                            }
                            fromFile.write(buff, 0, numread)
                        } while (true)
                        jsContent = fromFile.toString()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    //缓存加载的js文件
                    onlyForAppJsContent = jsContent
                    jsContent
                }
            webView?.loadUrl("javascript:$wholeJS")
            val methodJs = "javascript:isH5GlobalFunctionDefined('$method')"

            if (SysUtils.getApiVersion() >= 19) {
                webView?.evaluateJavascript(methodJs) { value ->
                    LogUtils.d("isH5GlobalFunctionDefined", method + "===" + value)
                    hbValueCallback?.onReceiveValue(!TextUtils.isEmpty(value) && TextUtils.equals("true", value))
                }
            } else {
                webView?.loadUrl(methodJs)
            }
        }
    }
}