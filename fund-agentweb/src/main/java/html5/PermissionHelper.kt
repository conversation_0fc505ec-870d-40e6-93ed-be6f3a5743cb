package html5

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.ContentResolver
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.FileProvider
import androidx.fragment.app.Fragment
import com.howbuy.global.common.SelectFileDlg
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.LogUtils
import com.howbuy.permission.Injection
import howbuy.com.fund_agentweb.R
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * class description.
 * H5调用本地图库功能,以及相关权限问题
 * <AUTHOR>
 * @date 2019/5/13
 */
object PermissionHelper {

    //跳转到系统相机
    const val CHOOSER_PHOTO_REQUEST_CODE = 10004

    //5.x以上的图片回调
    private var uploadMessageAboveL: ValueCallback<Array<Uri>>? = null

    //最大选择数量
    private var mMaxSelectCount = 1

    //单个文件大小上限
    private var mMaxFileSize = 10 * 1024 * 1024L

    //使用相机功能,拍照后,图片的uri地址
    private var imageUri: Uri? = null

    //如果用户选择拍照/打开图库后,如果没有选择照片,就需要回调null给系统,如果过程中,不能重置,要不然图片返回后,无法回调给系统数据
    private var mNeedResetUploadMsg: Boolean = true

    //选择图片文件后回调，外部可自行处理，或者使用默认处理
    private var onActivityResultAboveL: ((Array<Uri>?) -> Array<Uri>?)? = null

    fun setMaxFileSize(maxFileSize: Long) {
        this.mMaxFileSize = maxFileSize
    }

    fun setActivityResultAboveL(onActivityResultAboveL: (Array<Uri>?) -> Array<Uri>?) {
        this.onActivityResultAboveL = onActivityResultAboveL
    }

    /**
     * WebView handleShowFileChooser 调用
     */
    fun handleShowFileChooser(
        frag: Fragment,
        onPhotoPicker: (isMulti: Boolean, isPdf: Boolean, mediaType: ActivityResultContracts.PickVisualMedia.VisualMediaType) -> Void,
        filePathCallback: ValueCallback<Array<Uri>>?,
        fileChooserParams: WebChromeClient.FileChooserParams?
    ): Boolean {
        this.uploadMessageAboveL = filePathCallback
        try {
            var selectPic = false
            var selectPdf = false
            var camera = false
            if (fileChooserParams != null && fileChooserParams.acceptTypes != null) {
                fileChooserParams.acceptTypes.forEach {
                    if (it.contains("jpeg") || it.contains("png")) {
                        selectPic = true
                    } else if (it.contains("pdf")) {
                        selectPdf = true
                    } else if (it.contains("camera")) {
                        camera = true
                    } else if (it.contains("count-")) {
                        try {
                            mMaxSelectCount = it.split("-")[1].toInt()
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }
                if (fileChooserParams.isCaptureEnabled) {
                    //兜底 防止正常走input的无法生效
                    camera = true
                }
            } else {
                //防止H5页面没有添加 accept="image/*"的属性,就默认使用这个场景
                selectPic = true
                camera = true
            }
            showSelectDlg(frag, selectPic, selectPdf, camera, onPhotoPicker)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return true
    }

    /**
     * 选择弹框
     */
    private fun showSelectDlg(
        context: Fragment,
        selectPic: Boolean,
        selectPdf: Boolean,
        camera: Boolean,
        onPhotoPicker: (isMulti: Boolean, isPdf: Boolean, mediaType: ActivityResultContracts.PickVisualMedia.VisualMediaType) -> Void
    ) {
        mNeedResetUploadMsg = true
        SelectFileDlg.getInstance(selectPic, selectPdf, camera, { type ->
            when (type) {
                SelectFileDlg.TYPE_SELECT_PIC -> {
                    //打开图库,及拍照功能
                    this.mNeedResetUploadMsg = false
//                    openPhotoPicker(ActivityResultContracts.PickVisualMedia.ImageOnly)
                    onPhotoPicker.invoke(mMaxSelectCount > 1,false, ActivityResultContracts.PickVisualMedia.ImageOnly)
                }

                SelectFileDlg.TYPE_CAMERA -> {
                    //打开图库,及录制功能(录制功能不做)
                    showCamera(
                        context,
                        context.getString(R.string.camera_permission_title),
                        context.getString(R.string.camera_permission_content_idcard),
                        "设置路径:系统设置->${context.getString(R.string.app_name)}->权限"
                    )
                }

                SelectFileDlg.TYPE_SELECT_PDF -> {
                    this.mNeedResetUploadMsg = false
                    onPhotoPicker.invoke(
                        mMaxSelectCount > 1,
                        true,
                        ActivityResultContracts.PickVisualMedia.SingleMimeType("application/pdf")
                    )
//                    openPhotoPicker(ActivityResultContracts.PickVisualMedia.SingleMimeType("application/pdf"))
                }
            }
        }, {
            if (mNeedResetUploadMsg) {
                uploadMessageAboveL?.onReceiveValue(null)
                uploadMessageAboveL = null
            }
        }).show(context.childFragmentManager, "SelectFileDlg")
    }

    /**
     * 打开相机，外部设置权限文案
     */
    private fun showCamera(
        fragment: Fragment,
        permissionTitle: String,
        permissionTips: String,
        permissionSetting: String
    ) {
        fragment.activity ?: return
        Injection.getPermissionRepository().showApplyPermission(
            fragment.requireActivity(), {
            openPhotoChooserActivity(fragment)
        }, { t ->
            if (t == null) {
                //弹框dismiss,需要重置uploadMessageAboveL和uploadMessage 对象
                uploadMessageAboveL?.onReceiveValue(null)
                uploadMessageAboveL = null
            }
        }, permissionTitle, "${permissionTips}\n\n${permissionSetting}",
            permissionTips, Manifest.permission.CAMERA
        )
        mNeedResetUploadMsg = false
    }

    /**
     * 打开系统相机
     */
    private fun openPhotoChooserActivity(context: Fragment) {
        val intentCapture = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        // 判断存储卡是否可用，存储照片文件
        val carmeFile = cameraFile(context.activity)
        imageUri = getOutputMediaFileUri(context.activity, carmeFile)
        intentCapture.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        if (imageUri != null) {
            intentCapture.putExtra(MediaStore.EXTRA_OUTPUT, imageUri)
            context.startActivityForResult(intentCapture, CHOOSER_PHOTO_REQUEST_CODE)
        } else {
            LogUtils.pop("无法打开系统相机")
        }
    }

    /**
     * 相机拍照的图片保存在该路径下
     */
    private fun cameraFile(context: Activity?): File? {
        context ?: return null
        try {
            val timeStamp = SimpleDateFormat(DateUtils.DATEF_YMD_13, Locale.getDefault()).format(Date())
            return File(context.getExternalFilesDir(null)?.absolutePath + File.separator + "upload_image_" + timeStamp + ".jpg")
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    /**
     * 获取拍照后的照片的URI
     */
    private fun getOutputMediaFileUri(context: Activity?, cameaFile: File?): Uri? {
        if (cameaFile == null || context == null) {
            return null
        }
        return FileProvider.getUriForFile(context, context.packageName.toString() + ".fileProvider", cameaFile)
    }

    /**
     * 对选择的图片数量大小拦截处理
     */
    private fun handleSelectResult(results: Array<Uri>?): Array<Uri> {
        //按mMaxSelectCount 截取results
        var list = results?.toMutableList() ?: mutableListOf()
        if (list.size > mMaxSelectCount) {
            list = list.subList(0, mMaxSelectCount)
        }
        var hasOverSize = false
        //list遍历删除其中不符合条件的
        val result = list.filter {
            val sizeValid = checkSelectFileSize(it)
            if (!sizeValid) {
                hasOverSize = true
            }
            sizeValid
        }
        if (hasOverSize) {
            LogUtils.pop("文件过大，请上传小于${mMaxFileSize / 1024 / 1024}M的文件！")
        }
        return result.toTypedArray()
    }

    /**
     * 判断选择的文件是否大于最大限制
     */
    @SuppressLint("Range")
    private fun checkSelectFileSize(uri: Uri): Boolean {
        if (mMaxFileSize <= 0) return true
        val resolver: ContentResolver = GlobalApp.getApp().contentResolver
        val cursor = resolver.query(uri, arrayOf(OpenableColumns.SIZE), null, null, null)
        try {
            return if (cursor != null) {
                cursor.moveToFirst()
                val size = cursor.getLong(cursor.getColumnIndex(OpenableColumns.SIZE))
                cursor.close()
                size <= mMaxFileSize
            } else {
                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 处理拍照图片回调
     */
    fun handleChoosePhotoResult(resultCode: Int) {
        if (uploadMessageAboveL == null) return
        if (imageUri == null || resultCode != Activity.RESULT_OK) {
            uploadMessageAboveL?.onReceiveValue(null)
            uploadMessageAboveL = null
        } else {
            val result = arrayOf(imageUri!!)
            if (uploadMessageAboveL != null) {
                uploadMessageAboveL?.onReceiveValue(result)
                uploadMessageAboveL = null
            }
        }
    }

    /**
     * 处理选择后的图片
     */
    fun handChosePicturesResult(data: List<Uri>?) {
        //处理返回的图片，并回调给H5页面
        if (uploadMessageAboveL == null) return
        //上传文件 点取消需要如下设置。 否则再次点击上传文件没反应
        if (data.isNullOrEmpty()) {
            uploadMessageAboveL?.onReceiveValue(null)
            uploadMessageAboveL = null
        } else {
            if (uploadMessageAboveL != null) {
                val result = if (onActivityResultAboveL == null) {
                    handleSelectResult(data.toTypedArray())
                } else {
                    onActivityResultAboveL!!.invoke(data.toTypedArray())
                }
                uploadMessageAboveL?.onReceiveValue(result)
                uploadMessageAboveL = null
            }
        }
    }

}