package html5.screenshot;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;

import androidx.core.util.Consumer;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.howbuy.fund.base.config.ValConfig;
import com.howbuy.fund.base.frag.AbsHbFrag;
import com.howbuy.fund.base.nav.NavHelper;

import howbuy.com.fund_agentweb.R;
import html5.AbsFragWebView;
import html5.FragGlobalScreenWebView;

/**
 * description.
 * Web 截图相关方法
 * tao.liang
 * 2024/11/20
 */
public class GlobalWebShotUtils {


    private static ViewGroup addRootView(ViewGroup root, int layoutId) {
        if (root != null) {
            View content = LayoutInflater.from(root.getContext()).inflate(layoutId, null);
            content.setId(R.id.root_id);
            root.addView(content);
            return root;
        }
        return root;
    }

    /**
     * 0或不传：0,1,2->或不传：web长截图分享；1. 资讯截图分享；2. 直播海报分享
     * obj[0]代表类型: 0,1,2
     * obj[1]代表value值:shotType=0或不传：长截图分享的web URL；shotType=1：标题；shotType=2：直播Id
     * obj[2]代表:是否需要拼接二维码, 分享的web截图是否需要app拼二维码图片，1：需要拼接；0或不传：不需要拼接 (7.4.0支持)
     *
     * @param frag
     * @param webView
     * @param screenType
     * @param screenValue
     */
    public static void shotWebView(Fragment frag, WebView webView, String screenType, String screenValue,
                                   boolean supportH5NotifyScreenMethod, Consumer<WebView> bigPicShareCb) {
        ViewGroup fragView = (ViewGroup) frag.getView();
        if (null == fragView) return;
        if (frag instanceof AbsHbFrag) {
            ((AbsHbFrag) frag).showAlermDlg("截屏中...", false, false);
        }
        //webview 长截图, 如果有url就截图指定url的页面, 如果没有url就截取当前webview页面的视力
        String screenUrl;
        if (TextUtils.equals("1", screenType)) {
            //资讯截图,当前页面url,当前可见部分,screenValue就是title
            screenUrl = webView.getUrl();
        } else {
            if (TextUtils.isEmpty(screenValue)) {
                //长截图, 截图当前页面连接
                screenUrl = webView.getUrl();
            } else {
                //长截图, 指定截图另一个连接
                screenUrl = screenValue;
            }
        }
        //截图时,如果当前页面中的web有toolbar,就需要设置为true, 否则false, 要不然影响了页面中web title
        boolean hasToolbar = true;
        if (frag instanceof AbsFragWebView) {
            hasToolbar = ((AbsFragWebView) frag).mActionBarShowed;
        }
        try {
            Bundle bundle = NavHelper.obtainArg("",
                    ValConfig.IT_URL, screenUrl,
                    ValConfig.IT_TYPE, hasToolbar,
                    FragGlobalScreenWebView.KEY_SCREEN_TYPE, screenType,
                    FragGlobalScreenWebView.KEY_SCREEN_VALUE, screenValue,
                    FragGlobalScreenWebView.KEY_SCREEN_WEB, true
            );
            FragmentManager fragmentManager = frag.getChildFragmentManager();
            addScreenWeb(frag, fragmentManager, fragView, bundle, supportH5NotifyScreenMethod,
                    null, bigPicShareCb);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @param frag            frag是为了要控制进度条消失(是否传入根据当前页面的loading控制在哪个页面)
     * @param fragmentManager
     * @param fragView
     * @param bundle
     * @param loadFinishCb
     */
    private static void addScreenWeb(Fragment frag, FragmentManager fragmentManager,
                                     ViewGroup fragView, Bundle bundle,
                                     boolean executeShotByJs, Consumer<WebView> loadFinishCb,
                                     Consumer<WebView> bigPicShareCb) {
        GlobalWebShotUtils.addRootView(fragView, R.layout.screen_shot_webview_layout);
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        FragGlobalScreenWebView screenFrag = new FragGlobalScreenWebView();
        screenFrag.setRootFrag(frag, screenFrag, fragmentManager.beginTransaction(), fragView, executeShotByJs, loadFinishCb);
        screenFrag.setHwBigPicScreenShot(bigPicShareCb);
        screenFrag.setArguments(bundle);
        transaction.add(R.id.web_screen, screenFrag);
        transaction.commitAllowingStateLoss();
    }
}
