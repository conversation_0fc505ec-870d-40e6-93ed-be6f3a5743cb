package html5.action;

import android.os.Message;
import android.webkit.WebView;
import com.howbuy.fund.base.frag.AbsHbFrag;

import html5.impl.IWebToolBar;

/**
 * Created by tao.liang on 2016/7/26.
 * 规定: pushModuleAction 的 what 值 以 "1" 开头;
 * functionAction的what值以"2" 开头
 */
public abstract class AbsWebJsFuncHelper extends WebFuncHelper {


    protected AbsHbFrag mFragment;
    protected WebView mWebview;
    protected IWebToolBar mWebCall;

    public AbsWebJsFuncHelper(AbsHbFrag frag, WebView webView) {
        this.mFragment = frag;
        this.mWebview = webView;

    }

    public AbsWebJsFuncHelper(AbsHbFrag frag, WebView webView, IWebToolBar webCall) {
        this(frag, webView);
        this.mWebCall = webCall;
    }

    /**
     * 处理webview中 js 函数 触发的事件(调起模块,处理webview的js与本地交互)
     *
     * @param msg
     * @return
     */
    public abstract int handJsFuncMsg(Message msg);


}
