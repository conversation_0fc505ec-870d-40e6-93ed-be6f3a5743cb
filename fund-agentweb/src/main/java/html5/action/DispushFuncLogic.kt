package html5.action
import android.webkit.WebView
import androidx.fragment.app.Fragment
import com.howbuy.lib.utils.LogUtils
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker

/**
 * class description.
 * Js, Hb 命令 业务处理
 * <AUTHOR>
 * @date 2023/6/27
 */
object DispushFuncLogic {


    fun handleLogin(frag: Fragment?, webView: WebView?, callback: String?) {
        printLog("handleLogin")
    }

    fun handleRegister(frag: Fragment?, webView: WebView?, callback: String?) {
        printLog("handleRegister")
    }

    fun handleGesturLogin(frag: Fragment?, webView: WebView?, callback: String?) {
        printLog("handleGesturLogin")
    }

    fun handleModify(frag: Fragment?) {
        printLog("handleModify")
    }

    fun launchForgetPwd(frag: Fragment?) {
        printLog("launchForgetPwd")
    }

    fun handleBindCard(frag: Fragment?) {
        printLog("handleBindCard")
    }

    fun handleCardManagement(frag: Fragment?) {
        printLog("handleCardManagement")
    }

    fun handleFeedback(frag: Fragment?) {
        printLog("handleFeedback")
    }

    fun handleProtocol(frag: Fragment?){
        printLog("handleProtocol")
    }
    fun commentList(frag: Fragment?){
        printLog("commentList")
    }

    //交易(买, 卖,...)
    fun handleTrade(frag: Fragment?, action: String, id: String?) {
        printLog("handleTrade")
    }

    fun handActionShare(frag: Fragment?, webView: WebView, jsonString: String?) {
        printLog("handActionShare")
    }

    fun handUpdateH5Version(hbCmd: Boolean) {
        //hbCmd hb命令的,通过之前的上报观察,已不再使用
        Invoker.getInstance().navigation(IWebProvider::class.java).updateH5ZipResource(1, "js update H5zip")
    }

    fun printLog(method:String){
        //TODO: 暂未实现
        LogUtils.d("DispushFuncLogic", "$method 方法暂未实现,这是掌基的js函数")
    }

}
