package html5.action;

import android.content.Context;
import android.content.Intent;
import android.os.Message;
import android.webkit.WebView;

import com.google.gson.GsonUtils;
import com.howbuy.fund.base.nav.NavHelper;
import com.howbuy.lib.utils.JsonUtils;
import com.howbuy.lib.utils.StrUtils;
import com.howbuy.router.provider.IWebProvider;
import com.howbuy.router.proxy.Invoker;

import org.json.JSONObject;

import java.net.URLDecoder;
import java.util.HashMap;

import html5.WebViewUtils;

import static com.howbuy.lib.utils.JsonUtils.getString;

/**
 * Created by tao.liang on 2016/1/21.
 * 这个工具类是处理 H5中function后 在 onActivityResult的数据js处理
 */
public class HandleCallbackHelper {
    public static final String TRANSFORM_JJDM = "TRANSFORM_JJDM";//转换后基金code

    //0: 操作成功 1: 参数不能为空 2: 参数解析失败  3:操作失败  4:用户取消
    public static final int CODE_SUCCESS = 0;
    public static final int CODE_PARAMS_NULL = 1;
    public static final int CODE_FAIL = 3;
    public static final int CODE_CANCEL = 4;


    public static void handleResultCallback(Context context, boolean success, Message msg, WebView webView, Object obj) {
        HashMap<String, Object> dataMap = new HashMap<String, Object>();
        //处理特殊的值
        switch (msg.what) {
            case WebFuncHelper.HANDLER_MODULE_COMMON_JS_NATIVE:
                //处理本地公共模块方法
                ParamsMessage pm = (ParamsMessage) msg.obj;
                String params = pm.getParams();
                JSONObject js = JsonUtils.getObject(params);
                if (js != null) {
                    String moduleId = getString(js, "moduleId");
                    // 扫描银行卡
                    if (StrUtils.equals("TI_BANKCARD_SCAN", moduleId) && success && obj instanceof Intent) {
                        String cardNum = ((Intent) obj).getStringExtra("scan_result");
                        dataMap.put("bankCardNum", cardNum);
                    } else if (StrUtils.equals("TI_SCAN_QR_CODE", moduleId) && success && obj instanceof Intent) {
                        //处理h5调用本地扫码模块,处理返回结果
                        String param = getString(js, "params");
                        JSONObject parJson;
                        if (!GsonUtils.isNullJson(param)) {
                            parJson = JsonUtils.getObject(param);
                            //获取h5传递过来的参数,如果h5Handle=1,由本地处理扫码结果,并不调用callback
                            String h5Handle = JsonUtils.getString(parJson, "h5Handle");
                            String content = ((Intent) obj).getStringExtra("codedContent");
                            if (StrUtils.equals("1", h5Handle)) {
                                //交由h5去处理
                                dataMap.put("qrcode", content);
                            } else {
                                try {
                                    //目前本地处理只处理: cmd=(xxx)内容,并且不回调callback给h5
                                    if (content != null ? content.startsWith("cmd") : false) {
                                        String qrContent = URLDecoder.decode(content, "utf-8");
                                        Invoker.getInstance().navigation(IWebProvider.class).launcherToWebByCmd(context, qrContent, null, true);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                return;
                            }
                        }
                    }
                }
                break;

            default:
                break;

        }

        //组装 data map数据
        prepareDataMap(dataMap, obj);

        if (msg.obj instanceof ParamsMessage) {
            ParamsMessage pm = (ParamsMessage) msg.obj;
            String cb = pm.getCallback();
            int code;
            if (success) {
                code = CODE_SUCCESS;
            } else {
                if (msg.arg1 == NavHelper.REQ_CODE_MOD_PWD) { //H5交易时忘记密码返回若是取消时，无需回调
//                    code = CODE_CANCEL;
                    return;
                } else {
                    code = CODE_FAIL;
                }
            }
            callJsFunc(code, null, cb, webView, dataMap);
        }

    }

    /**
     * 对外提供一个方法: 模块操作后调用js callback
     *
     * @param resCode
     * @param errorDes
     * @param cb
     * @param webView
     * @param dataMap
     */
    public static void callJsFunc(int resCode, String errorDes, String cb, WebView webView, HashMap<String, Object> dataMap) {
        try {
            HashMap<String, Object> map = new HashMap<>();
            HashMap<String, Object> errMap = new HashMap<>();
            errMap.put("errorCode", String.valueOf(resCode));
            if (resCode == CODE_SUCCESS) {
                errMap.put("errorDesc", "操作成功");
            } else if (resCode == CODE_FAIL) {
                errorDes = errorDes == null ? "操作失败" : errorDes;
                errMap.put("errorDesc", errorDes);
            } else if (resCode == CODE_CANCEL) {
                errorDes = errorDes == null ? "用户取消" : errorDes;
                errMap.put("errorDesc", errorDes);
            } else if (resCode == CODE_PARAMS_NULL) {
                errorDes = errorDes == null ? " 参数解析失败" : errorDes;
                errMap.put("errorDesc", errorDes);
            } else {
                errorDes = errorDes == null ? " --" : errorDes;
                errMap.put("errorDesc", errorDes);
            }
            map.put("error", errMap);
            map.put("data", dataMap == null || dataMap.size() == 0 ? null : dataMap);
            WebViewUtils.loadJs(webView, cb, GsonUtils.toJson(map));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //根据不同的handle what值来设置js的参数值, data 字段
    private static void prepareDataMap(HashMap<String, Object> dataMap, Object obj) {
        //基金转投用到
        if (obj instanceof Intent) {
            Intent intent = (Intent) obj;
            String fundCode = intent.getStringExtra(TRANSFORM_JJDM);
            if (!StrUtils.isEmpty(fundCode)) {
                dataMap.put("inputFundCode", fundCode);
            }
        }
    }

}
