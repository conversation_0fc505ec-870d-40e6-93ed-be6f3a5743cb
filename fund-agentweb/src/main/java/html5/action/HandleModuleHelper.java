package html5.action;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Message;
import android.text.TextUtils;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.launcher.ARouter;
import com.howbuy.fund.base.aty.AtyEmpty;
import com.howbuy.fund.base.config.ValConfig;
import com.howbuy.fund.base.frag.AbsHbFrag;
import com.howbuy.fund.base.nav.NavHelper;
import com.howbuy.fund.base.router.AtyRouterPath;
import com.howbuy.fund.base.utils.Optional;
import com.howbuy.lib.aty.AtyMgr;
import com.howbuy.lib.utils.LogUtils;
import com.howbuy.lib.utils.MathUtils;
import com.howbuy.lib.utils.StrUtils;
import com.howbuy.lib.utils.SysUtils;
import com.howbuy.router.provider.IWebProvider;
import com.howbuy.router.proxy.Invoker;
import org.json.JSONObject;

import html5.AbsFragWebView;
import html5.WebViewUtils;
import html5.impl.IWebUrlHandler;

/**
 * Created by tao.liang on 2016/1/21.
 * 这个工具类是处理 调用 各个功能模块
 */
public class HandleModuleHelper extends AbsWebJsFuncHelper {
    private HandleModuleCommonJs mHandleModuleCommonJs;

    public HandleModuleHelper(AbsHbFrag frag, WebView webView) {
        super(frag, webView);
    }

    @Override
    public int handJsFuncMsg(Message msg) {
        if (mWebview == null || mWebview.getContext() == null) {
            return -10;
        }
        switch (msg.what) {
            case HANDLER_MODULE_LOGIN_WHAT: //1. 登录
                DispushFuncLogic.INSTANCE.handleLogin(mFragment, mWebview, parseCallback(msg));
                break;
            case HANDLER_MODULE_REGISTER_WHAT: //2. 注册
                DispushFuncLogic.INSTANCE.handleRegister(mFragment, mWebview, parseCallback(msg));
                break;
            case HANDLER_MODULE_GESTURE_LOGIN_WHAT: //3. 手势密码锁
                DispushFuncLogic.INSTANCE.handleGesturLogin(mFragment, mWebview, parseCallback(msg));
                break;

            case HANDLER_MODULE_FOREGET_PWD_WHAT: //4. 忘记交易密码(这个特殊点, 它的js回调不处理)
                DispushFuncLogic.INSTANCE.handleModify(mFragment);
                break;

            case HANDLER_MODULE_CALL_PHONE_WHAT://5.调起用户电话本(未实现)
                break;
            case HANDLER_MODULE_SUGGESTION_WHAT://6.调起意见反馈模块(未实现)
                DispushFuncLogic.INSTANCE.handleFeedback(mFragment);
                break;
            case HANDLER_MODULE_PC_PROTOCOL_WHAT://7.调起签定代扣协议
                DispushFuncLogic.INSTANCE.handleProtocol(mFragment);
                break;
            case HANDLER_MODULE_FIXED_MGR_WHAT: //8.调起定投管理
                handleOpenFixedListPager();
                break;
            case HANDLER_MODULE_DRAW_MONEY_WHAT: //9.调起取活期(未实现)
                DispushFuncLogic.INSTANCE.handleTrade(mFragment, IWebUrlHandler.PARAMS_DRAW_MONEY, "");
                break;
            case HANDLER_MODULE_DEPOSIT_MONEY_WHAT: //10.调起存活期(未实现)
                DispushFuncLogic.INSTANCE.handleTrade(mFragment, IWebUrlHandler.PARAMS_SAVE_MONEY, "");
                break;
            case HANDLER_MODULE_BIND_BANKCARD_WHAT: //11.调起绑定银行卡
                DispushFuncLogic.INSTANCE.handleBindCard(mFragment);
                break;
            case HANDLER_MODULE_BANK_MGR_WHAT://12.调起银行卡管理模块
                DispushFuncLogic.INSTANCE.handleCardManagement(mFragment);
                break;
            case HANDLER_MODULE_SELECT_BANK_WHAT: //13.调起选择银行卡(未实现)
                break;
            case HANDLER_MODULE_NEW_WEBVIEW_WHAT: //14.在新窗口中打开web页面
                handleOpenNewWebviewPage(msg, msg.what);
                break;
            case HANDLER_MODULE_PROTOCOL_WEBVIEW_WHAT: //15.签订协议窗口模版
                DispushFuncLogic.INSTANCE.printLog("HANDLER_MODULE_PROTOCOL_WEBVIEW_WHAT");
                break;
            case HANDLER_MODULE_CLOSE_CUR_WEBVIEW_WHAT: //16. 关闭当前Web页窗口(无回调)
                handleColseCurrentWebview(msg);
                break;
            case HANDLER_MODULE_SIMU_DETAILS_WHAT: //17私募详情页面(在内部做js callback)
                handleOpenSimuDetails(msg);
                break;
            case HANDLER_MODULE_SIMU_RANK_WHAT: //18私募排行(在内部做js callback)
                handleOpenSimuRank(msg, mFragment, mWebview);
                break;
            case HANDLER_MODULE_SEARCH_WHAT: //19搜索
                handleOpenSearcPage(msg);
                break;
            case HANDLER_MODULE_SHARE_DIG_WHAT://21显示分享浮层模块
                handleShare(msg); //在内部做js callback
                break;
            case HANDLER_MODULE_USER_RISK_MODIFY_WHAT: //修改用户风险等级模块
                handleModifyUserRiskModuel(msg, mFragment);
                break;
            case HANDLER_MODULE_KEYBOARD_SHOW: //显示键盘
                handleKeyboardShow(msg);
                break;
            case HANDLER_MODULE_KEYBOARD_HIDE: //隐藏键盘
                handleKeyboardHide();
                break;
            case HANDLER_MODULE_ALL_COMMENT_LIST: //评论列表
                DispushFuncLogic.INSTANCE.commentList(mFragment);
                break;
            case HANDLER_MODULE_PROPERTY_PIGGY:
                handlePropertyPiggy();
                break;
            case HANDLER_MODULE_PLAN:
                handlePlan(msg);
                break;
            case HANDLER_MODULE_REAL_NAME: //一账通实名认证
                handleRealNameStep(mFragment);
                break;
            case HANDLER_MODULE_SET_TRADE_PWD: //设置交易密码
                handleSetTradePwd(mFragment);
                break;
            case HANDLER_MODULE_FUND_HOLD_LIST:
                handlePublicHoldList(msg.what);
                break;
            case HANDLER_MODULE_COMMON_JS_NATIVE: //通用模块处理
                if (mHandleModuleCommonJs == null) {
                    mHandleModuleCommonJs = new HandleModuleCommonJs();
                }
                if (mFragment != null && mFragment.getActivity() != null) {
                    mHandleModuleCommonJs.handleCommonJsNative(msg, mFragment, mWebview, msg.what);
                }
                break;
            case HANDLER_MODULE_OPENACCOUNT_TO_NATIVE: //h5实名开户流程结果返回
                handleOpenAccountResult(msg);
                break;
            case HANDLER_METHOD_IS_CONTAIN://判断js方法是否存在，存在继续掉，不存在，不调用；
                handlerMenthodIsContain(msg);
                break;
        }
        return msg.what;
    }

    @Nullable
    private static String parseCallback(Message msg) {
        if (null == msg) return null;
        if (msg.obj instanceof ParamsMessage) {
            ParamsMessage pm = (ParamsMessage) msg.obj;
            return pm.getCallback();
        }
        return null;
    }

    /**
     * 执行公募图片分享逻辑
     *
     * @param shotType           H5传递的截图模式
     * @param shotValue          H5传递的截图参数
     * @param needHeadTailConcat 是否需要拼接头尾
     * @param executeShotByJs    截图时机是否为js通知原生进行截图
     */
    private void doGmShare(String shotType, String shotValue, boolean needHeadTailConcat, boolean executeShotByJs) {
//        Activity activity = mFragment.getActivity();
//        if (TextUtils.isEmpty(shotType) || null == activity) return;
//
//        ImgInfoToShare imgInfoToShare = new ImgInfoToShare();
//        imgInfoToShare.shareMode = convertShotMode(shotType, shotValue);
//        imgInfoToShare.targetH5Page = parseTargetH5Url(shotType, shotValue);
//        imgInfoToShare.rawImgUrl = parseRawImgUrl(shotType, shotValue);
//        imgInfoToShare.shouldAddHeadTail = needHeadTailConcat;
//        imgInfoToShare.executeShotByJs = executeShotByJs;
//        Invoker.getInstance()
//                .navigation(IGmProvider.class)
//                .shareGmImg(activity, imgInfoToShare, mWebview)
//                .subscribe();
    }

    /**
     * 解析定制的H5页面的链接，图片模式（5）返回“”；截屏模式（4）时其与当前页面的链接相同的返回“”，否则返回对应的链接
     *
     * @param shotType     图片模式
     * @param targetH5Page 定制的H5页面的链接
     * @return 定制的H5页面的链接
     */
    private String parseTargetH5Url(@NonNull String shotType, @Nullable String targetH5Page) {
        String targetH5 = targetH5Page;
        //为了区分，这里强制将当前页面的分享，从目标链接种移除掉
        if (TextUtils.equals(mWebview.getUrl(), targetH5) || !TextUtils.equals("4", shotType)) {
            targetH5 = "";
        }
        return targetH5;
    }

    /**
     * 解析分享的指定图片链接，仅在模式5下有效
     */
    private String parseRawImgUrl(@NonNull String shotType, @Nullable String shotValue) {
        if (TextUtils.equals("5", shotType)) return shotValue;
        return null;
    }

    private void handlerMenthodIsContain(Message msg) {
        if (SysUtils.getApiVersion() < 19) {
            ParamsMessage pm = (ParamsMessage) msg.obj;
            if (pm != null) {
                String eventId = pm.getParams();
                WebViewUtils.setNativeJsMethod(mWebview, eventId);
            }
        }
    }

    private void handlePublicHoldList(int requesCode) {
        DispushFuncLogic.INSTANCE.printLog("handlePublicHoldList");
    }

    private void handlePlan(Message msg) {
        DispushFuncLogic.INSTANCE.printLog("handlePlan");
    }

    //活期持仓
    private void handlePropertyPiggy() {
        DispushFuncLogic.INSTANCE.printLog("handlePropertyPiggy");
    }

    @Deprecated
    private void handleKeyboardHide() {
    }

    @Deprecated
    private void initWebViewKeyboardMgr() {
    }

    @Deprecated
    private void handleKeyboardShow(Message msg) {
    }

    private void handleShare(Message msg) {
        ParamsMessage pm = (ParamsMessage) msg.obj;
        if (pm == null) {
            return;
        }
        String cb = pm.getCallback();
        String params = pm.getParams();
        Invoker.getInstance().navigation(IWebProvider.class).showCommonShareDialog(mFragment, mWebview, cb, params);
    }


    //跳转到定投合约管理页面
    private void handleOpenFixedListPager() {
        DispushFuncLogic.INSTANCE.printLog("handleOpenFixedListPager");
    }

    //打开私募详情页面
    private void handleOpenSimuDetails(Message msg) {
        DispushFuncLogic.INSTANCE.printLog("handleOpenSimuDetails");
    }

    //打开搜索页面
    private void handleOpenSearcPage(Message msg) {
        Invoker.getInstance().navigation(IWebProvider.class).launcherSearch(mFragment);
    }


    //处理14 在窗口中打开一个webview
    private void handleOpenNewWebviewPage(Message msg, int requestCode) {
        ParamsMessage pm = (ParamsMessage) msg.obj;
        if (pm != null) {
            String url = pm.getParams();
            String json = (String) pm.getExtraObj();
            String title = "";
            String hiddenBackBtn = "0";
            String hiddenNavBar = "0";
            String landScape = "0"; //为1时, 代表要横屏显示
            try {
                JSONObject jo = new JSONObject(json);
                title = jo.has("title") ? jo.getString("title") : "";
                hiddenBackBtn = jo.has("backbtnhidden") ? jo.getString("backbtnhidden") : "0";
                hiddenNavBar = jo.has("navBarStyle") ? jo.getString("navBarStyle") : "1";
                landScape = jo.has("landscape") ? jo.getString("landscape") : "0";
            } catch (Exception e) {
                e.printStackTrace();
            }
            //打开一个新的webview页面
            boolean showNaBar = !"2".equals(hiddenNavBar);
            Bundle bundle = NavHelper.obtainArg(title, ValConfig.IT_URL, url, ValConfig.IT_TYPE, showNaBar,
                    ValConfig.IT_VALUE_5, landScape);
            if ("1".equals(hiddenBackBtn)) {
                bundle.putBoolean(ValConfig.IT_WEB_BACK_BTN, false);
            }
            Invoker.getInstance().navigation(IWebProvider.class).launchWebViewByJS(mFragment,
                    bundle, requestCode);
        }
    }

    //处理 16 关闭当前Webview
    @SuppressLint("WrongConstant")
    private void handleColseCurrentWebview(Message msg) {
        ParamsMessage pm = (ParamsMessage) msg.obj;
        String params;
        if (pm != null) {
            params = pm.getParams();
            if (!StrUtils.isEmpty(params)) {
                LogUtils.d(WebViewUtils.TAG, "close window msg: " + params);
                boolean hasAnima = true; //是否有动画
                String personVoteStr = null;
                String backdepth = null;
                String assetsAuthStates = null;
                try {
                    JSONObject jo = new JSONObject(params);
                    String anima = jo.has("animated") ? jo.getString("animated") : "";
                    hasAnima = !"0".equals(anima);
                    backdepth = jo.has("backdepth") ? jo.getString("backdepth") : "";
                    assetsAuthStates = jo.has("assetsAuthStates") ? jo.getString("assetsAuthStates") : "";

                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (!TextUtils.isEmpty(assetsAuthStates)) {
                    //TODO:暂未实现 - 授权信息 回调
                    //ApiHelperKt.apiSmAuthorize().updateAuthStatus(assetsAuthStates);
                }
                if (hasAnima) { //动画
                    if (mFragment.getActivity() != null) {
                        mFragment.getActivity().overridePendingTransition(0, 0);
                    }
                }

                //默认h5自己返回上一个页面
                if (TextUtils.equals(VAL_BACK_DEPTH, backdepth)) {
                    //backdepth = 999 返回到首页
                    if (mFragment.getActivity() != null) {
                        ARouter.getInstance()
                                .build(AtyRouterPath.PATH_ATY_TB_MAIN)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP)
                                .navigation(mFragment.getActivity());
                    }
                } else {
                    //backdepth != 999 按照实际数值返回
                    if (mFragment.getActivity() != null) {
                        Bundle bundle = new Bundle();
                        bundle.putString(ValConfig.IT_ENTITY, params);
                        if (!StrUtils.isEmpty(parseH5ResultFromJsonKey(params, "riskLevel"))) {//风险等级
                            //用户从机器人页面做完风险测评，直接去买基金，没有取到用户的风险等级.即h5跳转h5没走本地判断逻辑，故请求一次（则本地多请求一次）
                            handRefreshRiskLevel();
                        } else {
                            String openAccountResult = parseH5ResultFromJsonKey(params, "openAccountStatus");
                            boolean isFailed = !StrUtils.isEmpty(openAccountResult) && StrUtils.equals("0", openAccountResult);
                            // 0-未成功，1-成功（跳过了开户第二步绑定银行卡过程），2-成功（没跳过开户第二步绑定银行卡过程）
                            if (isFailed) { //刷新实名信息在
                                mFragment.getActivity().setResult(Activity.RESULT_CANCELED, null);
                                mFragment.getActivity().finish(); //关闭当前页面
                                return;
                            }
                            String selectFiscalResidentStatus = parseH5ResultFromJsonKey(params, "selectFiscalResidentStatus");
                            if (!StrUtils.isEmpty(selectFiscalResidentStatus)) {//税收居民完成结果（0-未成功，1-成功）
                                bundle.putString(ValConfig.IT_STATUS, selectFiscalResidentStatus);
                            }
                        }
                        int backDepthInt = MathUtils.forValI(backdepth, 0);
                        if (backDepthInt > 1) {
                            //大于1 关闭多个页面
                            int realBackDepth = Math.min(backDepthInt, AtyMgr.getAtys().size() - 1);
                            for (int i = 0; i < realBackDepth; i++) {
                                Activity activity = AtyMgr.getAtys().peek();
                                if (activity instanceof AtyEmpty) {
                                    Fragment fragment = ((AtyEmpty) activity).getCurrentFragment();
                                    if (fragment instanceof AbsFragWebView) {
                                        activity.setResult(Activity.RESULT_OK, NavHelper.bundleToBundle(bundle));
                                        activity.finish();
                                        //移除当前Activity,避免onDestroy没执行下一次peek拿到的没发生变化
                                        Optional.ofNullable(AtyMgr.getAtys()).ifPresent(activities -> activities.remove(activity));
                                    } else {
                                        break;
                                    }
                                } else {
                                    break;
                                }
                            }
                        } else {
                            if (((AbsFragWebView) mFragment).mNeedExecuteBackWhenCloseWeb) {
                                //不直接调用mFragment.getActivity().finish(), 在实现该业务的地方处理
                                Invoker.getInstance().navigation(IWebProvider.class).notifyAppRequestThenFinishActivity(mFragment, bundle);
                            } else {
                                mFragment.getActivity().setResult(Activity.RESULT_OK, NavHelper.bundleToBundle(bundle));
                                mFragment.getActivity().finish(); //关闭当前页面
                            }

                        }
                    }
                }
            } else {
                if (mFragment.getActivity() != null) {
                    mFragment.getActivity().setResult(Activity.RESULT_OK, NavHelper.bundleToBundle(new Bundle()));
                    mFragment.getActivity().finish(); //关闭当前页面
                }
            }
        } else {
            if (mFragment.getActivity() != null) {
                mFragment.getActivity().setResult(Activity.RESULT_OK, NavHelper.bundleToBundle(new Bundle()));
                mFragment.getActivity().finish(); //关闭当前页面
            }
        }
    }

    /**
     * h5实名开户流程结果返回
     *
     * @param msg
     */
    private void handleOpenAccountResult(Message msg) {
        ParamsMessage pm = (ParamsMessage) msg.obj;
        String params;
        if (pm != null) {
            params = pm.getParams();
            if (!StrUtils.isEmpty(params)) {
                if (mFragment.getActivity() != null) {
                    boolean isFailed = false;
                    String openAccountResult = parseH5ResultFromJsonKey(params, "openAccountStatus");
                    // 0-未成功，1-成功（跳过了开户第二步绑定银行卡过程），2-成功（没跳过开户第二步绑定银行卡过程）
                    isFailed = !StrUtils.isEmpty(openAccountResult) && StrUtils.equals("0", openAccountResult);
                    LogUtils.d("TradeAccess", "[openAccount] handleOpenAccountResult() invoked, open failed: " + isFailed);
                    if (!isFailed) {
                        handleRefreshCertificationResult();
                    }
                } else {
                    LogUtils.d("TradeAccess", "[openAccount] handleOpenAccountResult() invoked, activity is null");
                }
            } else {
                LogUtils.d("TradeAccess", "[openAccount] handleOpenAccountResult() invoked, params is empty");
            }
        } else {
            LogUtils.d("TradeAccess", "[openAccount] handleOpenAccountResult() invoked, pm is null");
        }
    }

    /**
     * h5操作流程返回结果的解析
     *
     * @param params
     * @param jsonKey riskLevel=风险评测 ;openAccountStatus=实名开户；selectFiscalResidentStatus=税收居民
     * @return
     */
    public String parseH5ResultFromJsonKey(String params, String jsonKey) {
        if (StrUtils.isEmpty(params)) {
            return null;
        } else {
            try {
                JSONObject jsonObject = new JSONObject(params);
                String riskResult = jsonObject.has(jsonKey) ? jsonObject.getString(jsonKey) : null;
                if (riskResult != null) {
                    return riskResult;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    //控制左边的返回按钮
    private void handleBackBtnEnable(boolean isEnable) {
        //设置返回键是否能用
        if (mFragment != null) {
            mFragment.setBackEnable(isEnable);
        }
    }

    private void handleRealNameStep(Fragment mFragment) {
        DispushFuncLogic.INSTANCE.printLog("handleRealNameStep");
    }

    private void handleSetTradePwd(Fragment mFragment) {
        DispushFuncLogic.INSTANCE.printLog("handleSetTradePwd");
    }

    private void handRefreshRiskLevel() {
        DispushFuncLogic.INSTANCE.printLog("handRefreshRiskLevel");
    }

    private void handleRefreshCertificationResult() {
        //实名成功，刷新用户信息，银行卡信息，风险等级
        DispushFuncLogic.INSTANCE.printLog("handleRefreshCertificationResult");
    }

    private void handleModifyUserRiskModuel(Message msg, Fragment mFragment) {
        DispushFuncLogic.INSTANCE.printLog("handleModifyUserRiskModuel");
    }

    private void handleOpenSimuRank(Message msg, Fragment mFragment, WebView mWebView) {
        DispushFuncLogic.INSTANCE.printLog("handleOpenSimuRank");
    }

    /**
     * @param urlPath urlPath加载完毕
     */
    public void onPageLoadFinish(String urlPath) {
        if (mHandleModuleCommonJs != null) {
            mHandleModuleCommonJs.onPageLoadFinish(urlPath, mWebview);
        }
    }

}
