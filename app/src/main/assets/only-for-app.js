/**
 * 该函数供APP用来判断是否存在某个指定的全局js函数（直接挂载在window对象上的js函数）
 * 只有当存在window[globalFuncName]且其值为函数时才返回true
 *
 * @param globalFuncName {string} 要判断的函数名
 * @return {boolean} 若存在目标函数则返回true，否则返回false
 *
 * Note:
 * 1、iOS和android的好买基金6.4.4版本中会开始用到这份文件，
 * 2、储蓄罐iOS和android都还没有开发计划
 * 3、请勿修改该函数，iOS读取这个函数问题不大，安卓那边下面的函数随便修改一下很容易导致解析失败
 */
window.isH5GlobalFunctionDefined = function(globalFuncName) {
    if (globalFuncName in window && typeof window[globalFuncName] === 'function') {
        var u = navigator.userAgent;
        var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 || u.indexOf('android') > -1;
        if (isAndroid && window.pushModuleAction && window.pushModuleAction.isContainMethod) {
            window.pushModuleAction.isContainMethod(globalFuncName)
        }
        return true
    }
    return false
}
