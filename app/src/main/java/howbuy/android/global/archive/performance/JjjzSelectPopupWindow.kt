package howbuy.android.global.archive.performance

import android.app.Activity
import android.graphics.Bitmap
import android.graphics.drawable.BitmapDrawable
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.component.widgets.MyPopupWindow
import com.howbuy.lib.utils.NoDbClickListener
import howbuy.android.global.R


/**
 * Create by tao.liang on 2021/6/25.
 * 业绩表现-历史净值列表- 净值方式切换 pop弹框
 */
class JjjzSelectPopupWindow(val context: Activity?, mListData: MutableList<String>?,
                            var selectPos: Int, viewWidth: Int, val clickListener: IClickListener)
    : MyPopupWindow(context, R.layout.popup_select_netvalue_type_layout) {

    init {
        height = ViewGroup.LayoutParams.WRAP_CONTENT
        width = viewWidth
        isOutsideTouchable = true
        isFocusable = true
        setBackgroundDrawable(
            BitmapDrawable(context?.resources, null as Bitmap?)
        )
        val rcView = contentView.findViewById<RecyclerView>(R.id.rc_view)
        rcView.layoutManager = LinearLayoutManager(context)
        rcView.adapter = object : BaseQuickAdapter<String, BaseViewHolder>(
            R.layout.item_selected_networth_type_popup_layout, mListData) {
            override fun convert(holder: BaseViewHolder, t: String) {
                val position = holder.bindingAdapterPosition
                holder.setText(R.id.tv_name, t)
                holder.setGone(R.id.iv_selected, position != selectPos)
                holder.itemView.setOnClickListener(object : NoDbClickListener() {
                    override fun onNoDoubleClick(v: View?) {
                        clickListener.onClick(position, t)
                        dismiss()
                    }
                })
            }
        }
    }

    interface IClickListener {
        fun onClick(position: Int, item: String)
    }

}