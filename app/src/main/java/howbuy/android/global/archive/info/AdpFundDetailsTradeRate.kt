package howbuy.android.global.archive.info

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import howbuy.android.global.R
import howbuy.android.global.archive.entity.FundDetailsTradeRateItem

/**
 * @Description 档案页-交易须知-费率列表
 * <AUTHOR>
 * @Date 2024/3/22
 * @Version v2.0
 */
class AdpFundDetailsTradeRate(list: MutableList<FundDetailsTradeRateItem>?) :
    BaseQuickAdapter<FundDetailsTradeRateItem, BaseViewHolder>(R.layout.item_fund_details_trade_rate, list) {
    override fun convert(holder: BaseViewHolder, item: FundDetailsTradeRateItem) {
        holder.setText(R.id.tv_title, item.rateDesc).setText(R.id.tv_value, FundTextUtils.showTextEmpty("", item.rate, "%"))
    }
}