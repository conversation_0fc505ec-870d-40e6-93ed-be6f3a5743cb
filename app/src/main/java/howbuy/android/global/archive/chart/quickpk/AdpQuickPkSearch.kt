package howbuy.android.global.archive.chart.quickpk

import android.text.SpannableString
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.lib.utils.SpanBuilder
import com.howbuy.lib.utils.StrUtils
import howbuy.android.global.R
import howbuy.android.global.search.HkSearchFundContent

/**
 * 快速pk搜索
 * <AUTHOR>
 * @Date 2024/10/17
 * @Version V2.5
 */
class AdpQuickPkSearch : BaseQuickAdapter<HkSearchFundContent, BaseViewHolder>(R.layout.item_quick_pk_search) {

    private var mKeyWords = ""

    fun setKeywords(keywords: String) {
        this.mKeyWords = keywords
    }

    override fun convert(holder: BaseViewHolder, item: HkSearchFundContent) {
        holder.setText(R.id.tv_fund_name, formatSearchSource(item.tsshortName))
    }

    private fun formatSearchSource(input: String?): SpannableString {
        if (StrUtils.isEmpty(input)) return SpannableString("")

        val sb = StringBuilder()
        sb.append(input)
        val spannable = SpanBuilder(sb)
        val index = spannable.string.lowercase().indexOf(mKeyWords.lowercase())
        if (index != -1) {
            spannable.color(index, index + mKeyWords.length, ContextCompat.getColor(context, R.color.fd_rise), false);
        }
        return spannable.spannableString
    }
}