package howbuy.android.global.archive.chart.quickpk

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.account.UserDataHelper
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsCustomVPFrag
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.global.data_api.DataIds
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.getHkCustNo
import com.howbuy.global.user.entity.UserHoldInfo
import howbuy.android.global.R
import howbuy.android.global.databinding.FragQuickPkListBinding
import howbuy.android.global.entity.SmFavoriteItem
import howbuy.android.global.optional.SmOptionalMgr

/**
 * @Description 档案页-快速pk-自选基金
 * <AUTHOR>
 * @Date 2024/10/17
 * @Version V2.5
 */
class FragQuickPkOptList : AbsCustomVPFrag<FragQuickPkListBinding>() {
    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragQuickPkListBinding {
        return FragQuickPkListBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_quick_pk_list

    private val mAdapter by lazy {
        ListAdapter()
    }

    private var mFundCode: String? = null

    //持仓列表
    private val mHoldList = mutableListOf<String>()

    override fun parseArgment(arg: Bundle?) {
        setSmHoldList()
        mFundCode = arguments?.getString(ValConfig.IT_ID)
//        val maxSize = arg?.getInt(ValConfig.IT_TYPE, 0) ?: 0
//        if (maxSize > 0) {
//            binding.rv.setMaxHeight(DensityUtils.dp2px(18 * maxSize + 7 * (maxSize + 1).toFloat()))
//        }
        binding.rv.adapter = mAdapter.apply {
            setOnItemClickListener { _, _, position ->
                if (parentFragment is QuickPkDialog) {
                    (parentFragment as QuickPkDialog).onSelectClick(mAdapter.data[position].jjjc, mAdapter.data[position].jjdm)
                }
            }
        }
        binding.rv.addItemDecoration(XLinearBuilder(context).setSpacing(7f).setShowFirstTopLine(true).setShowLastLine(true).build())
        mAdapter.setList(getOptList())
    }

    private fun setSmHoldList() {
        if (!TextUtils.isEmpty(getHkCustNo())) {
            val smHoldDataWrap = UserDataHelper.getDataManager(DataIds.ID_USER_HOLD).getSync<UserHoldInfo>().data
            mHoldList.clear()
            smHoldDataWrap?.fundHoldList?.forEach {
                if (!TextUtils.isEmpty(it.fundCode)) {
                    mHoldList.add(it.fundCode)
                }
            }
        }
    }

    private fun getOptList(): MutableList<SmFavoriteItem> {
        var optList = SmOptionalMgr.getInstance().allOptionalListFromDb
        if (!TextUtils.isEmpty(mFundCode)) {
            //过滤
            optList = optList.filter { showItem(it) }
        }
        return optList
    }

    //若用户未登录，“自选基金”tab不显示
    //若当前用户为“非专业投资者”，只取自选池中海外公募基金（香港）-【海外产品分类】hwcpfl='1'的基金 （含未登录场景@王爽）
    //若当前用户为“专业投资者”，则取自选池中全量产品
    private fun showItem(item: SmFavoriteItem): Boolean {
        if (TextUtils.equals(item.jjdm, mFundCode)) return false
        if (apiUserInfo().isProInvestor()) return true
        return TextUtils.equals("1", item.piType)
    }

    class ListAdapter : BaseQuickAdapter<SmFavoriteItem, BaseViewHolder>(R.layout.item_quick_pk_list_item) {

        override fun convert(holder: BaseViewHolder, item: SmFavoriteItem) {
            holder.setText(R.id.tv_name, item.jjjc)
                .setText(R.id.tv_type, item.tzcl)
        }

    }
}