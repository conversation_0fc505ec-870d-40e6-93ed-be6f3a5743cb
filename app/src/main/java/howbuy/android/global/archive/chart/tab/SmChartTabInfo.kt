package howbuy.android.global.archive.chart.tab

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * class description.
 * 私募模块中-走势图tab对象
 * <AUTHOR>
 * @date 2022/10/20
 */

/**
 * @param tabCode tab唯一标识
 * @param tabTitle tab显示的文案内容
 * @param tabTag 标识tag,用于fragment的查找使用
 * @param tabValidIndex 所有可点击的tab中,当前tab的有效下标,当tabEnable=false时,该字段标记为-1;
 *                      在tabList集合中找出所有tabEnable=true的tab对象,从第一个tabEnable=true的tab开始计算下标为0,
 *                      依次++,作为实际有效的下标,该字段与实现Viewpager2的pageIndex对应.
 * @param tabIndex  标识当前tab在整个tabList集合中的数据原始下标(包括不可用的tab),该字段与tab的list下标对应.
 * @param tabEnable 标识当前tab是否可用,不可用的tab,不可点击.
 * @param tabEditable 标识当前tab是否可自定义编辑,切换,选择不同的选项(如: 私募档案页面走势图中的最后一个tab: 更多>).
 * @param tabEditableClicked 标识自定义tab是否点击过.
 * @param tabRepeatClick 标识当前tab是否重复点击(处理点击tab时,暴露给业务层使用);false-当前不是定位在该tab,点击该tab;true-当前已定位在该tab,再次重复点击;
 */
@Parcelize
data class SmChartTabInfo(
    val tabCode: String,
    var tabTitle: String?,
    var subTabTitle: String?,
    val tabTag: String,
    var tabIndex: Int,
    var tabValidIndex: Int,
    var tabEnable: Boolean = true,
    var tabEditable: Boolean = false,
    var tabEditableClicked: Boolean = false,
    var tabRepeatClick: Boolean = false,
    var subTabTitle2: String? = null
) : Parcelable {

    constructor(
        tabCode: String,
        tabTitle: String?,
        tabTag: String,
        tabIndex: Int,
        tabValidIndex: Int,
        tabEnable: Boolean = true,
        tabEditable: Boolean
    ) : this(tabCode, tabTitle, "", tabTag, tabIndex, tabValidIndex, tabEnable, tabEditable, false, false)

    constructor(
        tabCode: String,
        tabTitle: String?,
        subTabTitle: String?,
        subTabTitle2: String?,
        tabTag: String,
        tabIndex: Int,
        tabValidIndex: Int,
        tabEnable: Boolean = true,
        tabEditable: Boolean
    ) : this(
        tabCode,
        tabTitle,
        subTabTitle,
        tabTag,
        tabIndex,
        tabValidIndex,
        tabEnable,
        tabEditable,
        false,
        false,
        subTabTitle2
    )

    constructor(
        tabCode: String,
        tabTitle: String?,
        subTabTitle: String?,
        subTabTitle2: String?,
        tabEnable: Boolean = true,
        tabEditable: Boolean
    ) : this(
        tabCode,
        tabTitle,
        subTabTitle,
        "",
        0,
        0,
        tabEnable,
        tabEditable,
        false,
        false,
        subTabTitle2
    )

    override fun toString(): String {
        return "SmChartTabInfo(tabCode='$tabCode', tabTitle=$tabTitle, tabTag='$tabTag', tabValidIndex=$tabValidIndex, tabIndex=$tabIndex, tabEnable=$tabEnable, tabEditable=$tabEditable, tabEditableClicked=$tabEditableClicked, tabRepeatClick=$tabRepeatClick)"
    }
}