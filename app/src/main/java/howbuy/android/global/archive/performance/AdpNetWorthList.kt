package howbuy.android.global.archive.performance

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.SysUtils
import howbuy.android.global.R
import howbuy.android.global.archive.entity.FundNetValueBody
import howbuy.android.global.optional.IObserver

/**
 * description.
 * 历史净值列表数据
 * tao.liang
 * 2024/3/22
 */
class AdpNetWorthList(private val observer: IObserver) :
    BaseQuickAdapter<FundNetValueBody.Item, BaseViewHolder>(R.layout.item_history_networth_layout) {

    //默认显示复权净值, 为0时,显示累计净值; 1是复权净值
    var mNetValueType: Int = 1

    /**
     * 切换 复权净值和累计净值
     */
    fun resetValueType(netValueType: Int) {
        mNetValueType = netValueType
        notifyDataSetChanged()
    }

    override fun convert(holder: BaseViewHolder, item: FundNetValueBody.Item) {
        holder.setText(R.id.tv_date, DateUtils.timeFormat(item.jzrq, DateUtils.DATEF_YMD, DateUtils.DATEF_YMD_))
        val rv = holder.getView<RecyclerView>(R.id.rv_column)
        val layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        rv.layoutManager = layoutManager
        observer.onObserve(rv, layoutManager, holder.bindingAdapterPosition)
        val columnList = if (mNetValueType == 0) {
            mutableListOf(item.jjjzStr, item.ljjzStr, item.hbxx)
        } else {
            mutableListOf(item.jjjzStr, item.fqdwjz, item.hbxx)
        }
        if (rv.adapter is AdpNetWorthData) {
            (rv.adapter as AdpNetWorthData).setList(columnList)
        } else {
            rv.adapter = AdpNetWorthData(columnList)
        }
    }
}

class AdpNetWorthData(list: MutableList<String?>) : BaseQuickAdapter<String?, BaseViewHolder>(R.layout.item_history_networth_column, list) {

    private var valueWidth = 110f.dp2px()
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        valueWidth = Math.max((SysUtils.getWidth(context) - (107 + 8).dp2px()) / 3, 110f.dp2px())
    }

    override fun onBindViewHolder(holder: BaseViewHolder, position: Int) {
        super.onBindViewHolder(holder, position).apply {
            holder.getView<View>(R.id.tv_value).layoutParams.width = valueWidth
        }
    }

    override fun convert(holder: BaseViewHolder, item: String?) {
        if (holder.bindingAdapterPosition == 2) {
            //涨跌幅-红涨绿跌
            FundUtils.formatDataForSmNewColor(holder.getView(R.id.tv_value), item, "1", "1")
        } else {
            holder.setText(R.id.tv_value, FundTextUtils.showTextEmpty(item))
            holder.setTextColor(R.id.tv_value, ColorUtils.parseColor("#262626"))
        }
    }

}