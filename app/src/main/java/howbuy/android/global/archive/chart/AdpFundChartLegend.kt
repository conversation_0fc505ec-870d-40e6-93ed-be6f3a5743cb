package howbuy.android.global.archive.chart

import android.graphics.Typeface
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundUtils
import com.howbuy.lib.utils.ColorUtils
import howbuy.android.global.R
import howbuy.android.global.archive.entity.TagLegendItem

/**
 * @Description 阳光私募-走势图图例
 * <AUTHOR>
 * @Date 2023/12/22 13:42
 * @Version V836
 */

class AdpFundChartLegend @JvmOverloads constructor(
    list: MutableList<TagLegendItem>?,
    private val canClick: Boolean = true,
    private val riseDownStyle: Boolean? = true
) :
    BaseQuickAdapter<TagLegendItem, BaseViewHolder>(R.layout.item_chart_legend, list) {

    override fun convert(holder: BaseViewHolder, item: TagLegendItem) {
        holder.getView<LinearLayout>(R.id.container).gravity = Gravity.CENTER_VERTICAL
        if (item.type == TagLegendItem.TYPE_ADD || item.type == TagLegendItem.TYPE_CHANGE) {
            //比较基准按钮
            holder.setGone(R.id.tv_change_bjjz, false)
                .setGone(R.id.lay_dot, true)
                .setGone(R.id.lay_name_value, true)
            val tvChangeBjjz = holder.getView<TextView>(R.id.tv_change_bjjz)
            if (item.type == TagLegendItem.TYPE_ADD) {
                tvChangeBjjz.text = "添加比较基准"
                tvChangeBjjz.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.sm_icon_pro_set, 0, 0, 0)
            } else {
                tvChangeBjjz.text = "更换比较基准"
                tvChangeBjjz.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.sm_icon_update2, 0, 0, 0)
            }
        } else {
            holder.setGone(R.id.tv_change_bjjz, true)
                .setGone(R.id.lay_dot, false)
                .setGone(R.id.lay_name_value, false)
            holder.getView<View>(R.id.dot).background = TagLegendItem.dotBg(item.index, item.type)
            holder.getView<TextView>(R.id.tv_name).text = item.name
            holder.getView<TextView>(R.id.tv_name).apply {
                text = item.name
                typeface = if (item.isSelect) Typeface.create("sans-serif-medium", Typeface.NORMAL) else Typeface.DEFAULT
//                setTextColor(ColorUtils.parseColor(if (canClick) "#666666" else "#999999"))
            }
            holder.getView<View>(R.id.tv_value).visibility = View.VISIBLE
            FundUtils.formatDataForSmNewColor(
                holder.getView(R.id.tv_value), item.value, "1",
                if (riseDownStyle == true) "1" else "0"
            )
            if (riseDownStyle != true) {
                holder.setTextColor(R.id.tv_value, ContextCompat.getColor(context, R.color.fd_unbiased))
            }
        }
    }
}