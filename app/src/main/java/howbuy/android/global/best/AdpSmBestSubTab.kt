package howbuy.android.global.best

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import howbuy.android.global.R

/**
 * @Description 私募优选-二级tab
 * <AUTHOR>
 * @Date 2024/1/29
 * @Version V838
 */
class AdpSmBestSubTab : BaseQuickAdapter<SmBestSubTab, BaseViewHolder>(R.layout.item_sm_best_sub_tab) {

    var mSelectIndex: Int = 0

    fun setClickPos(pos: Int, notify: Boolean) {
        mSelectIndex = pos
        if (notify) {
            notifyDataSetChanged()
        }
    }

    override fun convert(holder: BaseViewHolder, item: SmBestSubTab) {
        holder.setText(
            R.id.tv_item, FundTextUtils.showTextEmpty(item.secondName)
                    + FundTextUtils.showTextEmpty("(", item.secondCount, ")")
        )
        holder.getView<TextView>(R.id.tv_item).textSize = if (mSelectIndex == holder.layoutPosition) 15f else 13f
    }
}