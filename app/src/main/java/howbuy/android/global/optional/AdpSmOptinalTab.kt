package howbuy.android.global.optional

import android.graphics.Typeface
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import howbuy.android.global.R
import howbuy.android.global.entity.SmOptionalTab

/**
 * @Description 私募优选-二级tab
 * <AUTHOR>
 * @Date 2024/1/29
 * @Version V838
 */
class AdpSmOptinalTab :
    BaseQuickAdapter<SmOptionalTab, BaseViewHolder>(R.layout.rcv_tab_sm_optional_item) {

    var mSelectedGroupId: String? = ""

    fun setClickPos(selectedGroupId: String?, notify: Boolean) {
        mSelectedGroupId = selectedGroupId
        if (notify) {
            notifyDataSetChanged()
        }
    }

    override fun convert(holder: BaseViewHolder, item: SmOptionalTab) {
        holder.setText(R.id.tv_optional_tab, FundTextUtils.showTextEmpty(item.groupName))
        holder.setGone(R.id.indicator_optional, item.groupId != mSelectedGroupId)
        if (item.groupId == mSelectedGroupId) {
            holder.getView<TextView>(R.id.tv_optional_tab).textSize = 17f
            holder.getView<TextView>(R.id.tv_optional_tab).typeface =
                Typeface.create("sans-serif-medium", Typeface.NORMAL)
        } else {
            holder.getView<TextView>(R.id.tv_optional_tab).textSize = 15f
            holder.getView<TextView>(R.id.tv_optional_tab).typeface = Typeface.DEFAULT
        }
    }
}