package howbuy.android.global.optional.columnsedit

import android.annotation.SuppressLint
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ListView
import android.widget.TextView
import androidx.drawerlayout.widget.DrawerLayout
import androidx.recyclerview.widget.RecyclerView
import com.howbuy.component.widgets.dslv.DragSortListView
import com.howbuy.fund.base.adapter.group.DefaultHolderFactory
import com.howbuy.fund.base.adapter.group.TypedAdapter
import com.howbuy.fund.base.widget.NoScrollLayoutManager
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.ViewUtils
import howbuy.android.global.R
import howbuy.android.global.optional.SmColumnType
import howbuy.android.global.optional.getNormalColumField
import java.util.*

/**
 * 私募自选“指标”编辑侧边栏View处理逻辑
 * <AUTHOR>
 * @date 2024/09/02
 */
class OptionColumnEditView(var presenter: SmOptionColumnsEditContract.Presenter?) :
    SmOptionColumnsEditContract.View {
    companion object {
        const val TAG: String = "[SmOpt-Columns]"
    }

    private var layRoot: View? = null
    private var tvReset: TextView? = null
    private var tvOk: TextView? = null

    /**已显示列表(可拖拽排序，可删除)*/
    private var listExisted: DragSortListView? = null
    private var existsAdp: AdpSmGroupOptionEditColumnExists? = null

    /**未显示列表*/
    private var listColumnNotShow: RecyclerView? = null
    private var lvitemJJJZ: ListView? = null
    private var labelNotShow: TextView? = null

    override fun destroy() {
        this.presenter = null
        this.layRoot = null
    }

    override fun inflateView(parent: ViewGroup): View {
        this.layRoot = LayoutInflater.from(parent.context)
            .inflate(R.layout.sm_group_option_columns_edit_right_menu, parent, false)
        return layRoot!!
    }

    override fun initViews(groupId: String?) {
        layRoot?.let {
            tvReset = it.findViewById(R.id.tvReset)
            tvOk = it.findViewById(R.id.tvOk)
            tvReset!!.setOnClickListener { presenter?.reset(groupId) }
            tvOk!!.setOnClickListener { presenter?.submit(groupId, getDataInExistedList(groupId)) }
            //不否编辑数据列表
            var unableEditList = getDataUnEditList(groupId)
            //获取不可编辑项后,净值涨跌这一项不展示
//            unableEditList = unableEditList.subList(0, unableEditList.size - 1)
            lvitemJJJZ = it.findViewById(R.id.lv_unable_edit)
            val unableEditAdapter = AdpSmGroupOptionEditColumnExists(it.context, false) { v ->
                v?.let { it ->
                    handleClickUnableEditdList(it)
                }
            }
            unableEditAdapter.items.clear()
            unableEditAdapter.items.addAll(unableEditList)
            lvitemJJJZ?.adapter = unableEditAdapter
            lvitemJJJZ?.post {
                ViewUtils.setListViewHeightBasedOnChildren(lvitemJJJZ)
            }
            //可编辑数据列表
            listExisted = it.findViewById(R.id.dragList)
            existsAdp = AdpSmGroupOptionEditColumnExists(it.context, true) { v ->
                v?.let { it ->
                    handleClickInExistedList(groupId, it)
                }
            }
            listExisted!!.adapter = existsAdp
            listExisted!!.setDropListener { from: Int, to: Int ->
                //位置变更
                existsAdp!!.moveItem(from, to)
                presenter?.columnPositionChanged(
                    groupId,
                    existsAdp!!.getItem(from) as SmColumnType,
                    from,
                    to
                )
            }
            listExisted!!.post { ViewUtils.setListViewHeightBasedOnChildren(listExisted) }

            listColumnNotShow = it.findViewById(R.id.columnsNotShow)
            listColumnNotShow!!.isNestedScrollingEnabled = false
            listColumnNotShow!!.layoutManager = NoScrollLayoutManager(it.context)
            val noAddAdp = TypedAdapter.Builder<SmColumnType>()
                .setHolderFactory(DefaultHolderFactory(R.layout.sm_group_option_columns_edit_item_no_add))
                .setHolderBinder { holder: RecyclerView.ViewHolder, _: Int, data: SmColumnType ->
                    val columnName: TextView = holder.itemView.findViewById(R.id.columnName)
                    columnName.text = data.filedTitle
                    holder.itemView.setOnClickListener {
                        moveItemFromNoExistedList2ExistedList(
                            groupId,
                            data,
                            holder.bindingAdapterPosition
                        )
                    }
                }
                .build()
            listColumnNotShow!!.adapter = noAddAdp

            labelNotShow = it.findViewById(R.id.labelNotShow)
        }
    }

    /**
     * 处理显示列表的点击事件
     */
    private fun handleClickInExistedList(groupId: String?, it: View) {
        var position: Int? = null
        var data: SmColumnType? = null
        if (it.getTag(R.id.positionIcon) is Int) {
            position = it.getTag(R.id.positionIcon) as Int
        }
        if (it.getTag(R.id.columnName) is SmColumnType) {
            data = it.getTag(R.id.columnName) as SmColumnType
        }
        val valid =
            position != null && data != null && position >= 0 && (position < (existsAdp?.count
                ?: 0))
        if (valid) {
            when (it.id) {
                R.id.deleteIcon -> moveItemFromExistedList2NoExistedList(
                    groupId,
                    data!!,
                    position!!
                )

                R.id.columnName -> presenter?.requestPositionColumn(data!!)
                R.id.positionIcon -> presenter?.requestPositionColumn(data!!)
            }
        }
    }

    /**
     * 处理显示列表的点击事件
     */
    private fun handleClickUnableEditdList(it: View) {
        val data = if (it.getTag(R.id.columnName) is SmColumnType) {
            it.getTag(R.id.columnName) as SmColumnType
        } else {
            null
        }
        if (data != null) {
            when (it.id) {
                R.id.deleteIcon -> LogUtils.pop("${data.filedTitle}不可删除或排序~")
                R.id.dragSortIcon -> LogUtils.pop("${data.filedTitle}不可删除或排序~")
                R.id.columnName -> presenter?.requestPositionColumn(data)
                R.id.positionIcon -> presenter?.requestPositionColumn(data)
            }
        }
    }

    override fun showExistsColumns(columnsExist: MutableList<SmColumnType>) {
        LogUtils.d(TAG, "showExistsColumns, list size:" + columnsExist.size)
        existsAdp?.let { adp ->
            adp.setItems(columnsExist, true)
            ViewUtils.setListViewHeightBasedOnChildren(listExisted!!)
            LogUtils.d(TAG, "showExistsColumns, update data")
        }
    }

    override fun showNoExistedColumns(columnsExist: MutableList<SmColumnType>) {
        LogUtils.d(TAG, "showNoExistedColumns, list size:" + columnsExist.size)
        labelNotShow?.visibility = if (columnsExist.isEmpty()) View.GONE else View.VISIBLE
        listColumnNotShow?.let {
            it.adapter?.let { adp ->
                @Suppress("UNCHECKED_CAST")
                (adp as TypedAdapter<SmColumnType>).updateDataList(columnsExist)
            }
        }
    }

    override fun getDataInExistedList(groupId: String?): MutableList<SmColumnType> {
        val list: MutableList<SmColumnType> = mutableListOf()
        list.addAll(getNormalColumField().subList(0, 2))
        if (null != existsAdp && existsAdp!!.count > 0) {
            list.addAll(existsAdp!!.items)
        }
        return list
    }

    override fun getDataUnEditList(groupId: String?): MutableList<SmColumnType> {
        val list: MutableList<SmColumnType> = mutableListOf()
        list.addAll(getNormalColumField().subList(0, 2))
        return list
    }

    override fun getDataInNoExistedList(): MutableList<SmColumnType> {
        if (null == listColumnNotShow) return mutableListOf()
        if (null == listColumnNotShow?.adapter) return mutableListOf()
        @Suppress("UNCHECKED_CAST")
        return if (listColumnNotShow!!.adapter!!.itemCount <= 0) mutableListOf() else (listColumnNotShow!!.adapter!! as TypedAdapter<SmColumnType>).dataList
    }

    /**
     * 从未显示列表中删除一项，并将其添加到已显示的列表中
     */
    private fun moveItemFromNoExistedList2ExistedList(
        groupId: String?,
        column: SmColumnType,
        positionInNoExistedList: Int
    ) {
        //remove data from no existed list
        listColumnNotShow?.adapter?.let { adp ->
            @Suppress("UNCHECKED_CAST") val adapter = adp as TypedAdapter<SmColumnType>
            if (positionInNoExistedList >= 0 && positionInNoExistedList < adapter.dataItemCount) {
                Log.d(
                    TAG,
                    "index removed:$positionInNoExistedList, list size:${adapter.dataItemCount}"
                )
                adapter.dataList.removeAt(positionInNoExistedList)
                adapter.notifyItemRemoved(positionInNoExistedList)
                if (adapter.dataList.isEmpty()) {
                    labelNotShow?.visibility = View.GONE
                }
            }
        }
        column.isFiledShow = true
        //add data to existed list
        existsAdp?.let { adp ->
            adp.items.add(column)
            adp.notifyDataSetChanged()
            ViewUtils.setListViewHeightBasedOnChildren(listExisted!!)
        }
        presenter?.addToExist(groupId, column)
    }

    /**
     * 从已显示列表中删除一项，并将其添加到未显示的列表中
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun moveItemFromExistedList2NoExistedList(
        groupId: String?,
        column: SmColumnType,
        positionInExistedList: Int
    ) {
        if (column.filedCode == "jjjz" || column.filedCode == "jzhb") {
            return //业绩基准,投资期限,最新净值 ,净值涨跌  不支持删除
        }

        //remove data from existed list
        existsAdp?.let { adp ->
            LogUtils.d(TAG, "before remove from exists, list: ${adp.items}")
            adp.items.removeAt(positionInExistedList)
            adp.notifyDataSetChanged()
            ViewUtils.setListViewHeightBasedOnChildren(listExisted!!)
            LogUtils.d(TAG, "after remove from exists, list: ${adp.items}")
        }
        column.isFiledShow = false
        //add data to no existed list
        listColumnNotShow?.adapter?.let { adp ->
            @Suppress("UNCHECKED_CAST") val adapter = adp as TypedAdapter<SmColumnType>
            adapter.dataItemCount
            if (adapter.dataItemCount <= 0) {
                labelNotShow?.visibility = View.VISIBLE
            }
            LogUtils.d(TAG, "before add to noExists, list: ${adapter.dataList}")
            adapter.dataList.add(column)
            val sortedList = sortList(adapter.dataList)
            adapter.setList(sortedList)
            LogUtils.d(TAG, "after add to noExists, list: ${adapter.dataList}")
//            adapter.notifyDataSetChanged()
        }
        presenter?.removeFromExists(groupId, column)
    }

    override fun closeDraw() {
        layRoot?.let {
            if (it.parent !is DrawerLayout) return
            (it.parent as DrawerLayout).closeDrawer(it)
        }
    }

    private fun sortList(list: MutableList<SmColumnType>): List<SmColumnType> {
        val map = mapOf(
            "jjjz" to 0,
            "jzhb" to 1,
            "hb1y" to 2,
            "hb3y" to 3,
            "hb6y" to 4,
            "hb1n" to 5,
            "hb2n" to 6,
            "hb3n" to 7,
            "hb5n" to 8,
            "hbjn" to 9,
            "hbzx" to 10,
            "zxrq" to 11,
            "hbcl" to 12,
            "clrq" to 13,
        )
        return list.sortedWith(compareBy { map[it.filedCode] })
    }
}