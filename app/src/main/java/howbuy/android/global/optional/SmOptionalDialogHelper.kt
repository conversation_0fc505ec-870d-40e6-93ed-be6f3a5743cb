package howbuy.android.global.optional

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.PopupWindow
import android.widget.TextView
import com.howbuy.dialog.DlgHelper
import com.howbuy.fund.net.util.HandleErrorMgr
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import howbuy.android.global.R
import howbuy.android.global.request.GlobalRequest
import howbuy.android.global.utils.SmOptHelper

/**
 * class description.
 * 私募自选列表操作-统一弹框操作
 * <AUTHOR>
 * @date 2021/6/24
 */
object SmOptionalDialogHelper {

    /**
     * 移除自定义分组弹框
     * @param executeImmediate 是否立即调用接口保存数据[从编辑页面调用,则传false, 列表页面的长按弹出菜单删除,则传true]
     * @param fundCodes 多个基金,以 , 隔开(当executeImmediate为false时,可以不传, 为true时,必须传fundcode)
     * @param fundName 可为null, dialog上的提示文案不一样
     * @param smfl 基金分类: 添加自选时,需要有分类区别是 阳光私募(sm)还是固收(fix),股权型(stock)产品
     * @param executeImmediate 是否立即执行生效(列表操作操作立即生效执行, 编辑页面需要点击完成才保存操作(2021/12/16:编辑页面删除也实时生效))
     * @param onClick 点击弹框上的 取消, 确定 按钮 回调(页面调用处,需要执行结果)
     */
    fun showRemoveGroupDialog(
        context: Context?,
        mutilpGroup: Boolean,
        customGroup: Boolean,
        groupId: String?,
        fundCodes: String?,
        fundName: String?,
        smfl: String?,
        executeImmediate: Boolean,
        onClick: IOptionalDlgClickListener?, favListener: SmOptHelper.IFavListener?
    ) {
        context ?: return
        var needDeleteFav = false
        val customView =
            View.inflate(context, R.layout.dlg_remove_custom_optional_group_layout, null)
        val tvTitle = customView.findViewById<TextView>(R.id.tv_remove_optional_title)
        val layCheck = customView.findViewById<View>(R.id.lay_remove_optional)
        val checkView = customView.findViewById<CheckBox>(R.id.cb_remove_optional)
        // isCustomGroup 是否为自定义分组(有groupId就是自定义分组)
        if (customGroup) {
            layCheck.visibility = View.VISIBLE
            layCheck.setOnClickListener {
                needDeleteFav = !checkView.isChecked
                checkView.isChecked = needDeleteFav
            }
        } else {
            needDeleteFav = !customGroup
            layCheck.visibility = View.GONE
        }

        tvTitle.text = if (customGroup) {
            if (TextUtils.isEmpty(fundName)) {
                if (mutilpGroup) {
                    "在多个分组下选中了基金，确定移除选中的基金吗？"
                } else {
                    "确定从当前分组移除选中的基金吗？"
                }
            } else {
                "确定从当前分组移除“$fundName”吗？"
            }
        } else {
            if (TextUtils.isEmpty(fundName)) {
                if (mutilpGroup) {
                    "在多个分组选中了基金，确定将选中基金删除自选吗？"
                } else {
                    "确认将选中基金删除自选？"
                }
            } else {
                "确定将“$fundName”删除自选吗？"
            }
        }
        DlgHelper { _, which ->
            if (which == DlgHelper.IDlgHelper.DLG_POSITIVE) {
                if (executeImmediate) {
                    if (needDeleteFav || !customGroup) {
                        //需要删除自选,调用老接口删除自选就可以
                        SmOptHelper.updateOptional(
                            context,
                            null,
                            fundCodes,
                            0,
                            fundName,
                            "",
                            favListener
                        )
                    } else {
                        //自定义分组中的基金,删除分组下的基金
                        GlobalRequest.reqSmOptionalGroupOptBatch(
                            fundCodes,
                            null,
                            groupId.toString(),
                            0
                        ) {
                            if (it.isSuccess) {
                                onClick?.refresh()
                            } else {
                                val errMsg = HandleErrorMgr.handErrorMsg(it.mErr, true)
                                LogUtils.pop(errMsg)
                            }
                        }
                    }
                } else {
                    //编辑页面监听,当前操作删除的状态(true为需要删除自选)
                    onClick?.onPositiveClick(needDeleteFav)
                }
            } else {
                //取消按钮
                onClick?.onNegativeeClick()
            }
        }.showDialog(
            context, DlgHelper.DlgArg("取消", "确定", "", customView)
                .setGravity(Gravity.CENTER)
                .setBackCancelAble(true).setTouchCancelAble(true), 1
        )
    }

    /**
     * 私募自选首页-功能弹出菜单
     * 1. 新建分组 :股权自选无该菜单
     * 2. 分组管理 :股权自选无该菜单
     * 3. 基金管理
     * 4. 持仓置顶 : 非持仓tab时,有该菜单
     */
    fun showManagePopWindow(
        context: Context?,
        isHoldTab: Boolean,
        anchorView: View,
        onClick: View.OnClickListener
    ) {
        context ?: return
        val window = PopupWindow(context)
        window.width = ViewGroup.LayoutParams.WRAP_CONTENT
        window.height = ViewGroup.LayoutParams.WRAP_CONTENT
        window.setBackgroundDrawable(ColorDrawable(0))
        val menuView =
            View.inflate(anchorView.context, R.layout.sm_option_manage_entry_window, null)
        window.contentView = menuView
        window.isFocusable = true
        window.isOutsideTouchable = true
        window.isTouchable = true

        menuView.findViewById<View>(R.id.lay_sm_optional_menu_add_group).apply {
            visibility = View.VISIBLE
            setOnClickListener {
                window.dismiss()
                //新建分组
                onClick.onClick(it)
            }
        }
        menuView.findViewById<View>(R.id.lay_sm_optional_menu_manager_group).apply {
            visibility = View.VISIBLE
            setOnClickListener {
                window.dismiss()
                //分组管理
                onClick.onClick(it)
            }
        }
        menuView.findViewById<View>(R.id.lay_sm_optional_menu_manager_fund).setOnClickListener {
            window.dismiss()
            //管理基金
            onClick.onClick(it)
        }
        menuView.findViewById<View>(R.id.lay_sm_optional_menu_hold_top).apply {
            visibility = if (!isHoldTab) {
                View.VISIBLE
            } else {
                View.GONE
            }

            setOnClickListener {
                window.dismiss()
                //持仓置顶
                onClick.onClick(it)
            }
        }

        val anchorLoc = IntArray(2)
        anchorView.getLocationOnScreen(anchorLoc)
        val topAnchor = anchorLoc[1]
        val windowYOff = topAnchor + anchorView.measuredHeight - DensityUtils.dp2px(20f)
        window.showAtLocation(anchorView, Gravity.TOP or Gravity.END, 0, windowYOff)
    }
}

interface IOptionalDlgClickListener {
    fun onPositiveClick(needDeleteFav: Boolean)
    fun onNegativeeClick()
    fun refresh()
}