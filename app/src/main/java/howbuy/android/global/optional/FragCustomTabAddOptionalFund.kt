package howbuy.android.global.optional

import android.os.Bundle
import android.view.View
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.fund.base.frag.AbsBindingFrag
import com.howbuy.android.analytics.annotation.pv.PvInfo
import howbuy.android.global.PATH_FRAG_SM_CUSTOM_ADD_OPTIONAL_FUND
import howbuy.android.global.R
import howbuy.android.global.BR
import howbuy.android.global.databinding.FragCustomTabAddOptionalFundLayoutBinding

/**
 * 私募自选-自定义分组下 点击 添加按钮跳转页面
 * <AUTHOR>
 * @Date 2022/3/10 15:53
 * @Version 7.8.2
 */
@Route(path = PATH_FRAG_SM_CUSTOM_ADD_OPTIONAL_FUND)
class FragCustomTabAddOptionalFund :
    AbsBindingFrag<FragCustomTabAddOptionalFundLayoutBinding, CustomTabAddOptionalFundVm>() {
    override fun getFragLayoutId(): Int = R.layout.frag_custom_tab_add_optional_fund_layout

    override fun getViewModelId(): Int = BR.viewModel

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {

    }

    override fun initViewObservable() {

    }


}