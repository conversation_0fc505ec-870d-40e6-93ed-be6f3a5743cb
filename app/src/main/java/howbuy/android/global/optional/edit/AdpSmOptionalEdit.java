package howbuy.android.global.optional.edit;

import android.text.TextUtils;
import android.util.ArraySet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.howbuy.fund.base.utils.FundTextUtils;
import com.howbuy.lib.adp.AbsAdp;
import com.howbuy.lib.adp.AbsViewHolder;
import com.howbuy.lib.utils.DensityUtils;
import com.howbuy.lib.utils.SysUtils;

import java.util.ArrayList;
import java.util.List;

import howbuy.android.global.R;
import howbuy.android.global.entity.SmFavoriteItem;
import howbuy.android.global.optional.ImportHoldFundsHelper;
import howbuy.android.global.utils.CommonTools;


/**
 * class description.
 * 私募自选编辑页面-列表Adapter
 *
 * <AUTHOR>
 * @date 2018/4/2
 */
public class AdpSmOptionalEdit extends AbsAdp<SmFavoriteItem> {

    private Fragment mFrag;
    //添加选中基金时,会自动按基金代码正序排
    private ArraySet<String> mCheckList;

    private int mTitleViewWidth = 0;

    public AdpSmOptionalEdit(Fragment fragment, List<SmFavoriteItem> items) {
        super(fragment.getActivity(), items);
        this.mFrag = fragment;
        if (mCheckList == null) {
            mCheckList = new ArraySet<>();
        }
        mTitleViewWidth = (int) (SysUtils.getWidth(mFrag.getActivity()) * (3f / 5));
    }

    public ArraySet<String> getCheckList() {
        return mCheckList;
    }

    @Override
    protected View getViewFromXml(int type, ViewGroup p) {
        return mLf.inflate(R.layout.item_optional_edit_layout, p, false);
    }

    @Override
    protected AbsViewHolder<SmFavoriteItem> getViewHolder() {
        return new SmOptEditHolder();
    }

    class SmOptEditHolder extends AbsViewHolder<SmFavoriteItem> implements View.OnClickListener {
        private View mLayTitleItem;
        private CheckBox mcb;
        private TextView mTvTitle1;
        private TextView mTvTitle2;
        private TextView mTvBuyTag1;
        private TextView mTvBuyTag2;
        private ImageView mIvTop;
        int holdTagWidth = 0;
        @Override
        protected void initView(View root, int type) {
            mLayTitleItem = root.findViewById(R.id.lay_sm_opt_title);
            mcb = (CheckBox) root.findViewById(R.id.cb_sm_opt);
            mTvTitle1 = (TextView) root.findViewById(R.id.tv_sm_opt_fund_name);
            mTvTitle2 = (TextView) root.findViewById(R.id.tv_sm_opt_fund_name2);
            mTvBuyTag1 = (TextView) root.findViewById(R.id.tv_sm_opt_buy_tag1);
            mTvBuyTag2 = (TextView) root.findViewById(R.id.tv_sm_opt_buy_tag2);
            mIvTop = (ImageView) root.findViewById(R.id.iv_sm_opt_top);
            mIvTop.setOnClickListener(this);
            mLayTitleItem.setOnClickListener(this);
            //计算持有标签宽度
            holdTagWidth = (int) mTvBuyTag1.getPaint().measureText("持有");
        }

        @Override
        protected void initData(SmFavoriteItem item, boolean isReuse) {
            boolean isCheck = getCurStatusByKey(item);
            mcb.setChecked(isCheck);
            setTitleUIData(item);
            if (TextUtils.equals("1", item.getTop())) {
                mIvTop.setImageResource(R.mipmap.sm_zx_up_icon_red);
            } else {
                mIvTop.setImageResource(R.mipmap.icon_stick);
            }
        }

        /**
         * 持有标签跟在基金简称后面
         * @param t
         */
        private void setTitleUIData(SmFavoriteItem t) {
            //处理 持有 标签
            String title = FundTextUtils.showTextEmpty(t.getJjjc());
            if (TextUtils.equals("1", t.getSmHold())) {
                //剩余title宽度内容: 总宽度-左右边距
                int totalWidth = mTitleViewWidth  - DensityUtils.dp2px(20f) - DensityUtils.dp2px(20f);
                CommonTools.INSTANCE.handleBreakLineTxtWithLabel(
                        title,
                        totalWidth - 5,
                        holdTagWidth +  DensityUtils.dp2px(7f),
                        0,
                        new ArrayList<>(),
                        mTvTitle1,
                        mTvTitle2,
                        mTvBuyTag1,
                        mTvBuyTag2);
            } else {
                mTvTitle1.setText(title);
                mTvTitle1.setMaxLines(2);
                mTvTitle2.setVisibility(View.GONE);
                mTvBuyTag1.setVisibility(View.GONE);
                mTvBuyTag2.setVisibility(View.GONE);
            }
        }

        @Override
        public void onClick(View v) {
            if (v.getId() == R.id.lay_sm_opt_title) {
                //选中/取消选中
                checkedItem(mIndex);
            } else if (v.getId() == R.id.iv_sm_opt_top) {
                if (TextUtils.equals("1", mItem.getTop())) {
                    //取消置顶
                    topItem(mIndex, false);
                } else {
                    //置顶
                    topItem(mIndex, true);
                }
            }
        }
    }

    public boolean getCurStatusByKey(SmFavoriteItem item) {
        return mCheckList.contains(item.getJjdm());
    }

    public void setItemChecked(String key, boolean isCheck, boolean notify) {
        if (isCheck) {
            mCheckList.add(key);
        } else {
            mCheckList.remove(key);
        }
        if (notify) {
            notifyDataSetChanged();
        }
    }

    public void clearCheckList(boolean notify) {
        mCheckList.clear();
        if (notify) {
            notifyDataSetChanged();
        }
    }

    public void checkedItem(int position) {
        if (getCheckList() != null && position < getCount()) {
            SmFavoriteItem bean = getItems().get(position);
            setItemChecked(bean.getJjdm(), !getCurStatusByKey(bean), false);
            notifyAllSelectedState();
            notifyDataSetChanged();
        }
    }

    public void topItem(int from, boolean top) {
        SmFavoriteItem item = removeItem(from, false);
        if (item != null) {
            int normalAearFirstIndex = ImportHoldFundsHelper.INSTANCE.getAreaNormalFirstIndex(getItems());
            if (top) {
                item.setTop("1");
                insertItem(item, 0, true);
            } else {
                item.setTop("0");
                if (normalAearFirstIndex == -1) {
                    //添加到最后
                    normalAearFirstIndex = getItems().size();
                }
                insertItem(item, normalAearFirstIndex, true);
            }
            if (mAllSelectedListener != null) {
                mAllSelectedListener.dataSortChanged(item, true, top);
            }
        }
    }

    public void dragSort(int from, int to) {
        if (from != to) {
            int normalAearFirstIndex = ImportHoldFundsHelper.INSTANCE.getAreaNormalFirstIndex(getItems());
            SmFavoriteItem item = removeItem(from, false);
            if (item != null) {
                //如果normalAearFirstIndex=0说明没有置顶区的基金
                if (normalAearFirstIndex != -1) {
                    if (to < normalAearFirstIndex) {
                        item.setTop("1");
                    } else {
                        item.setTop("0");
                    }
                }
                insertItem(item, to, true);
                if (mAllSelectedListener != null) {
                    mAllSelectedListener.dataSortChanged(item, false, TextUtils.equals("1", item.getTop()));
                }
            }
        }
    }

    // 刷新全选按钮状态
    public void notifyAllSelectedState() {
        int totalSelectCount = getCheckList().size();
        // 通知全选
        int result = -1;
        if (totalSelectCount == getCount()) {
            result = 1;
        } else if (totalSelectCount == 0) {
            result = -1;
        } else {
            result = 0;
        }
        // 通知全选
        if (mAllSelectedListener != null) {
            mAllSelectedListener.onSelectedState(result);
        }
    }

    ListenerAllSelectedState mAllSelectedListener;

    public interface ListenerAllSelectedState {
        /**
         * -1:无任何选中
         * 0: 有部分选中
         * 1:全部选中
         */
        void onSelectedState(int result);

        /**
         * 数据顺序是否改变
         *
         * @param target        操作的目标item
         * @param clickTop      操作是是否是  点击置顶按钮(true); false代表的是拖动操作
         * @param needToTopArea 操作类型: true:要把当前基金放在置顶区; false:要把当前置顶的移动到非置顶区(也可能之前该基金本身就不是置顶状态)
         */
        void dataSortChanged(SmFavoriteItem target, boolean clickTop, boolean needToTopArea);
    }

    public void setAllSelectedListener(ListenerAllSelectedState mAllSelectedListener) {
        this.mAllSelectedListener = mAllSelectedListener;
    }

}
