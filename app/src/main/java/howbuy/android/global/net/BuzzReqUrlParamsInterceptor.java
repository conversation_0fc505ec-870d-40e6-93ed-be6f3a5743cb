package howbuy.android.global.net;

import android.text.TextUtils;

import com.howbuy.fund.base.config.ApkConfig;
import com.howbuy.fund.net.http.ReqParams;
import com.howbuy.fund.net.util.UrlUtils;
import com.howbuy.http.okhttp3.Interceptor;
import com.howbuy.http.okhttp3.Request;
import com.howbuy.http.okhttp3.Response;
import com.howbuy.lib.compont.GlobalApp;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * description.
 * 1. 加密业务接口请求,需要在url后面拼接参数 authVer=1.0&productId=145217193&tokenId=xxxxxx
 * 2. 明文业务接口请求,需要在url后面拼接参数 productId=145217193
 * tao.liang
 * 2024/3/1
 */
public class BuzzReqUrlParamsInterceptor implements Interceptor {

    public final static String SECURITYKEY_URL_SUFFIX = "/gateway/securitykey.htm";

    /**
     * request.originParams() 直接添加参数可能为空，因为有些url无原始参数；
     * builder中参数改变，url不会改变，这个在拦截设置时要特别注意
     */

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        if (request.tag() instanceof ReqParams) {
            Request.Builder builder = request.newBuilder();
            ReqParams reqParams = (ReqParams) request.tag();
            if (!TextUtils.isEmpty(reqParams.getUrl()) && !reqParams.getUrl().contains(SECURITYKEY_URL_SUFFIX)) {
                HashMap<String, String> urlParams = new HashMap<>();
                if (reqParams.isEncrypt()){
                    //加密接口才要拼接该字段,固定拼接值
                    urlParams.put("authVer", "1.0");
                }
                String prodId = GlobalApp.getApp().getPublicParams().get("productId");
                if (!TextUtils.isEmpty(prodId)) {
                    urlParams.put("productId", prodId);
                }
                String token = GlobalApp.getApp().getPublicParams().get("token");
                if (reqParams.isEncrypt() && !TextUtils.isEmpty(token)) {
                    urlParams.put("tokenId", token);
                }
                try {
                    String tempUrl = UrlUtils.buildUrlRaw(reqParams.getUrl(), urlParams);
                    builder.url(tempUrl);
                    return chain.proceed(builder.build());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return chain.proceed(request);
    }
}