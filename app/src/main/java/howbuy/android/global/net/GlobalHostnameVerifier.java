package howbuy.android.global.net;

import com.howbuy.fund.net.util.HandleErrorMgr;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;

/**
 * @Description 域名校验
 * <AUTHOR>
 * @Date 2024/3/27
 * @Version v2.0
 */
public class GlobalHostnameVerifier implements HostnameVerifier {
    public static final String DOMAIN_HOWBUY = "*.howbuy.com";
    public static final String DOMAIN_EHOWBUY = "*.ehowbuy.com";
    public static final String DOMAIN_DNS = "*.ldns.ehowbuy.com";
    public static final String DOMAIN_HXM = "www.haoxiaomai.com";

    public GlobalHostnameVerifier() {
    }

    public boolean verify(String hostname, SSLSession session) {
        HostnameVerifier hv = HttpsURLConnection.getDefaultHostnameVerifier();
        HandleErrorMgr.d("hostname: ", hostname + "; session:" + (hv.verify("*.howbuy.com", session) || hv.verify("*.ehowbuy.com", session) || hv.verify("*.ldns.ehowbuy.com", session)) + "--------------" + hv.verify("*.howbuy.com", session) + hv.verify("*.ehowbuy.com", session) + hv.verify("*.ldns.ehowbuy.com", session));
        return hv.verify(DOMAIN_HOWBUY, session) || hv.verify(DOMAIN_EHOWBUY, session) || hv.verify(DOMAIN_DNS, session) || hv.verify(DOMAIN_HXM, session);
    }
}
