package howbuy.android.global.headlines.vhall.video.chat

import android.annotation.SuppressLint
import android.app.Dialog
import android.net.Uri
import android.text.TextUtils
import android.view.Gravity
import android.widget.LinearLayout
import com.howbuy.component.widgets.pinchimageview.BigImageParam
import com.howbuy.component.widgets.pinchimageview.PinchImageView
import com.howbuy.fund.base.BaseDialogFragment
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.utils.ImgHelper
import howbuy.android.global.R
import java.lang.Exception

/**
 * @Description 查看大图dialog
 * <AUTHOR>
 * @Date 2023/2/22
 * @Version v806
 */
@SuppressLint("NotifyDataSetChanged")
class DlgBigImagePreview : BaseDialogFragment() {

    companion object {
        fun getInstance(bigImageParam: BigImageParam): DlgBigImagePreview {
            val fragment = DlgBigImagePreview()
            fragment.arguments = NavHelper.obtainArg("", ValConfig.IT_BEAN, bigImageParam)
            return fragment
        }
    }

    private lateinit var bigImageView: PinchImageView
    private lateinit var bigImageParam: BigImageParam
    override fun onCreateDialog(): Dialog {
        bigImageParam = arguments?.getParcelable(ValConfig.IT_BEAN) ?: BigImageParam()
        val dialog = Dialog(activity!!, R.style.cpay_MyDialog)
        bigImageView = PinchImageView(context)
        dialog.setContentView(bigImageView)
        initView()
        dialog.window?.setWindowAnimations(R.style.picker_view_scale_anim)
        dialog.window?.setGravity(Gravity.CENTER)
        dialog.window?.setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT)
        dialog.setCanceledOnTouchOutside(true)
        return dialog
    }

    private fun initView() {
        bigImageView.setOnClickListener { _, _ -> dismiss() }
        if (imgIsDrawable()) {
            showDrawable()
        } else {
            showImgWithUrl()
        }
    }

    private fun imgIsDrawable(): Boolean {
        return -1 != bigImageParam.drawableResId && TextUtils.isEmpty(bigImageParam.url)
    }

    private fun showDrawable() {
        bigImageView.setImageResource(bigImageParam.drawableResId)
    }

    private fun showImgWithUrl() {
        if (TextUtils.isEmpty(bigImageParam.url)) {
            dismiss()
            return
        }
        try {
            ImgHelper.display(Uri.decode(bigImageParam.url), bigImageView)
        } catch (e: Exception) {
            e.printStackTrace()
            dismiss()
        }
    }
}