package howbuy.android.global.headlines.entity

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import kotlinx.android.parcel.Parcelize

/**
 *@desc 专栏Item
 *<AUTHOR>
 *@date 2024/05/13
 **/
@Parcelize
data class ColumnList(
    val simuColumnDTOS:MutableList<ColumnItem>?
) : Parcelable, AbsNormalBody()

@Parcelize
data class ColumnItem(
    //专栏Id
    val columnId: String?,
    //专栏名称-红色字用##{内容}##区分，如#日报#
    var columnName: String?,
    //专栏图标
    val columnImgUrl: String?,
    //专栏摘要-最新内容
    val columnDesc: String?,
    //跳转链接
    val eventUrl: String?,
) : Parcelable