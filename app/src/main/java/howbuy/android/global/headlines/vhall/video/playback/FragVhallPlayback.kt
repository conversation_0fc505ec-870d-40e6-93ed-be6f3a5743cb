package howbuy.android.global.headlines.vhall.video.playback

import android.annotation.SuppressLint
import android.graphics.drawable.AnimationDrawable
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.SeekBar
import com.howbuy.fund.base.arch.ViewModelHelper
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.vhall.player.Constants
import com.vhall.player.vod.VodPlayerView
import howbuy.android.global.R
import howbuy.android.global.databinding.FragVhallPlaybackBinding
import howbuy.android.global.headlines.vhall.VhallUtils
import howbuy.android.global.headlines.vhall.dialog.SmVideoSettingsBaseDlg
import howbuy.android.global.headlines.vhall.dialog.SmVideoSettingsLandSelectedDlg
import howbuy.android.global.headlines.vhall.video.FragVhallBase
import howbuy.android.global.headlines.vhall.video.VhallVideoPlayMgr
import howbuy.android.global.headlines.vhall.video.chat.FragSmVhallChatLand
import howbuy.android.global.headlines.vhall.video.vm.SmVhallPreviewData
import howbuy.android.global.headlines.vhall.video.vm.VmSmVhallDetail


/**
 * @Description 微吼播放器区域-回看
 * <AUTHOR>
 * @Date 2022/12/13
 * @Version v806
 */
open class FragVhallPlayback : FragVhallBase<FragVhallPlaybackBinding, PlaybackPresenter>(), View.OnClickListener, PlaybackView {

    private var lastPlayerPos: Long = 0
    private var vodPlayerView: VodPlayerView ?= null
    var fromSmallView: Boolean = false

    /**
     * 定位提示语,只显示一次
     */
    private var hasShowedLocationProgressTips = false

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragVhallPlaybackBinding {
        return FragVhallPlaybackBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_vhall_playback

    override fun parseArgment(arg: Bundle?) {
        super.parseArgment(arg)
        //curPlayTimeStr:单位s
        if (fromSmallView) {
            lastPlayerPos = VhallVideoPlayMgr.lastPlayPos
            mPresenter?.setCurPlayerCurrentPosition(lastPlayerPos)
        } else {
            val mVm = ViewModelHelper.createViewModel(activity, VmSmVhallDetail::class.java)
            mVm.mDetailData.observe(this) {
                it?.let {
                    val curPlayTimeStr = it.playTime
                    lastPlayerPos = MathUtils.forValLFormat(curPlayTimeStr, 0) * 1000
                    mPresenter?.setCurPlayerCurrentPosition(lastPlayerPos)
                }
            }
        }
        val liveData = arg?.getParcelable<SmVhallPreviewData>(FragVhallBase.INTENT_LIVE_DATA)
        binding.tvTitle.text = FundTextUtils.showTextEmpty(liveData?.title, "")
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        if (VhallVideoPlayMgr.vodPlayerView == null) {
            fromSmallView = false
            vodPlayerView = VodPlayerView(GlobalApp.getApp())
        } else {
            fromSmallView = true
            vodPlayerView = VhallVideoPlayMgr.vodPlayerView!!
            val viewGroup = VhallVideoPlayMgr.vodPlayerView!!.parent as? ViewGroup
            viewGroup?.removeAllViews()
        }
        val livingParams = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        binding.layVideoContainer.addView(vodPlayerView, livingParams)
        (mPresenter as? PlaybackPresenterImpl)?.fromSmallView = fromSmallView
        binding.layVideoContainer.setOnTouchListener(this)
        binding.layDlna.setOnClickListener(this)
        binding.ivBack.setOnClickListener(this)
        binding.ivPlay.setOnClickListener(this)
        binding.ivShare.setOnClickListener(this)
        binding.errorShare.setOnClickListener(this)
        binding.ivFullscreen.setOnClickListener(this)
        binding.ivMenu.setOnClickListener(this)
        binding.tvDlnaChange.setOnClickListener(this)
        binding.tvDlnaStop.setOnClickListener(this)
        binding.tvQuality.setOnClickListener(this)
        binding.tvSpeed.setOnClickListener(this)
        binding.seekbar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                setProgressLabel(progress.toLong())
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {
                cancelDismissControlViewTimer()
            }

            override fun onStopTrackingTouch(seekBar: SeekBar?) {
                mPresenter?.onStopTrackingTouch(seekBar)
                startDismissControlViewTimer()
            }
        })
        startDismissControlViewTimer()
        if (mChatFragmentLand != null) {
            val transaction = childFragmentManager.beginTransaction()
            transaction.replace(R.id.container_land_chat, mChatFragmentLand!!)
            transaction.commitAllowingStateLoss()
        } else {
            binding.containerLandChat.visibility = View.GONE
        }

        if (fromSmallView) {
            setSeekbarMax(VhallVideoPlayMgr.getWatchPlayback()?.duration ?: 0)
            setSeekbarCurrentPosition(VhallVideoPlayMgr.getWatchPlayback()?.currentPosition ?: 0L)
        }
    }

    private var mChatFragmentLand: FragSmVhallChatLand? = null
    fun setLandChatView(chatFragmentLand: FragSmVhallChatLand?) {
        //回放视频不显示聊天消息,如果后续有需要,只需要打开注释就行
        //mChatFragmentLand = chatFragmentLand
    }

    fun showLastPlayPosTips() {
        if (activity == null) {
            return
        }
        hasShowedLocationProgressTips = true
        if (!fromSmallView) {
            LogUtils.pop("已为您定位上次观看位置")
        }
//        binding.tvLastPlayPosHint.visibility = View.VISIBLE
//        binding.tvLastPlayPosHint.postDelayed({
//            if (activity != null) {
//                binding.tvLastPlayPosHint.visibility = View.GONE
//            }
//        }, 3000)
    }

    override fun setPlayIcon(isPlay: Boolean) {
        if (isPlay) {
            binding.ivPlay.setImageResource(R.mipmap.ic_vhall_play)
            ImgHelper.loadAnimatedWebP(binding.ivAudioImg, R.mipmap.sm_bk_pla_whi)
        } else {
            binding.ivPlay.setImageResource(R.mipmap.ic_vhall_pause)
            binding.ivAudioImg.setImageResource(R.mipmap.sm_bk_pla_whi)
            binding.layoutTop.visibility = View.VISIBLE
            binding.layoutBottom.visibility = View.VISIBLE
        }
    }

    private var hasGetDuration = false

    private var playTimeNeedHour = true
    override fun setSeekbarMax(max: Long) {
        hasGetDuration = true
        binding.seekbar.max = max.toInt()
        val totalTime = if (max>=0){
            VhallUtils.convertLongTimeToStr(max, false)
        }else {
            "00:00"
        }
        val tvWidth: Float
        if (totalTime.length > 5) {
            tvWidth = binding.tvTotalTime.paint.measureText("99:99:99")
            playTimeNeedHour = true
        } else {
            tvWidth = binding.tvTotalTime.paint.measureText("99:99")
            playTimeNeedHour = false
        }
        binding.tvPlayTime.layoutParams.width = (tvWidth + 10).toInt()
        binding.tvTotalTime.layoutParams.width = (tvWidth + 10).toInt()
        binding.tvTotalTime.text = totalTime
        if (lastPlayerPos > 0 && !fromSmallView && !hasShowedLocationProgressTips) {
            showLastPlayPosTips()
        }
    }

    private fun setProgressLabel(currentTime: Long) {
        if (hasGetDuration) {
            if (currentTime <=0) {
                binding.tvPlayTime.text = "00:00"
            } else {
                binding.tvPlayTime.text = VhallUtils.convertLongTimeToStr(currentTime, playTimeNeedHour)
            }
        }
    }

    override fun setSeekbarCurrentPosition(position: Long) {
        if (hasGetDuration) {
            binding.seekbar.progress = position.toInt()
        }
    }

    override fun getCurPlayPos(): Long {
        return binding.seekbar.progress.toLong()
    }

    override fun getVideoView(): VodPlayerView? {
        return vodPlayerView
    }

    override fun setPvNumVisible(isShow: Boolean) {
        binding.tvWatchNum.visibility = if (isShow) View.VISIBLE else View.GONE
    }

    @SuppressLint("SetTextI18n")
    override fun setPvNum(showNum: Int) {
        binding.tvWatchNum.text = "${showNum}人看过"
    }

    override fun setQuality(qualities: List<String>?, auto: Boolean) {
        handQualities(qualities)
    }

    override fun setQualityText(dpi: String) {
        binding.tvQuality.text = VhallUtils.covertQualityTxt(dpi)
        if (TextUtils.equals(Constants.Rate.DPI_AUDIO, dpi)) {
            //音频画面
            binding.layAudio.visibility = View.VISIBLE
            ImgHelper.loadAnimatedWebP(binding.ivAudioImg, R.mipmap.sm_bk_pla_whi)
        } else {
            binding.layAudio.visibility = View.GONE
        }
    }

    override fun setPlaySpeedText(speed: Float) {
        binding.tvSpeed.text = VhallUtils.covertSpeedTxt(speed)
    }

    override fun onNetError(errorMsg: String) {
        LogUtils.pop(errorMsg)
    }

    /**
     * 显示错误信息
     */
    override fun showError(showImg: Boolean, errorMsg: String) {
        binding.layError.root.visibility = View.VISIBLE
        binding.errorShare.visibility = View.VISIBLE
        binding.layError.errorMsg.text = errorMsg
        if (showImg) {
            ImgHelper.loadAnimatedWebP(binding.layError.errorImg, R.mipmap.sm_bk_pla_whi)
            binding.layError.errorImg.visibility = View.VISIBLE
        } else {
            binding.layError.errorImg.visibility = View.GONE
        }
    }

    /**
     * 隐藏错误信息
     */
    override fun hideError() {
        binding.layError.root.visibility = View.GONE
        binding.errorShare.visibility = View.GONE
        showProgressbar(false)
    }

    override fun showProgressbar(show: Boolean) {
        if (show) {
            binding.progressBar.visibility = View.VISIBLE
            val animationDrawable = binding.progressBar.background as AnimationDrawable
            if (!animationDrawable.isRunning) {
                animationDrawable.start()
            }
        } else {
            binding.progressBar.visibility = View.GONE
        }
    }

    override fun onClick(v: View) {
        super.onClick(v)
        when (v.id) {
            R.id.tv_speed -> {
                val bundle = NavHelper.obtainArg(
                    "",
                    SmVideoSettingsBaseDlg.VALUE_CUR_SELECTED_VALUE, currentSpeed,
                    SmVideoSettingsBaseDlg.VALUE_TV_MODE, false,
                    SmVideoSettingsBaseDlg.VALUE_SELECTION_TYPE, 1
                )
                SmVideoSettingsLandSelectedDlg.getInstance(bundle, object : SmVideoSettingsLandSelectedDlg.OnSmVideoLandSettingsItemClickListener {
                    override fun onLandFuncItemClick(actionType: Int, selectedValue: String?) {
                        if (actionType == 1) {
                            selectedValue?.let {
                                if (!TextUtils.equals(currentSpeed, it)) {
                                    currentSpeed = it
                                    mPresenter?.setSpeed(MathUtils.forValF(it, 1.0f))
                                }
                            }
                        }
                    }
                }).show(childFragmentManager, null)
            }
        }
    }

    override fun changeOrientation(isPortrait: Boolean) {
        if (isPortrait) {
            //竖屏
            binding.tvTitle.visibility = View.INVISIBLE
            binding.ivShare.visibility = View.VISIBLE
            binding.ivFullscreen.visibility = View.VISIBLE
            binding.tvQuality.visibility = View.GONE
            binding.tvSpeed.visibility = View.GONE
            binding.containerLandChat.visibility = View.GONE
        } else {
            //横屏
            startTitleMarquee()
            binding.tvTitle.visibility = View.VISIBLE
            binding.ivShare.visibility = View.GONE
            binding.ivFullscreen.visibility = View.GONE
            binding.tvQuality.visibility = View.VISIBLE
            binding.tvSpeed.visibility = View.VISIBLE
            binding.containerLandChat.visibility = View.VISIBLE

        }
    }

    override fun onVideoLayoutDoubleTap() {
        mPresenter?.onPlayClick()
    }

    override fun onVideoLayoutSingleTap() {
        if (binding.layoutTop.visibility == View.VISIBLE) {
            binding.layoutTop.visibility = View.GONE
        } else {
            binding.layoutTop.visibility = View.VISIBLE
            startTitleMarquee()
        }
        if (binding.layoutBottom.visibility == View.VISIBLE) {
            binding.layoutBottom.visibility = View.GONE
        } else {
            binding.layoutBottom.visibility = View.VISIBLE
        }
    }

    override fun startTitleMarquee() {
        if (activity == null) return
        if (binding.tvTitle.visibility == View.VISIBLE) {
            binding.tvTitle.requestFocus()
        }
    }

    override fun getTopControlLayout(): View? {
        return if (activity == null) {
            null
        } else {
            binding.layoutTop
        }
    }

    override fun getTopMenuView(): View? {
        return if (activity == null) {
            null
        } else {
            binding.ivMenu
        }
    }

    override fun getBottomControlLayout(): View? {
        return if (activity == null) {
            null
        } else {
            binding.layoutBottom
        }
    }

    override fun isLiving() = false

    override fun isPlaying(): Boolean {
        return mPresenter?.isPlaying() ?: false
    }

    override fun onDlnaStart() {
        startDismissControlViewTimer()
        handleDlnaUI(true)
        setPlayIcon(true)
    }

    private fun handleDlnaUI(isDlna: Boolean) {
        hideError()
        if (isDlna) {
            binding.layDlna.visibility = View.VISIBLE
            binding.ivPlay.visibility = View.GONE
            binding.tvPlayTime.visibility = View.GONE
            binding.seekbar.visibility = View.GONE
            binding.tvTotalTime.visibility = View.GONE
            binding.ivFullscreen.visibility = View.GONE
            binding.ivMenu.visibility = View.GONE
        } else {
            binding.layDlna.visibility = View.GONE
            binding.ivPlay.visibility = View.VISIBLE
            binding.tvPlayTime.visibility = View.VISIBLE
            binding.seekbar.visibility = View.VISIBLE
            binding.tvTotalTime.visibility = View.VISIBLE
            binding.ivFullscreen.visibility = View.VISIBLE
            binding.ivMenu.visibility = View.VISIBLE
        }
    }

    override fun onDlnaPause() {
        startDismissControlViewTimer()
        handleDlnaUI(true)
        setPlayIcon(false)
    }

    override fun onDlnaStop() {
        mCurrentDevice = null
        startDismissControlViewTimer()
        handleDlnaUI(false)
        mPresenter?.onPlayClick()
    }

    override fun onDlnaCurrentPosition(curTime: String, duration: String) {
//        cancelDismissControlViewTimer()
//        showControlView()
//        hideError()
//        binding.layDlna.visibility = View.VISIBLE
//        binding.layoutBottom.visibility = View.GONE
//        setSeekbarMax(VhallUtils.convertTimeStrToSecond(duration) * 1000)
//        setSeekbarCurrentPosition(VhallUtils.convertTimeStrToSecond(curTime) * 1000)
    }

    override fun onDlnaError(errorCode: Int, errorMsg: String?) {
        LogUtils.pop("投屏失败")
        mCurrentDevice = null
    }
}