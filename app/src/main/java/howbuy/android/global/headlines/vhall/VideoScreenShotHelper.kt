package howbuy.android.global.headlines.vhall

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.howbuy.fund.base.aty.AbsAty
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.imageloader.BitmapCallback.SimpleBitmapCallback
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.ScreenShotUtils
import com.howbuy.lib.utils.SysUtils
import com.howbuy.lib.utils.ViewUtils
import com.howbuy.scanner.encode.CodeCreator
import howbuy.android.global.R
import howbuy.android.global.headlines.vhall.video.vm.SmLivePosterBody
import howbuy.android.global.request.GlobalRequest
import html5.screenshot.GlobalCreatePosterPreviewerUtils

/**
 * class description.
 * 直播海报截图
 * <AUTHOR>
 * @date 2021/4/1
 */
@SuppressLint("StaticFieldLeak")
object VideoScreenShotHelper {

    /**
     * 根据liveId,请求分享对象,并弹出分享框
     * @param liveId 直播id, 与 posterBody 两个参数至少传入一个
     * @param posterBody 海报数据与  liveId 两个参数至少传入一个
     */
    fun showPostCardByLiveId(activity: AbsAty, liveId: String?, posterBody:SmLivePosterBody?) {
        if (TextUtils.isEmpty(liveId) && posterBody == null){
            return
        }
        //请求海报及直播视频基本数据
        if (posterBody != null) {
            shotScreenOnlyForVideo(activity, posterBody)
        }else {
            GlobalRequest.reqSmLivingPosterInfo(liveId, 1) {
                shotScreenOnlyForVideo(activity, it.mData as SmLivePosterBody)
            }
        }
    }

    private var screenView: View? = null
    private var ivMainPic: ImageView? = null
    private var ivHeader: ImageView? = null
    private var ivQrcode: ImageView? = null
    private var tvUserName: TextView? = null
    private var tvTips: TextView? = null

    private fun shotScreenOnlyForVideo(activity: AbsAty, mLiveBody: SmLivePosterBody) {
        screenView = View.inflate(activity, R.layout.include_sm_video_outlink_shot_screen_layout, null)
        ivMainPic = screenView!!.findViewById<View>(R.id.iv_shotscreen_main) as ImageView
        ivHeader = screenView!!.findViewById<View>(R.id.iv_person_head) as ImageView
        ivQrcode = screenView!!.findViewById<View>(R.id.iv_screenshot_qrcode) as ImageView
        tvUserName = screenView!!.findViewById<View>(R.id.tv_screenshot_username) as TextView
        tvTips = screenView!!.findViewById<View>(R.id.tv_screenshot_tips) as TextView
        activity.showAlermDlg("海报生成中...", false, false)

        if (TextUtils.isEmpty(mLiveBody.mimgbig)) {
            setPosetUI(activity, null, mLiveBody)
        } else {
            ImgHelper.load(mLiveBody.mimgbig, null, object : SimpleBitmapCallback() {
                override fun onSuccess(bitmap: Bitmap) {
                    setPosetUI(activity, bitmap, mLiveBody)
                }

                override fun onFailed(e: Exception) {
                    setPosetUI(activity, null, mLiveBody)
                }
            })
        }
    }

    /*
     *需要将 Head的pic url传递过来,内容文案,二维码连接内容,当前视频是否直播状态
     */
    private fun setPosetUI(activity: AbsAty, bitmap: Bitmap?, mLiveBody: SmLivePosterBody) {
        try {
            activity.showAlermDlg(null, 0)
            if (bitmap != null) {
                val ww = SysUtils.getWidth(activity)
                val rate = bitmap.width * 1f / bitmap.height
                val wh = (ww / rate).toInt()
                val params = ivMainPic?.layoutParams
                params?.height = wh
                ivMainPic?.setImageBitmap(bitmap)
                ivMainPic?.layoutParams = params!!
            } else {
                ViewUtils.setVisibility(ivMainPic, View.GONE)
            }
            //根据内容生成二维码,
            val qrcodeStr: String? = mLiveBody.qrCodeLink
            if (TextUtils.isEmpty(qrcodeStr)) {
                //显示默认的人像
                ivHeader?.visibility = View.VISIBLE
                ivQrcode?.visibility = View.GONE
            } else {
                //生成二维码
                ivHeader?.visibility = View.GONE
                ivQrcode?.visibility = View.VISIBLE
                ivQrcode?.setImageBitmap(
                    CodeCreator.createQRCode(
                        mLiveBody.qrCodeLink,
                        DensityUtils.dip2px(110f),
                        DensityUtils.dip2px(110f),
                        BitmapFactory.decodeResource(activity.resources, R.drawable.ic_launcher)
                    )
                )
            }
            tvUserName?.text = FundTextUtils.showTextEmpty(mLiveBody.name, "好买全球")
            tvTips?.text = FundTextUtils.showTextEmpty(mLiveBody.word)
            //初始化底部view 视图和数据后,需要调用layoutview方法
            ScreenShotUtils.layoutView(activity, screenView)
            val footBitmapTemp = ScreenShotUtils.shotByTagView(screenView, null)
            GlobalCreatePosterPreviewerUtils().initPopWind(activity.window.decorView, activity, footBitmapTemp)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}