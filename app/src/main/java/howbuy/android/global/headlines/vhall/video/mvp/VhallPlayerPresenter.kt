package howbuy.android.global.headlines.vhall.video.mvp

import com.vhall.business_support.dlna.DMCControl
import com.vhall.business_support.dlna.DeviceDisplay
import org.fourthline.cling.android.AndroidUpnpService

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/1/14
 * @Version v260
 */
interface VhallPlayerPresenter : VhallVideoPresenter {

    /**
     * 点击播放暂停
     */
    fun onPlayClick()

    /**
     * 播放器初始化成功
     */
    fun onPlayerInitSuccess()

    /**
     * 开始播放
     */
    fun startPlay()

    /**
     * 继续播放
     */
    fun replay()

    /**
     * 切换分辨率
     */
    fun onSwitchPixel(pix: String, auto: Boolean, forceSetPiex:Boolean)

    /**
     * 返回键（横屏切回竖屏，竖屏退出页面）
     */
    fun onClickBack()

    /**
     * 有人进入直播间
     */
    fun onlineNum()

    /**
     * 获取投屏控制
     */
    fun getDlnaControl(deviceDisplay: DeviceDisplay?, service: AndroidUpnpService?): DMCControl

    /**
     * 获取当前播放进度
     */
    fun currentPlayTime() = 0L

    /**
     * 显示开始投屏UI
     */
    fun onDlnaStart()

    /**
     * 显示投屏暂停UI
     */
    fun onDlnaPause()

    /**
     * 显示投屏结束UI
     */
    fun onDlnaStop()

    /**
     * 显示投屏错误信息
     */
    fun onDlnaError(errorCode: Int, errorMsg: String?)

    /**
     * 停止投屏
     */
    fun stopDlna()

    /**
     * Fragment onResume
     */
    fun onFragmentResume()

    /**
     * Fragment onResume
     */
    fun onFragmentPause()

    /**
     * Fragment onResume
     */
    fun onFragmentStop()

    /**
     * Fragment onDestroy
     */
    fun onFragmentDestroy()

    /**
     * 释放播放器资源
     */
    fun destoryPlayer()

}