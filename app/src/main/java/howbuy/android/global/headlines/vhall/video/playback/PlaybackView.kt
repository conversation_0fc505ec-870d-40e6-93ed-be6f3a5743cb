package howbuy.android.global.headlines.vhall.video.playback

import com.vhall.player.vod.VodPlayerView
import howbuy.android.global.headlines.vhall.video.mvp.VhallPlayerView

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/1/14
 * @Version v260
 */
interface PlaybackView : VhallPlayerView<PlaybackPresenter> {

    /**
     * 获取播放器控件（vhall使用）
     */
    fun getVideoView(): VodPlayerView?

    /**
     * 设置进度条最大值
     */
    fun setSeekbarMax(max: Long)

    /**
     * 设置进度条进度
     */
    fun setSeekbarCurrentPosition(position: Long)

    /**
     * 显示设置的倍速
     */
    fun setPlaySpeedText(speed: Float)
}