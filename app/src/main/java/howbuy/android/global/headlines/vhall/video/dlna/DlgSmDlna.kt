package howbuy.android.global.headlines.vhall.video.dlna

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.DialogInterface
import android.view.Gravity
import android.view.View
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.widget.LinearLayout
import com.howbuy.fund.base.BaseDialogFragment
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.vhall.business_support.dlna.DeviceDisplay
import howbuy.android.global.R
import howbuy.android.global.databinding.DlgSmDlnaBinding
import io.reactivex.Observable
import io.reactivex.Observer
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import java.util.concurrent.TimeUnit

/**
 * @Description vhall 投屏弹框
 * <AUTHOR>
 * @Date 2023/1/9
 * @Version v806
 */
@SuppressLint("NotifyDataSetChanged")
class DlgSmDlna : BaseDialogFragment() {

    private lateinit var binding: DlgSmDlnaBinding
    private val mAnimation by lazy {
        AnimationUtils.loadAnimation(context, R.anim.anim_center_rotate)
    }
    private val mDeviceAdp by lazy {
        AdpDlnaDevice(mListener)
    }

    companion object {
        const val DEVICE_LIST = "DEVICE_LIST"
        private var mListener: DlnaListener? = null
        fun getInstance(listener: DlnaListener): DlgSmDlna {
            val fragment = DlgSmDlna()
            mListener = listener
            return fragment
        }
    }

    override fun onCreateDialog(): Dialog {
        val dialog = Dialog(activity!!, R.style.cpay_MyDialog)
        binding = DlgSmDlnaBinding.inflate(activity!!.layoutInflater)
        dialog.setContentView(binding.root)
        initView()
        dialog.window?.setWindowAnimations(R.style.AnimBottom)
        dialog.window?.setGravity(Gravity.BOTTOM)
        dialog.window?.setLayout(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT)
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }

    private var timeOutDisposable: Disposable? = null
    private fun initView() {
        Observable.interval(30L, 0L, TimeUnit.SECONDS)
            .take(1)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : Observer<Long> {
                override fun onSubscribe(d: Disposable) {
                    timeOutDisposable = d
                }

                override fun onNext(aLong: Long) {
                }

                override fun onError(e: Throwable) {
                    e.printStackTrace()
                }

                override fun onComplete() {
                    timeOutDisposable = null
                    showEmpty()
                }
            })
        binding.ivClose.setOnClickListener { dismiss() }
        binding.rv.adapter = mDeviceAdp
        binding.rv.addItemDecoration(XLinearBuilder(context).setSpacing(10f).setShowLastLine(true).build())
        showLoading()
//        binding.layLoading.setOnClickListener {
//            if (mDeviceAdp.data.isNotEmpty()) {
//                showLoading()
//                mListener?.onSearch()
//            }
//        }
    }

    fun clear() {
        mDeviceAdp.setList(mutableListOf())
        showLoading()
    }

    fun addAll(list: MutableList<DeviceDisplay>) {
        if (list.isNotEmpty()) {
            timeOutDisposable?.dispose()
        }
        mDeviceAdp.setList(list)
        hideLoading()
        if (list.isEmpty()) {
            showLoading()
        } else {
            hideLoading()
        }
    }

    fun setSelect(selectDevice: DeviceDisplay?) {
        mDeviceAdp.onSelect(selectDevice)
    }

    fun deviceAdded(device: DeviceDisplay) {
        timeOutDisposable?.dispose()
        mDeviceAdp.addData(device)
        hideLoading()
    }

    fun deviceRemoved(device: DeviceDisplay) {
        mDeviceAdp.remove(device)
        if (mDeviceAdp.data.isEmpty()) {
            showEmpty()
        } else {
            hideLoading()
        }
    }

    private fun showLoading() {
        binding.layLoading.visibility = View.VISIBLE
        binding.tvEmpty.visibility = View.GONE
        binding.ivLoading.clearAnimation()
//        binding.tvLoading.text = "正在搜索可投屏设备"
        mAnimation.interpolator = LinearInterpolator()
        binding.ivLoading.startAnimation(mAnimation)
        if (mDeviceAdp.data.isEmpty()) {
            binding.rv.visibility = View.GONE
        }
    }

    private fun hideLoading() {
        binding.layLoading.visibility = View.GONE
        binding.tvEmpty.visibility = View.GONE
        binding.ivLoading.clearAnimation()
//        binding.tvLoading.text = "重新搜索可投屏设备"
        binding.rv.visibility = View.VISIBLE
    }

    private fun showEmpty() {
        binding.layLoading.visibility = View.GONE
        binding.tvEmpty.visibility = View.VISIBLE
        binding.ivLoading.clearAnimation()
        binding.rv.visibility = View.GONE
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        timeOutDisposable?.dispose()
        mListener = null
    }

}