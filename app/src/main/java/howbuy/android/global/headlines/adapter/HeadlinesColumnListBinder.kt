package howbuy.android.global.headlines.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.binder.BaseItemBinder
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.utils.handleTitle
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.global.common.banner.BannerImageAdp
import com.howbuy.global.common.widgets.OverScrollLayout
import com.howbuy.global.user.BannerClickMgr
import com.howbuy.global.user.ad.AdItem
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.LogUtils
import com.zhpan.bannerview.BannerViewPager
import howbuy.android.global.PATH_FRAG_COLUMN_LIST
import howbuy.android.global.R
import howbuy.android.global.headlines.entity.HeadlinesColumnList

/**
 * @Description 资讯 推荐专栏
 * <AUTHOR>
 * @Date 2025/1/9
 * @Version v2.6.0
 */
class HeadlinesColumnListBinder : BaseItemBinder<HeadlinesColumnList, BaseViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
        return BaseViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.item_headlines_column_list, parent, false)
        ).also {
            it.getView<RecyclerView>(R.id.rv_recommend_zl).apply {
                addItemDecoration(XLinearBuilder(context).setSpacing(10f).build())
            }
            it.getView<OverScrollLayout>(R.id.over_scroll).setListener {
                RouterHelper.launchFrag(context, PATH_FRAG_COLUMN_LIST)
            }
            it.getView<OverScrollLayout>(R.id.over_scroll).postDelayed({
                ((it.getView<RecyclerView>(R.id.rv_recommend_zl).layoutManager) as? LinearLayoutManager)?.scrollToPositionWithOffset(0, 2)
            }, 100)
        }
    }

    override fun convert(holder: BaseViewHolder, data: HeadlinesColumnList) {
        holder.setText(
            R.id.tv_title_zl, SpannableUtils.formatStr(
                SpannableItem("精\n选\n", ColorUtils.parseColor("#333333")),
                SpannableItem("专\n栏", ColorUtils.parseColor("#E7001D")),
            )
        )
        val adapter = AdpHeadlinesRecColumn(data.list)
        val footerView = View.inflate(context, R.layout.headlines_column_loadmore, null)
        adapter.addFooterView(footerView, 0, LinearLayout.HORIZONTAL)
        footerView.setOnClickListener {
            RouterHelper.launchFrag(context, PATH_FRAG_COLUMN_LIST)
        }
        holder.getView<RecyclerView>(R.id.rv_recommend_zl).adapter = adapter
    }

}