package howbuy.android.global.headlines.vhall.video.mvp

import com.vhall.business.data.RequestCallback

/**
 * @Description 微吼直播-主页面相关操作
 * <AUTHOR>
 * @Date 2025/1/14
 * @Version v260
 */
interface VhallVideoPresenter : VhallWatchContract.BasePresenter {

    /**
     * 举手申请上麦
     * @param type 1同意上麦 2拒绝上麦 3超时无响应
     */
    fun onRaiseHand(type: Int, callback: RequestCallback?) {}

    /**
     * 回复上麦邀请
     * @param type 1同意上麦 2拒绝上麦 3超时无响应
     */
    fun replyInvite(type: Int, callback: RequestCallback?) {}

    /**
     * 横竖屏切换
     * @param isDoc 是否文档全屏切换
     */
    fun changeScreenOri(isDoc:Boolean)
}