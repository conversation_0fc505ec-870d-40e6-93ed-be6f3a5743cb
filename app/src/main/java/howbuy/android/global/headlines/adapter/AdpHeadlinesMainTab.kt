package howbuy.android.global.headlines.adapter

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.howbuy.fund.base.config.ValConfig
import howbuy.android.global.headlines.FragHeadlinesNews
import howbuy.android.global.headlines.FragHeadlinesVideo

/**
 * 海外资讯首页(包含资讯、视听)
 * <AUTHOR>
 * @Date 2025/1/9
 * @Version v2.6.0
 */
class AdpHeadlinesMainTab(
    val frag: Fragment,
//    fm: FragmentManager,
    private val defaultTab: Int,
    private val defaultSubTab: String?
) :
//    AbsFragPageAdp(fm) {
    FragmentStateAdapter(frag) {

    override fun createFragment(position: Int): Fragment {
//    override fun getItem(position: Int): Fragment {
        val bundle = Bundle()
        if (position == 0) {
            if (defaultTab == 0) {
                bundle.putString(ValConfig.IT_ID, defaultSubTab)
            }
            val fragment = FragHeadlinesNews()
//            val fragment = FragSmBest()
            fragment.arguments = bundle
            return fragment
        } else {
            if (defaultTab == 1) {
                bundle.putString(ValConfig.IT_ID, defaultSubTab)
            }
            val fragment = FragHeadlinesVideo()
            fragment.arguments = bundle
            return fragment
        }
    }

//    override fun getTag(position: Int) = position.toString()

    override fun getItemCount() = 2
//    override fun getCount() = 2

}