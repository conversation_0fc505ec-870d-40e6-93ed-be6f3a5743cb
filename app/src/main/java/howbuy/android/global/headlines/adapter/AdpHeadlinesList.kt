package howbuy.android.global.headlines.adapter

import com.chad.library.adapter.base.BaseBinderAdapter
import howbuy.android.global.headlines.entity.HeadlinesColumnList
import howbuy.android.global.headlines.entity.HeadlinesHotList
import howbuy.android.global.headlines.entity.HeadlinesItem

/**
 * @Description 资讯列表
 * <AUTHOR>
 * @Date 2024/5/14
 * @Version v2.1.0
 * @param pageType 1:首页 资讯列表样式(圆角边框); 2:头条专栏列表样式(圆角背景) ; 3:搜索 默认: 头条主页面列表样式
 */
class AdpHeadlinesList(pageType: Int, analytics: ((HeadlinesItem) -> Unit)? = null) : BaseBinderAdapter() {
    init {
        addItemBinder(HeadlinesItem::class.java, HeadlinesItemBinder(pageType, analytics))
            .addItemBinder(HeadlinesHotList::class.java, HeadlinesHotListBinder())
            .addItemBinder(HeadlinesColumnList::class.java, HeadlinesColumnListBinder())
    }
}