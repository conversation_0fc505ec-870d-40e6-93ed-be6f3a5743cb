package howbuy.android.global.splashadds;

import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;

import com.howbuy.lib.utils.DateUtils;
import com.howbuy.lib.utils.LogUtils;

import java.util.Objects;

/**
 * 创建日期：2018/8/23
 *
 * <AUTHOR>
 */
public class AdvertInfo implements Parcelable {
    /**
     * 图片
     */
    public String picUrl;
    /**
     * 跳转地址
     */
    public String urlLink;
    /**
     * 标题
     */
    public String title;
    /**
     * 扩展字段1
     */
    public String title1;
    /**
     * 扩展字段2
     */
    public String title2;

    /***/
    public String publishStartTime;
    /***/
    public String publishEndTime;

    public String getPicUrl() {
        return picUrl;
    }

    public String getUrlLink() {
        return urlLink;
    }

    public String getTitle() {
        return title;
    }

    public String getTitle1() {
        return title1;
    }

    public String getTitle2() {
        return title2;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdvertInfo that = (AdvertInfo) o;
        return Objects.equals(picUrl, that.picUrl) && Objects.equals(urlLink, that.urlLink) && Objects.equals(title, that.title) && Objects.equals(title1, that.title1) && Objects.equals(title2, that.title2) && Objects.equals(publishStartTime, that.publishStartTime) && Objects.equals(publishEndTime, that.publishEndTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(picUrl, urlLink, title, title1, title2, publishStartTime, publishEndTime);
    }

    /**
     * <ol>当前的开屏广告是否可以显示
     * <li>发布时间存在值：仅当当前系统时间大于等于该时间戳，显示该广告</li>
     * <li>结束时间存在值：仅当当前系统时间小于等于该时间戳，显示该广告</li>
     * <li>若两者均为空，且广告为上架时，则长期显示</li>
     * </ol>
     * @return true-可以显示；false-不可以显示
     */
    public boolean canShowSplash() {
        boolean canShow = true;
        long timeNow = System.currentTimeMillis();
        if (TextUtils.isEmpty(publishStartTime) && TextUtils.isEmpty(publishEndTime)) {
            LogUtils.d("Debug-Splash", "publishStartTime & publishEndTime are empty");
            return true;
        }
        if (!TextUtils.isEmpty(publishStartTime)) {
            long publishStartTimeStamp = DateUtils.getTimeFormatLong(publishStartTime, DateUtils.DATEF_YMD_2);
            canShow = publishStartTimeStamp == 0 || publishStartTimeStamp <= timeNow; //解析失败，视为可显示
            LogUtils.d("Debug-Splash", "publishStartTimeStamp <= timeNow: " + canShow + ", publishStartTime:" + publishStartTime);
        }
        if (!TextUtils.isEmpty(publishEndTime)) {
            long publishEndTimeStamp = DateUtils.getTimeFormatLong(publishEndTime, DateUtils.DATEF_YMD_2);
            canShow = publishEndTimeStamp == 0 || publishEndTimeStamp >= timeNow; //解析失败，视为可显示
            LogUtils.d("Debug-Splash", "publishEndTimeStamp >= timeNow: " + canShow + ", publishEndTime:" + publishEndTime);
        }
        return canShow;
    }

    public static AdvertInfo createEmpty() {
        return new AdvertInfo();
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.picUrl);
        dest.writeString(this.urlLink);
        dest.writeString(this.title);
        dest.writeString(this.title1);
        dest.writeString(this.title2);
        dest.writeString(this.publishStartTime);
        dest.writeString(this.publishEndTime);
    }

    public void readFromParcel(Parcel source) {
        this.picUrl = source.readString();
        this.urlLink = source.readString();
        this.title = source.readString();
        this.title1 = source.readString();
        this.title2 = source.readString();
        this.publishStartTime = source.readString();
        this.publishEndTime = source.readString();
    }

    public AdvertInfo() {
    }

    protected AdvertInfo(Parcel in) {
        this.picUrl = in.readString();
        this.urlLink = in.readString();
        this.title = in.readString();
        this.title1 = in.readString();
        this.title2 = in.readString();
        this.publishStartTime = in.readString();
        this.publishEndTime = in.readString();
    }

    public static final Creator<AdvertInfo> CREATOR = new Creator<AdvertInfo>() {
        @Override
        public AdvertInfo createFromParcel(Parcel source) {
            return new AdvertInfo(source);
        }

        @Override
        public AdvertInfo[] newArray(int size) {
            return new AdvertInfo[size];
        }
    };
}