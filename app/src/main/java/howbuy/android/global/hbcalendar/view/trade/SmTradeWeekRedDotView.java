package howbuy.android.global.hbcalendar.view.trade;

import android.content.Context;
import android.view.View;
import android.widget.LinearLayout;

import com.howbuy.lib.utils.DensityUtils;

import org.joda.time.LocalDate;

import java.util.List;

import howbuy.android.global.hbcalendar.entity.NDate;
import howbuy.android.global.hbcalendar.listener.ITradeCalendarDataRenderListener;
import howbuy.android.global.hbcalendar.listener.OnClickWeekViewListener;
import howbuy.android.global.hbcalendar.painter.InnerPainter;
import howbuy.android.global.hbcalendar.utils.JodaUtil;
import howbuy.android.global.hbcalendar.view.BaseCalendarViewPager;

/**
 * <AUTHOR>
 */
public class SmTradeWeekRedDotView extends BaseTradeView {

    private OnClickWeekViewListener mOnClickWeekViewListener;
    private ITradeCalendarDataRenderListener mWeekDataRenderListener;
    public SmTradeWeekRedDotView(Context context, BaseCalendarViewPager calendar, LocalDate localDate,
                                 int weekFirstDayType, OnClickWeekViewListener onClickWeekViewListener,
                                 ITradeCalendarDataRenderListener weekDataRenderListener) {
        super(context, calendar, localDate, weekFirstDayType, false);
        this.mWeekDataRenderListener = weekDataRenderListener;
        this.mOnClickWeekViewListener = onClickWeekViewListener;
        drawCalendarTagView();
    }

    /**
     * 通过这种方式刷新数据需要注意:
     * 1. 页面上的UI已经渲染过,也可能数据也渲染过
     * 2. 在类似TextView.getText()时, 就存在取值不正确的情况,多次渲染,取值造成的内容重复问题
     * 3. 在刷新数据时,需要关注 类似的问题,并对应处理
     */
    public void resetData(){
        drawCalendarTagView();
    }


    @Override
    protected void handFlagViewUI(int i, int j, NDate curDate) {
        //设置周日历的UI样式(显示小红点)
        try {
            LinearLayout parent = (LinearLayout) textViewList.get(i)[j].getParent();
            LayoutParams params = (LayoutParams) parent.getLayoutParams();
            params.topMargin = DensityUtils.dp2px(10);
            if (mWeekDataRenderListener != null) {
                mWeekDataRenderListener.onRenderTagStyleForFixedProd(i,j,
                        curDate, null, null,null, textTags, null,
                        null, null, null
                ,null, null);
            } else {
                List<LocalDate> tagList = null;
                if (mCalendar.getCalendarPainter() instanceof InnerPainter) {
                    tagList = ((InnerPainter) mCalendar.getCalendarPainter()).getTagDateList();
                }
                LocalDate date = curDate.localDate;
                //实现标记数据UI
                if (tagList != null && tagList.contains(date) /*&& !date.equals(Attrs.mSelectDate)*/) {
                    textTags.get(i)[j].setVisibility(View.VISIBLE);
                } else {
                    textTags.get(i)[j].setVisibility(View.GONE);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected List<NDate> getNCalendar(LocalDate localDate, int type) {
        return JodaUtil.getWeekCalendar(localDate, type);
    }

    @Override
    protected void onCustomClick(NDate nDate, LocalDate initialDate) {
        if (mOnClickWeekViewListener != null) {
            mOnClickWeekViewListener.onClickCurrentWeek(nDate.localDate);
        }
    }

    @Override
    public boolean isEqualsMonthOrWeek(LocalDate date, LocalDate initialDate) {
        return mDateList.contains(new NDate(date));
    }

}
