package howbuy.android.global.hbcalendar.view;

import android.content.Context;


import org.joda.time.LocalDate;

import java.util.List;

import howbuy.android.global.hbcalendar.entity.NDate;
import howbuy.android.global.hbcalendar.listener.OnClickWeekViewListener;
import howbuy.android.global.hbcalendar.utils.JodaUtil;

/**
 * Created by tao.liang on 2019/8/11.
 * 周日历
 */
public class WeekView extends BaseCalendarView {

    private OnClickWeekViewListener mOnClickWeekViewListener;

    public WeekView(Context context, LocalDate localDate, int weekFirstDayType, OnClickWeekViewListener onClickWeekViewListener) {
        super(context, localDate, weekFirstDayType);
        this.mOnClickWeekViewListener = onClickWeekViewListener;
    }

    @Override
    protected List<NDate> getNCalendar(LocalDate localDate, int type) {
        return JodaUtil.getWeekCalendar(localDate, type);
    }

    @Override
    protected void onClick(NDate nDate, LocalDate initialDate) {
        if (mOnClickWeekViewListener != null){
            mOnClickWeekViewListener.onClickCurrentWeek(nDate.localDate);
        }
    }

    @Override
    protected void disableClick(NDate clickNData) {
        if (mOnClickWeekViewListener != null){
            mOnClickWeekViewListener.onDisableClick(clickNData.localDate);
        }
    }

    @Override
    public boolean isEqualsMonthOrWeek(LocalDate date, LocalDate initialDate) {
        return mDateList != null && mDateList.contains(new NDate(date));
    }

    @Override
    public void setSelectDate(LocalDate localDate) {

    }

    @Override
    protected int getTwoDateCount(LocalDate startDate, LocalDate endDate, int type) {
        return JodaUtil.getIntervalWeek(startDate, endDate, type);
    }

    @Override
    protected LocalDate getIntervalDate(LocalDate localDate, int count) {
        if (localDate == null) {
            //默认相距一个月
            return new LocalDate();
        }
        return localDate.plusWeeks(count);
    }

    @Override
    public boolean isWeek() {
        return true;
    }

}
