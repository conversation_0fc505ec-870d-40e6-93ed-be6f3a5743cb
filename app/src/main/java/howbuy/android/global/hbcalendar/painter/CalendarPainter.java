package howbuy.android.global.hbcalendar.painter;

import android.graphics.Canvas;
import android.graphics.Rect;

import howbuy.android.global.hbcalendar.entity.NDate;

import org.joda.time.LocalDate;

import java.util.List;


public interface CalendarPainter {

    /**
     * 设置红点数据列表
     *
     * @param tagDateList
     */
    void setTagDateList(List<LocalDate> tagDateList);

    /**
     * 获取红点数据
     * @return
     */
    List<LocalDate> getTagDateList();

    void setDrawSelectDate(boolean draw);

    /**
     * 滑动切换页面的时候是否选中上个月的同一天或者上周同一天
     * @return
     */
    boolean getDrawSelectDate();

    /**
     * 绘制今天的日期
     *
     * @param canvas
     * @param rect
     * @param nDate
     * @param isSelect 今天是否被选中
     */
    void onDrawToday(Canvas canvas, Rect rect, NDate nDate, boolean isSelect);


    /**
     * 绘制当前月或周的日期
     *
     * @param canvas
     * @param rect
     * @param nDate
     * @param isSelect 是否选中
     */
    void onDrawCurrentMonthOrWeek(Canvas canvas, Rect rect, NDate nDate, boolean isSelect);

    /**
     * 绘制上一月，下一月的日期，周日历不须实现
     *
     * @param canvas
     * @param rect
     * @param nDate
     */
    void onDrawNotCurrentMonth(Canvas canvas, Rect rect, NDate nDate);


    /**
     * 绘制不可用的日期，和方法setDateInterval(startFormatDate, endFormatDate)对应
     *
     * @param canvas
     * @param rect
     * @param nDate
     */
    void onDrawDisableDate(Canvas canvas, Rect rect, NDate nDate);


    /**
     * 绘制背景颜色
     *
     * @param canvas
     * @param rect
     */
    void onDrawHintBackGroundColor(Canvas canvas, Rect rect, int mlines);


}
