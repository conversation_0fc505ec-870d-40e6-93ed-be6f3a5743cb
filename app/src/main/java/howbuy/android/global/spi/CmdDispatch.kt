package howbuy.android.global.spi

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import com.alibaba.android.arouter.launcher.ARouter
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.nav.NavCompleteCallback
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.push.IPushDispatch
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.global.login_api.ILoginProvider
import com.howbuy.global.login_api.LoginCallback
import com.howbuy.global.login_api.LoginParams
import com.howbuy.global.login_api.LoginResult
import com.howbuy.global.login_api.LoginRouterPath.PATH_ACTIVITY_MAIN
import com.howbuy.global.login_api.LoginType
import com.howbuy.global.user.MineRouterPath
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.MathUtils
import com.howbuy.lib.utils.StrUtils
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import howbuy.android.global.PATH_FRAG_BEST_PAGE
import howbuy.android.global.PATH_FRAG_MESSAGE_CENTER
import howbuy.android.global.PATH_FRAG_NEWS
import howbuy.android.global.headlines.SmHeadLineLauncher
import howbuy.android.global.utils.LauncherFundDetailsMgr

/**
 * description.
 * 好买球App 实现短命令相关逻辑
 * tao.liang
 * 2024/1/18
 */
class CmdDispatch : IPushDispatch {
    private var mContext: Context? = null

    enum class JpushType {
        HWCOMMON, F, SMYX, HWMSG, HWGM, HWYX, OPTIONAL, SMTVLIVING, HWZX, HWGRXX
    }

    private fun getPushType(actionMsg: String?): JpushType? {
        if (TextUtils.isEmpty(actionMsg)) return null
        var resultType: JpushType? = null
        resultType = getPushTypeByCondition(actionMsg, "HWCOMMON", JpushType.HWCOMMON, resultType)
        resultType = getPushTypeByCondition(actionMsg, "F", JpushType.F, resultType)
        resultType = getPushTypeByCondition(actionMsg, "SMYX", JpushType.SMYX, resultType)
        resultType = getPushTypeByCondition(actionMsg, "HWMSG", JpushType.HWMSG, resultType)
        resultType = getPushTypeByCondition(actionMsg, "HWGM", JpushType.HWGM, resultType)
        resultType = getPushTypeByCondition(actionMsg, "HWYX", JpushType.HWYX, resultType)
        resultType = getPushTypeByCondition(actionMsg, "Z", JpushType.OPTIONAL, resultType)
        resultType = getPushTypeByCondition(actionMsg, "SMTVLIVING", JpushType.SMTVLIVING, resultType)
        resultType = getPushTypeByCondition(actionMsg, "HWZX", JpushType.HWZX, resultType)
        resultType = getPushTypeByCondition(actionMsg, "HWGRXX", JpushType.HWGRXX, resultType)

        return resultType
    }

    private fun getPushTypeByCondition(
        actionMsg: String?,
        condition: String,
        type: JpushType, resultType: JpushType?
    ): JpushType? {
        return if (StrUtils.equals(condition, actionMsg)) type else resultType
    }

    override fun dispatchCmd(
        context: Context,
        code: String?,
        value: String?,
        extra: String?,
        message: String?,
        needTitle: Boolean,
        callback: NavCompleteCallback?
    ): Boolean {
        mContext = context
        val cmdType = getPushType(code)
        if (cmdType != null) {
            handCmdBuz(cmdType, value, extra, message, needTitle, callback)
        }
        return cmdType != null
    }

    private fun handCmdBuz(
        cmdType: JpushType?,
        value: String?,
        extra: String?,
        message: String?,
        needTitle: Boolean,
        callback: NavCompleteCallback?
    ) {
        when (cmdType) {
            JpushType.HWCOMMON -> {
                //跳转web页面
                lunchWap(value, message, extra, needTitle, callback)
            }

            JpushType.F -> {
                //跳转档案页面(通过message字段传入是否公募产品)
                LauncherFundDetailsMgr.launcherFundDetails(
                    mContext,
                    value,
                    TextUtils.equals("1", extra),
                    //注意message这个参数字段: 在[AdpHomeProd.launchToDetails()]中通过 message参数传递了sfxspl=1,首页产品,必须都可见
                    message
                )
            }

            JpushType.SMYX -> {
                //海外私募[原:海外优选页面](一级tab和二级tab定位)
                val data = listOfNotNull(
                    value.takeIf { !it.isNullOrEmpty() },
                    extra.takeIf { !it.isNullOrEmpty() }
                ).joinToString("|")
                RouterHelper.launchFrag(
                    mContext, PATH_FRAG_BEST_PAGE,
                    NavHelper.obtainArg("", ValConfig.IT_TYPE, "2", ValConfig.IT_DATA, data)
                )
            }

            JpushType.HWGM -> {
                //海外公募页面
                RouterHelper.launchFrag(
                    mContext, PATH_FRAG_BEST_PAGE,
                    NavHelper.obtainArg("", ValConfig.IT_TYPE, "1")
                )
            }

            JpushType.HWYX -> {
                //value：定位公募或私募（1.公募，2.私募，其他.默认定位）
                //extra: 定位code集合，不同级别code使用英文逗号分隔。当param1为空或非1/2值时，该参数不生效 拼接格式：一级code,二级code。
                //海外优选-公募、私募合并页面
                RouterHelper.launchFrag(
                    mContext, PATH_FRAG_BEST_PAGE,
                    NavHelper.obtainArg("", ValConfig.IT_TYPE, value, ValConfig.IT_DATA, extra)
                )
            }

            JpushType.HWZX -> {
                //海外资讯---资讯 & 视听 支持 传入tabCode定位tab
                //value: 资讯: 0 视听: 1  其他值默认定位0
                //extra: 传入tabCode定位tab
                RouterHelper.launchFrag(
                    mContext, PATH_FRAG_NEWS,
                    NavHelper.obtainArg("", ValConfig.IT_TYPE, value, ValConfig.IT_ID, extra)
                )
            }

//            JpushType.HWZL -> {
//                //专栏详情页
//                RouterHelper.launchFrag(
//                    mContext, PATH_FRAG_COLUMN_DETAILS,
//                    NavHelper.obtainArg("", ValConfig.IT_ID, value)
//                )
//            }

            JpushType.HWMSG -> {
                //海外消息中心列表,支持传入tabindex定位下载
                val tabIndex = MathUtils.forValI(value, 0)
                RouterHelper.launchFrag(
                    mContext, PATH_FRAG_MESSAGE_CENTER, NavHelper.obtainArg(
                        "消息中心", ValConfig.IT_VALUE_1, tabIndex
                    )
                )
            }

            JpushType.OPTIONAL -> {
                //首页-自选
                ARouter.getInstance().build(PATH_ACTIVITY_MAIN)
                    .withInt(
                        Invoker.getInstance().navigation(ILoginProvider::class.java)
                            .getMainActivityParamskeyIndex(), 1
                    )
                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
                    .navigation(mContext)
            }

            JpushType.SMTVLIVING -> {
                //跳转视频/直播
                launchSmVideo(value, extra, callback)
            }

            JpushType.HWGRXX -> {
                launchToUserCenter(callback)
            }

            else -> {}
        }
    }

    /**
     * 跳转到个人中心页
     */
    private fun launchToUserCenter(callback: NavCompleteCallback?) {
        if (mContext is Activity) {
            Invoker.getInstance().navigation(ILoginProvider::class.java)
                .login(LoginParams(mContext as Activity, LoginType.password),
                    ""
                ) { result ->
                    if (result.success) {
                        RouterHelper.launchFragWithCallback(
                            mContext,
                            MineRouterPath.PATH_FRAG_USER_CENTER,
                            NavHelper.obtainArg("个人中心")
                        ) { code, data ->
                            callback?.onNavComplete(code, data?.extras, null)
                            true
                        }
                    } else {
                        callback?.onNavComplete(
                            Activity.RESULT_CANCELED,
                            null,
                            null
                        )
                    }
                }
        } else {
            callback?.onNavComplete(
                Activity.RESULT_CANCELED,
                null,
                null
            )
        }
    }

    private fun launchSmVideo(value: String?, extra: String?, callback: NavCompleteCallback?) {
        SmHeadLineLauncher.launcherToVideoPage(mContext, value, extra, !TextUtils.isEmpty(extra), callback)
    }

    private fun lunchWap(value: String?, message: String?, extra: String?, needTitle: Boolean, callback: NavCompleteCallback?) {
        val b =
            NavHelper.obtainArg(message, ValConfig.IT_URL, value)
        b.putBoolean(ValConfig.IT_TYPE, needTitle)
        val extraArr = extra?.split("|")

        /**
         * E=(1.需要登录；2.需要激活;  3: 不需要原生导航栏,但会显示圆形返回按钮(默认不配置会显示导航栏)；4:不要原生导航条，也不要圆形返回键)
         * 通过 |来分割组合 功能,
         */
        var interceptLoginCodes = 0
        if (extraArr?.contains("1") == true) {
            //需要登录
            interceptLoginCodes = IGlobalInterceptCode.GLOBAL_CHECK_LOGIN
        }
        var interceptActiveCodes = 0
        if (extraArr?.contains("2") == true) {
            //需要激活(一定是要先登录)
            interceptLoginCodes = IGlobalInterceptCode.GLOBAL_CHECK_LOGIN
            interceptActiveCodes = IGlobalInterceptCode.GLOBAL_CHECK_ACTIVE
        }
        b.putIntArray(
            ValConfig.IT_INTERCEPT_CODE,
            intArrayOf(interceptLoginCodes, interceptActiveCodes)
        )
        if (extraArr?.contains("3") == true) {
            //E=3显示导航栏,但会显示圆形返回键
            b.putBoolean(ValConfig.IT_TYPE, false)
        }
        if (extraArr?.contains("4") == true) {
            //E=4 不显示导航栏,也不显示圆形返回键
            b.putBoolean(ValConfig.IT_TYPE, false)
            b.putBoolean(ValConfig.IT_WEB_BACK_BTN, false)
        }
        Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(mContext, b, callback)
    }

    override fun testFun() {
        LogUtils.d("CmdDispatch  执行")
    }
}