package howbuy.android.global.test;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.howbuy.component.widgets.decoration.SpaceDecoration;
import com.howbuy.fund.base.adapter.group.DefaultHolderFactory;
import com.howbuy.fund.base.adapter.group.TypedAdapter;
import com.howbuy.fund.base.arch.AutoReleaseSingleObserver;
import com.howbuy.fund.base.arch.ClearViewModel;
import com.howbuy.fund.base.arch.ViewModelHelper;
import com.howbuy.fund.base.frag.AbsHbFrag;
import com.howbuy.fund.base.storage.CommonStorageUtils;
import com.howbuy.lib.utils.DensityUtils;
import com.howbuy.lib.utils.LogUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.Callable;

import howbuy.android.global.R;
import io.reactivex.Single;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 清除SP文件中的某个值
 */
@Route(path = "/global/debug/FragDebugClearSpValue")
public class FragDebugClearSpValue extends AbsHbFrag {
    private final TypedAdapter<String> adapter = new TypedAdapter.Builder<String>()
            .setHolderFactory(new DefaultHolderFactory(R.layout.main_item_sp_key_value))
            .setHolderBinder((holder, position, data) -> bindItemView(holder.itemView, data))
            .build();

    /**原始数据，请不要二次修改*/
    private final List<String> rawDataList = new LinkedList<>();
    private EditText editText;
    private TextView tvValueForKey;

    private void bindItemView(@NonNull View itemView, @NonNull String spKey) {
        TextView tvSpKey = itemView.findViewById(R.id.spKey);
        tvSpKey.setText(spKey);
        itemView.setOnClickListener(v -> fillInEditText(spKey));
    }

    private void fillInEditText(String spKey) {
        editText.setText(spKey);
    }

    @Override
    protected int getFragLayoutId() {
        return R.layout.main_frag_debug_clear_sp_value;
    }

    @Override
    protected void stepAllViews(View root, Bundle savedInstanceState) {
        RecyclerView list = root.findViewById(R.id.spKeyValueList);
        list.setLayoutManager(new LinearLayoutManager(getContext()));
        list.addItemDecoration(new SpaceDecoration(0, DensityUtils.dp2px(4f)));
        list.setAdapter(adapter);

        this.editText = root.findViewById(R.id.etSpKey);
        Button btnClear = root.findViewById(R.id.btnClear);
        Button btnSearch = root.findViewById(R.id.btnSearch);
        Button btnGetValue = root.findViewById(R.id.btnGetValue); //查询输入框中的KEY在SP文件中保存的值

        btnClear.setOnClickListener(v -> doClear());
        btnSearch.setOnClickListener(v -> doSearch());
        btnGetValue.setOnClickListener(v -> getValue());

        tvValueForKey = root.findViewById(R.id.tvValueForKey);
        tvValueForKey.setOnLongClickListener(v -> {
            tvValueForKey.setText("");
            return true;
        });
    }

    private void getValue() {
        String input = editText.getText().toString();
        if (TextUtils.isEmpty(input)) {
            LogUtils.pop("KEY不能为空");
            tvValueForKey.setText("");
            return;
        }
        boolean valueExist = CommonStorageUtils.INSTANCE.contains(input);
        LogUtils.d("SP", "key:" + input + ", value exists:" + (valueExist));
        try {
            boolean boolVal = CommonStorageUtils.INSTANCE.getBoolean(input, false);
            LogUtils.d("SP", "key:" + input + ", boolean value:" + (boolVal));
            tvValueForKey.setText(""+boolVal);
        } catch (Exception ignored) {}

        try {
            int intVal = CommonStorageUtils.INSTANCE.getInt(input, 0);
            LogUtils.d("SP", "key:" + input + ", int value:" + (intVal));
            tvValueForKey.setText(""+intVal);
        } catch (Exception ignored) {}


        try {
            float floatVal = CommonStorageUtils.INSTANCE.getFloat(input, 0f);
            LogUtils.d("SP", "key:" + input + ", float value:" + (floatVal));
            tvValueForKey.setText(""+floatVal);
        } catch (Exception ignored) {}

        try {
            float longVal = CommonStorageUtils.INSTANCE.getLong(input, 0L);
            LogUtils.d("SP", "key:" + input + ", longVal value:" + (longVal));
            tvValueForKey.setText(""+longVal);
        } catch (Exception ignored) {}

        try {
            String stringVal = CommonStorageUtils.INSTANCE.getString(input, null);
            LogUtils.d("SP", "key:" + input + ", string value:" + (stringVal));
            tvValueForKey.setText(""+stringVal);
        } catch (Exception ignored) {}

        if (!valueExist) {
            LogUtils.pop("值不存在！");
            tvValueForKey.setText("");
        }
    }

    @SuppressLint("DefaultLocale")
    private void doSearch() {
        String input = editText.getText().toString();
        //重置为显示全部数据
        if (TextUtils.isEmpty(input)) {
            updateShowList(rawDataList);
            LogUtils.pop("列表已重置");
            return;
        }
        input = input.toLowerCase();

        //根据输入筛选数据
        List<String> newData = new ArrayList<>(rawDataList.size());
        for (String key : this.rawDataList) {
            if (key.toLowerCase().contains(input)) {
                newData.add(key);
            }
        }
        LogUtils.pop(String.format("共搜索到%d条数据，请查看列表", newData.size()));
        updateShowList(newData);
    }

    /**
     * 删除输入框中输入的SP的key对应的数据
     */
    private void doClear() {
        String input = editText.getText().toString();
        if (TextUtils.isEmpty(input)) {
            LogUtils.pop("没有指定KEY");
            return;
        }

        boolean hasKey = CommonStorageUtils.INSTANCE.contains(input);
        if (!hasKey) {
            LogUtils.pop("Sp文件中不存在该KEY");
            return;
        }
        CommonStorageUtils.INSTANCE.remove(input);
        LogUtils.pop("清除成功");
    }


    private void updateShowList(@NonNull List<String> keyList) {
        adapter.updateDataList(keyList);
    }

    @Override
    protected void parseArgment(Bundle arg) {
        loadSpKeyList();
    }

    private void loadSpKeyList() {
        Single.fromCallable((Callable<List<String>>) () -> new LinkedList<>(CommonStorageUtils.INSTANCE.sharedPrefer().getAll().keySet()))
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new AutoReleaseSingleObserver<List<String>>(ViewModelHelper.createViewModel(this, ClearViewModel.class)) {
                    @Override
                    public void onSuccess(@NonNull List<String> strings) {
                        if (!strings.isEmpty()) {
                            FragDebugClearSpValue.this.rawDataList.addAll(strings);
                            updateShowList(strings);
                        } else {
                            LogUtils.pop("无数据");
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                        LogUtils.pop("数据解析失败");
                    }
                });
    }
}
