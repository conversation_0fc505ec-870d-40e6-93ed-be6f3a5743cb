package howbuy.android.global.test;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.howbuy.fund.base.frag.AbsFragViewBinding;
import com.shuyu.gsyvideoplayer.GSYVideoManager;
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack;
import com.shuyu.gsyvideoplayer.player.IjkPlayerManager;
import com.shuyu.gsyvideoplayer.player.PlayerFactory;
import com.shuyu.gsyvideoplayer.utils.OrientationUtils;
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer;

import java.util.ArrayList;

import howbuy.android.global.R;
import howbuy.android.global.databinding.FragTestVideHevcLayoutBinding;
import howbuy.android.global.headlines.entity.HwVideoBody;
import howbuy.android.global.headlines.video.shortvideo.SmListShortVideoCoverPlayer;

/**
 * description.
 * ijkPlayer 播放器测试案例
 * tao.liang
 * 2025/3/5
 */
@Deprecated
@Route(path = "/global/main/FragTestVideoHevc")
public class FragTestVideHevc extends AbsFragViewBinding<FragTestVideHevcLayoutBinding> {
    @NonNull
    @Override
    protected FragTestVideHevcLayoutBinding createViewBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container) {
        return FragTestVideHevcLayoutBinding.inflate(inflater, container, false);
    }

    @Override
    protected int getFragLayoutId() {
        return R.layout.frag_test_vide_hevc_layout;
    }

    @Override
    protected void stepAllViews(@Nullable View root, @Nullable Bundle savedInstanceState) {
        super.stepAllViews(root, savedInstanceState);
        PlayerFactory.setPlayManager(IjkPlayerManager.class);
        //设置视频加载超时时间
        GSYVideoManager.instance().setNeedMute(false);
        GSYVideoManager.instance().setTimeOut(8000, true);
    }

    @Override
    protected void parseArgment(Bundle arg) {
        init();

//        AdpHwRecommendVideoList adapter = new AdpHwRecommendVideoList(this, true, null);
        TestAdapter adapter = new TestAdapter();
        getBinding().rcvVideo.setAdapter(adapter);

        HwVideoBody.SmVideoItem item = new HwVideoBody.SmVideoItem(
                "111",
                "SP",
                "",
                "我不知道",
                "",
                "",
                "",
                "https://1251731163.vod2.myqcloud.com/9afd8d5dvodgzp1251731163/4bdf74481397757904327376111/He2QFEct4wIA.mp4",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                ""
        );

        ArrayList list = new ArrayList<HwVideoBody.SmVideoItem>();
        list.add(item);
        adapter.setList(list);
    }

    StandardGSYVideoPlayer videoPlayer;

    OrientationUtils orientationUtils;


    private void init() {

        getBinding().btnSeek.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                videoPlayer.seekTo(60 * 1000);
            }
        });
        videoPlayer = getBinding().videoPlayer;
//
////        String source1 = "http://devimages.apple.com.edgekey.net/streaming/examples/bipbop_4x3/gear3/prog_index.m3u8";
        String source1 = "https://1251731163.vod2.myqcloud.com/9afd8d5dvodgzp1251731163/4bdf74481397757904327376111/He2QFEct4wIA.mp4";
//        videoPlayer.setUp(source1, true, "测试视频");
//        videoPlayer.startPlayLogic();

//        //增加封面
////        ImageView imageView = new ImageView(this);
////        imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
////        imageView.setImageResource(R.mipmap.xxx1);
////        videoPlayer.setThumbImageView(imageView);
//        //增加title
//        videoPlayer.getTitleTextView().setVisibility(View.VISIBLE);
//        //设置返回键
//        videoPlayer.getBackButton().setVisibility(View.VISIBLE);
//        //设置旋转
////        orientationUtils = new OrientationUtils(this, videoPlayer);
//        //设置全屏按键功能,这是使用的是选择屏幕，而不是全屏
////        videoPlayer.getFullscreenButton().setOnClickListener(new View.OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                // ------- ！！！如果不需要旋转屏幕，可以不调用！！！-------
////                // 不需要屏幕旋转，还需要设置 setNeedOrientationUtils(false)
////                orientationUtils.resolveByClick();
////                //finish();
////            }
////        });
//        //是否可以滑动调整
//        videoPlayer.setIsTouchWiget(true);
//        //设置返回按键功能
////        videoPlayer.getBackButton().setOnClickListener(new View.OnClickListener() {
////            @Override
////            public void onClick(View v) {
////                onBackPressed();
////            }
////        });
//
//
//        ///不需要屏幕旋转
//        videoPlayer.setNeedOrientationUtils(false);
//
        videoPlayer.startPlayLogic();
    }


    @Override
    public void onPause() {
        super.onPause();
        videoPlayer.onVideoPause();
    }

    @Override
    public void onResume() {
        super.onResume();
        videoPlayer.onVideoResume();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        GSYVideoManager.releaseAllVideos();
        if (orientationUtils != null)
            orientationUtils.releaseListener();
    }

    //    @Override
//    public void onBackPressed() {
/////       不需要回归竖屏
////        if (orientationUtils.getScreenType() == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
////            videoPlayer.getFullscreenButton().performClick();
////            return;
////        }
//        //释放所有
//        videoPlayer.setVideoAllCallBack(null);
//        super.onBackPressed();
//    }
    class TestAdapter extends BaseQuickAdapter<HwVideoBody.SmVideoItem, BaseViewHolder> {
        TestAdapter() {
            super(R.layout.item_sm_video_main_list_test, null);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder holder, HwVideoBody.SmVideoItem item) {
            SmListShortVideoCoverPlayer player = holder.getView(R.id.video_item_player);
            player.setUp(item.getVideoLink(), true, "");
            player.startPlayLogic();
            player.setVideoAllCallBack(new GSYSampleCallBack(){
                @Override
                public void onPrepared(String url, Object... objects) {
                    super.onPrepared(url, objects);
                    player.showFengMian(false);
                }
            });
        }
    }
}
