package howbuy.android.global.home

import androidx.annotation.Keep
import com.chad.library.adapter.base.entity.MultiItemEntity

/**
 * description.
 * 重定义首页列表adapter渲染数据
 * tao.liang
 * 2024/3/20
 */
@Keep
data class HomeEntity(
    var type: Int, //自定义item类型
    var data: Any?, //item对应的数据
    var title: String?//标题
) : MultiItemEntity {

    constructor(type: Int, data: Any?) : this(type, data, title = null)

    override val itemType: Int
        get() = type

    object HomeItemType {
        const val TYPE_BANNER = 1
        const val TYPE_ICON = 2
        const val TYPE_NEWUSER_AREA = 3
        const val TYPE_CXGQY = 4
        const val TYPE_RECOMMEND = 5
        const val TYPE_NEWS = 6
    }


}