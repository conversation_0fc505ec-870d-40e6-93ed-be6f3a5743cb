package howbuy.android.global.home

import android.content.Context
import android.text.TextUtils
import android.view.View
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.push.PushDispatchHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.utils.ImgHelper
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.base.utils.handleTitle
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.h5.H5UrlKey
import com.howbuy.global.common.banner.BannerImageAdp
import com.howbuy.global.data_api.apiUserInfo
import com.howbuy.global.data_api.isLogined
import com.howbuy.global.user.BannerClickMgr
import com.howbuy.global.user.ad.AdItem
import com.howbuy.imageloader.ImageOptions
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.SpanBuilder
import com.howbuy.lib.utils.SysUtils
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import com.zhpan.bannerview.BannerViewPager
import com.zhpan.bannerview.constants.PageStyle
import com.zhpan.bannerview.observer.HiddenChangeObservable
import com.zhpan.bannerview.transform.PageTransformerFactory
import com.zhpan.bannerview.transform.TransformerStyle
import howbuy.android.global.PATH_FRAG_BEST_PAGE
import howbuy.android.global.R
import howbuy.android.global.entity.HomePageBody
import howbuy.android.global.headlines.adapter.AdpHeadlinesList
import howbuy.android.global.headlines.adapter.HeadlinesItemBinder

/**
 * description.
 * 首页面 列表适配器 数据351820
 * 包括: banner, icon推荐位,产品推荐位
 * tao.liang
 * 2024/3/20
 */
class AdpTabHomeMain(
    val mCx: Context?,
    val frag: Fragment?,
    private val hiddenChangeObservable: HiddenChangeObservable? = null
) : BaseMultiItemQuickAdapter<HomeEntity, BaseViewHolder>() {

    var funcListExpand = false
    var originFuncList = mutableListOf<HomePageBody.HomeIcon>()

    private val mDefaultInconOptions = ImageOptions.Builder()
        .setCacheInMemory(true).setCacheOnDisk(true)
        .build()

    init {
        addItemType(HomeEntity.HomeItemType.TYPE_BANNER, R.layout.item_tb_home_adv)
        addItemType(HomeEntity.HomeItemType.TYPE_ICON, R.layout.item_tb_home_icon_layout)
        addItemType(HomeEntity.HomeItemType.TYPE_NEWUSER_AREA, R.layout.item_tb_home_new_user_module_layout)
        addItemType(HomeEntity.HomeItemType.TYPE_CXGQY, R.layout.item_tb_home_cxgqy_layout)
        addItemType(HomeEntity.HomeItemType.TYPE_RECOMMEND, R.layout.item_tb_home_prod_area_layout)
        addItemType(HomeEntity.HomeItemType.TYPE_NEWS, R.layout.item_tb_home_news_layout)
    }

    override fun convert(holder: BaseViewHolder, item: HomeEntity) {
        when (item.itemType) {
            HomeEntity.HomeItemType.TYPE_BANNER -> {
                setBannerUI(holder, item.data as? MutableList<AdItem>)
            }

            HomeEntity.HomeItemType.TYPE_ICON -> {
                setIconUI(holder, item.data as? MutableList<HomePageBody.HomeIcon>)
            }

            HomeEntity.HomeItemType.TYPE_NEWUSER_AREA -> {
                setNewUserModuleUI(
                    holder,
                    item.data as? MutableList<HomePageBody.NewUserModuleItem>,
                    item.title
                )
            }

            HomeEntity.HomeItemType.TYPE_CXGQY -> {
                setCxgqyUI(holder, item.data as? HomePageBody.CxgModule)
            }

            HomeEntity.HomeItemType.TYPE_RECOMMEND -> {
                setRecommendUI(holder, item.data as? HomePageBody.HomeRecomend)
            }

            HomeEntity.HomeItemType.TYPE_NEWS -> {
                setNewsUI(holder, item.data as? HomePageBody.InformationModule)
            }
        }
    }

    /**
     * 设置banner数据
     */
    private fun setBannerUI(holder: BaseViewHolder, bannerList: MutableList<AdItem>?) {
        if (bannerList.isNullOrEmpty()) {
            //无数据不显示该模块, 隐藏
            holder.setGone(R.id.lay_banner_home_adv, true)
            return
        }
        holder.setGone(R.id.lay_banner_home_adv, false)
        holder.getView<BannerViewPager<AdItem>>(R.id.banner_home_adv).visibility = View.VISIBLE
        holder.getView<BannerViewPager<AdItem>>(R.id.banner_home_adv).apply {
            adapter = object : BannerImageAdp<AdItem>(mCx!!, this, object : DataTransform<AdItem> {
                override fun transform(data: AdItem): BaseBannerItem {
                    return BaseBannerItem(data.adImg, data.onClick)
                }
            }, 20, 20f) {
                override fun analytics(position: Int, data: AdItem) {
                    //埋点 banner1-6 在 610460-610510 之间
                    HbAnalytics.onClick("610" + (46 + position) + "0", "", data.adTitle)
                }

                override fun onItemClick(position: Int, data: AdItem): Boolean {
                    BannerClickMgr.onBannerClick(frag, data)
                    return true
                }

            }.needTitle(true)
            registerOnHiddenChange(hiddenChangeObservable)
            setIndicatorMargin(0, 0, DensityUtils.dp2px(25f), DensityUtils.dp2px(7f))
            setPageStyle(PageStyle.MULTI_PAGE_SCALE, 0.85f)
            addPageTransformer(PageTransformerFactory.createPageTransformer(TransformerStyle.DEPTH_SCALE))
            registerLifecycleObserver((mCx as AppCompatActivity?)?.lifecycle)
            create(bannerList)
        }


    }

    /**
     * 设置储蓄罐签约模块
     */
    private fun setCxgqyUI(holder: BaseViewHolder, cxgqyModule: HomePageBody.CxgModule?) {
        if (TextUtils.isEmpty(cxgqyModule?.img)) {
            holder.setGone(R.id.lay_sm_cxgqy, true)
            return
        }
        holder.setGone(R.id.lay_sm_cxgqy, false)
        ImgHelper.display(
            cxgqyModule?.img,
            holder.getView(R.id.iv_sm_cxgqy),
            ImgHelper.OPTIONS_NORMAL
        )
        holder.itemView.setOnClickListener {
            PushDispatchHelper.pushDispatch(mCx, cxgqyModule?.url)
            HbAnalytics.onClick("611090")
        }
    }

    /**
     * 设置icon位
     */
    private fun setIconUI(holder: BaseViewHolder, iconList: MutableList<HomePageBody.HomeIcon>?) {
        if (iconList.isNullOrEmpty()) {
            holder.setGone(R.id.rc_view_func_default, true)
            return
        }
        holder.setGone(R.id.rc_view_func_default, false)
        holder.getView<RecyclerView>(R.id.rc_view_func_default).apply {
            layoutManager = GridLayoutManager(mCx, 4)
            adapter = FuncListAdapter()
            (holder.getView<RecyclerView>(R.id.rc_view_func_default).adapter as? FuncListAdapter)?.apply {
                setTargetList(iconList)
            }
        }
        //展开/收起按钮(<=4个时,不显示这个按钮)
        holder.setGone(R.id.iv_expand_funcList, iconList.size <= 4)
        setFuncExpandUIStatus(holder.getView(R.id.iv_expand_funcList))
        holder.getView<View>(R.id.iv_expand_funcList).setOnClickListener {
            funcListExpand = !funcListExpand
            setFuncExpandUIStatus(holder.getView(R.id.iv_expand_funcList))
            (holder.getView<RecyclerView>(R.id.rc_view_func_default).adapter as? FuncListAdapter)?.apply {
                setTargetList(iconList)
            }
        }
    }

    inner class FuncListAdapter() :
        BaseQuickAdapter<HomePageBody.HomeIcon, BaseViewHolder>(
            R.layout.sub_item_tb_home_icon_layout
        ) {

        fun setTargetList(originIconList: MutableList<HomePageBody.HomeIcon>) {
            val targetIconList = mutableListOf<HomePageBody.HomeIcon>()
            val list = if (funcListExpand) {
                originIconList.subList(0, 8.coerceAtMost(originIconList.size))
            } else {
                originIconList.subList(0, 4.coerceAtMost(originIconList.size))
            }
            targetIconList.addAll(list)
            setList(targetIconList)
        }


        override fun convert(holder: BaseViewHolder, item: HomePageBody.HomeIcon) {
            holder.setText(R.id.tv_lable, item.iconTitle)
            ImgHelper.display(
                item.iconImgUrl,
                holder.getView(R.id.iv_icon),
                mDefaultInconOptions
            )
            holder.itemView.setOnClickListener {
                PushDispatchHelper.pushDispatch(mCx, item.iconEvent)
                if (item.iconEvent?.contains("SMYX") == true) {
                    HbAnalytics.onClick("610520", "", item.iconTitle)
                } else if (item.iconEvent?.contains("SMTT") == true) {
                    HbAnalytics.onClick("610530", "", item.iconTitle)
                } else {
                    //埋点: 611550-611600(这个区间的值, 不能包含 头条和优选的icon数量)
                    var tempCount = 0
                    run loop@{
                        originFuncList?.forEachIndexed { _, homeIcon ->
                            if (homeIcon.iconEvent?.contains("SMYX") == true
                                || homeIcon.iconEvent?.contains("SMTT") == true
                            ) {
                                tempCount++
                            }
                            if (homeIcon.iconEvent == item.iconEvent) {
                                return@loop
                            }
                        }
                    }
                    HbAnalytics.onClick(
                        "611" + (55 + (holder.layoutPosition - tempCount)) + "0",
                        "", item.iconTitle
                    )
                }
            }
        }
    }


    private fun setFuncExpandUIStatus(imageView: ImageView) {
        imageView.setImageResource(
            if (funcListExpand) {
                R.mipmap.ic_home_func_collapse
            } else {
                R.mipmap.ic_home_func_expand
            }
        )
    }

    /**
     * 设置 新手专区模块数据
     */
    private fun setNewUserModuleUI(
        holder: BaseViewHolder,
        newUserList: MutableList<HomePageBody.NewUserModuleItem>?,
        title: String?
    ) {
        if (newUserList.isNullOrEmpty()) {
            holder.setGone(R.id.rc_view_newuser_area, true)
            return
        }
        if (title == null || TextUtils.isEmpty(title)) {
            holder.setGone(R.id.tv_left_newuser_title, true)
        } else {
            val text = title.take(4)
            val verticalText = StringBuilder()
            for (c in text.toCharArray()) {
                verticalText.append(c).append("\n")
            }
            if (verticalText.isNotEmpty()) {
                verticalText.setLength(verticalText.length - 1)
            }
            holder.setGone(R.id.tv_left_newuser_title, false)
            holder.setText(R.id.tv_left_newuser_title, verticalText)
        }
        holder.setGone(R.id.rc_view_newuser_area, false)
        holder.getView<RecyclerView>(R.id.rc_view_newuser_area).apply {
            layoutManager = LinearLayoutManager(mCx, LinearLayoutManager.HORIZONTAL, false)
            if (itemDecorationCount == 0) {
                addItemDecoration(XLinearBuilder(mCx).setSpacing(5f).build())
            }
            //模块左右内边距(10+10), 图片title宽度+图片标题右边距(15+5), 卡片中间的间距(5*2)
            val titleWidth = if (TextUtils.isEmpty(title)) 0 else 20f.dp2px()
            val viewWidth = SysUtils.getWidth(mCx) - titleWidth - DensityUtils.dp2px(20f) - DensityUtils.dp2px(10f)
            adapter = object : BaseQuickAdapter<HomePageBody.NewUserModuleItem, BaseViewHolder>(
                R.layout.sub_item_tb_home_newuser_img_layout, newUserList
            ) {

                override fun convert(holder: BaseViewHolder, item: HomePageBody.NewUserModuleItem) {
                    holder.getView<ImageView>(R.id.iv_newuser_guide).layoutParams?.also {
                        it.width = viewWidth / 3
                    }
                    ImgHelper.display(
                        item.iconImgUrl,
                        holder.getView(R.id.iv_newuser_guide),
                        mDefaultInconOptions
                    )
                    holder.itemView.setOnClickListener {
                        PushDispatchHelper.pushDispatch(mCx, item.iconEvent)
                        HbAnalytics.onClick("611610", "", item.iconTitle)
                    }
                }
            }
        }
    }

    /**
     * 设置推荐模块数据
     */
    private fun setRecommendUI(holder: BaseViewHolder, recommendData: HomePageBody.HomeRecomend?) {
        holder.setGone(R.id.lay_view_prod, false)
        var hasCxgSign = false
        data.forEachIndexed { index, homeEntity ->
            if (homeEntity.type == HomeEntity.HomeItemType.TYPE_CXGQY) {
                hasCxgSign = true
            }
        }
        if (hasCxgSign) {
            holder.setBackgroundResource(R.id.lay_view_prod, R.drawable.bg_corner10_bottom_white)
            (holder.getView<View>(R.id.lay_view_prod).layoutParams as? RecyclerView.LayoutParams)?.also {
                it.topMargin = 0
            }
        } else {
            holder.setBackgroundResource(R.id.lay_view_prod, R.drawable.bg_corner10_white)
            (holder.getView<View>(R.id.lay_view_prod).layoutParams as? RecyclerView.LayoutParams)?.also {
                it.topMargin = DensityUtils.dp2px(10f)
            }
        }
        //设置模块标题, 要根据 ##aaa## 匹配处理字体颜色, 否则直接展示内容
        val regex = "##(.*?)##".toRegex()
        val targetStr = regex.find(recommendData?.bigTitle ?: "")?.groupValues?.getOrNull(1) ?: ""
        if (!TextUtils.isEmpty(targetStr)) {
            val source = recommendData?.bigTitle?.replace("#", "")
            val targetIndex = source?.indexOf(targetStr) ?: -1
            if (targetIndex != -1) {
                SpanBuilder(source)
                    .color(
                        targetIndex,
                        targetIndex + targetStr.length,
                        ColorUtils.parseColor("#C51D25"),
                        false
                    )
                    .apply(holder.getView(R.id.tv_recom_title))
            }
        } else {
            holder.setText(R.id.tv_recom_title, recommendData?.bigTitle)
        }
        //设置列表数据内容和空样式
        if (recommendData?.moduleList.isNullOrEmpty()) {
            holder.setGone(R.id.lay_prod_empty, false)
            holder.setGone(R.id.rc_view_prod, true)
        } else {
            holder.setGone(R.id.lay_prod_empty, true)
            holder.setGone(R.id.rc_view_prod, false)
            holder.getView<RecyclerView>(R.id.rc_view_prod).apply {
                layoutManager = LinearLayoutManager(mCx)
                if (itemDecorationCount == 0) {
                    addItemDecoration(
                        XLinearBuilder(context).setSpacing(12f)
                            .setColor(ColorUtils.parseColor("#ffffff"))
                            .build()
                    )
                }
                adapter = AdpProdList(recommendData?.moduleList)
            }
        }
        holder.getView<View>(R.id.tv_des).setOnClickListener {
            //如果操作了激活流程, 如何保证 进入优选时, 用户信息是最新的状态?
            RouterHelper.launchFrag(context, PATH_FRAG_BEST_PAGE, NavHelper.obtainArg(""))
            HbAnalytics.onClick("610710")
        }

        //前提：仅针对“已登录已开户的非专业投资者用户“展示，其他类型客户，认证提示隐藏
        if (isLogined() && apiUserInfo().finishOpenAccount() && !TextUtils.equals(
                "PRO",
                apiUserInfo().investorType()
            )
        ) {
            holder.setGone(R.id.lay_auth_guide, false)
            holder.getView<View>(R.id.tv_auth_guide).setOnClickListener {
                val targetH5PageUrlKey = if (apiUserInfo().investorAuditStatus()) {
                    H5UrlKey.PRO_INVESTORS_CHECK
                } else {
                    H5UrlKey.PRO_INVESTORS_IDX
                }
                val bundle = NavHelper.obtainArg("", ValConfig.IT_VALUE_6, true)
                Invoker.getInstance().navigation(IWebProvider::class.java).launchWebView(
                    context, "", targetH5PageUrlKey, null, bundle
                ) { _, resultData, _ ->
                    //H5返回结果 请求接口成功后,关闭H5页面,刷新UI
                    notifyDataSetChanged()
                }
            }
        } else {
            holder.setGone(R.id.lay_auth_guide, true)
        }
    }

    private fun setNewsUI(holder: BaseViewHolder, newsData: HomePageBody.InformationModule?) {
        //设置模块标题, 要根据 ##aaa## 匹配处理字体颜色, 否则直接展示内容
        if (!TextUtils.isEmpty(newsData?.title)) {
            holder.setText(
                R.id.tv_news_title,
                newsData?.title?.handleTitle(ColorUtils.parseColor("#C51D25"))
            )
        } else {
            holder.setText(R.id.tv_news_title, newsData?.title)
        }
        holder.getView<RecyclerView>(R.id.rc_view_news).apply {
            if (itemDecorationCount == 0) {
                this.addItemDecoration(
                    XLinearBuilder(mCx).setSpacing(10f).build()
                )
            }
            val adpNews = AdpHeadlinesList(HeadlinesItemBinder.UI_TYPE_HOME) { item ->
                HbAnalytics.onClick("610980", item.contentId, item.title)
            }
            this.adapter = adpNews
            adpNews.setList(newsData?.newsList)
        }
        //无值,不显示 更多按钮
        holder.setGone(R.id.tv_news_des, TextUtils.isEmpty(newsData?.buttonJumpUrl))
        holder.getView<View>(R.id.tv_news_des).setOnClickListener {
            //跳转到 海外资讯页面
            PushDispatchHelper.pushDispatch(it.context, newsData?.buttonJumpUrl)
            HbAnalytics.onClick("610970")
        }
    }
}