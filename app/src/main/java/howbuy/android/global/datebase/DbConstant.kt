package howbuy.android.global.datebase

/**
 *@desc
 *<AUTHOR>
 *@date 2024/03/21
 **/
class DbConstant {
    companion object {
        const val CREATE_TABLE_COMMON: String =
            "CREATE TABLE [tb_common] ( [_id] [INTEGER IDENTITY](1,1), [key] TEXT NOT NULL, [subkey] TEXT NOT NULL, [value] BLOB, [state] INTEGER, [date] LONG, CONSTRAINT [] PRIMARY KEY ([key], [subkey]));"
        const val CREATE_TABLE_FAVORITE =
            "CREATE TABLE [tb_favorite] ( [code] TEXT NOT NULL, [classtype] TEXT, [favor] INTEGER DEFAULT (-1), [data] BLOB, [favTime] TEXT, [favPosition] INTEGER, [jjmc] TEXT, [jjfl] TEXT, [jzrq] TEXT, [value1] TEXT, [value2] TEXT, [value3] TEXT, [hbjn] TEXT, [hb1n] TEXT, [hb3n] TEXT, [hbcl] TEXT, [combFund] TEXT, [tzcl] TEXT, CONSTRAINT [sqlite_autoindex_tb_favorite_1] PRIMARY KEY ([code]));"
        const val CREATE_TABLE_FUNDS_INFO_OPT =
            "CREATE TABLE FundsInfoOpt( [code] text PRIMARY KEY ,[xuantime] text ,[postion] integer );"

    }
}