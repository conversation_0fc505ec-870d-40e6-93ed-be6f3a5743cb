package howbuy.android.global.message

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.span.SpannableItem
import com.howbuy.fund.base.utils.span.SpannableUtils
import com.howbuy.lib.utils.ColorUtils
import com.howbuy.lib.utils.MathUtils
import howbuy.android.global.R
import howbuy.android.global.archive.chart.tab.SmChartTabInfo

/**
 * 消息中心tab
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
class AdpMessageTab(list: MutableList<Int>) : BaseQuickAdapter<Int, BaseViewHolder>(R.layout.item_message_center_tab, list) {

    private var selectedPos = 0
    fun onSelect(position: Int) {
        selectedPos = position
        notifyDataSetChanged()
    }

    override fun convert(holder: BaseViewHolder, item: Int) {
        val title: String
        val showNum = if (item > 0) {
            "(${item})"
        } else {
            ""
        }
        if (holder.bindingAdapterPosition == 0) {
            title = "消息"
        } else {
            title = "待办"
        }
        holder.setText(
            R.id.tv, SpannableUtils.formatStr(
                SpannableItem(title),
                SpannableItem(showNum, ColorUtils.parseColor("#e94444"))
            )
        )
        holder.setVisible(R.id.indicator, selectedPos == holder.bindingAdapterPosition)
    }
}