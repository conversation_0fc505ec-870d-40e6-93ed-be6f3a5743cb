package howbuy.android.global.message

import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.global.user.UserRequest
import howbuy.android.global.PATH_FRAG_MESSAGE_LIST_BY_CATEGORY

/**
 * 按分类的消息列表
 * 继承抽象基类，专门处理特定分类的消息
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
@Route(path = PATH_FRAG_MESSAGE_LIST_BY_CATEGORY)
class FragMessageListByCategory : AbsFragMessageList() {

    /**
     * 实现具体的API调用逻辑
     * 这里调用消息列表API，传入分类ID
     */
    override fun requestData(needCount: <PERSON><PERSON>an, lastMsgId: String?) {
        UserRequest.queryMessageList(
            mPageType.toString(),
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            messageCategoryId, // 传入具体的分类ID
            0
        ) {
            renderData(it)
        }
    }

    /**
     * 重写埋点ID，为分类消息列表提供不同的埋点
     */
    override fun getAnalyticsClickId(): String {
        return "611331" // 分类消息点击埋点
    }

    /**
     * 重写空视图文本
     */
    override fun getEmptyText(): String {
        return "该分类暂无消息"
    }
}
