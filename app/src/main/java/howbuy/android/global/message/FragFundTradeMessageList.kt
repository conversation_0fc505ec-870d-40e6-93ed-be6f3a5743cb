package howbuy.android.global.message

import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.global.user.UserRequest
import howbuy.android.global.PATH_FRAG_FUND_TRADE_MESSAGE_LIST

/**
 * 基金交易提醒通知列表
 * 继承抽象基类，专门处理基金交易相关的消息
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
@Route(path = PATH_FRAG_FUND_TRADE_MESSAGE_LIST)
class FragFundTradeMessageList : AbsFragMessageList() {

    companion object {
        private const val FUND_TRADE_CATEGORY_ID = "2" // 基金交易分类ID
    }

    override fun parseArgment(arg: android.os.Bundle?) {
        super.parseArgment(arg)
        // 强制设置为基金交易分类
        messageCategoryId = FUND_TRADE_CATEGORY_ID
        mPageType = TYPE_MESSAGE // 基金交易消息都是普通消息类型
    }

    /**
     * 实现具体的API调用逻辑
     * 专门获取基金交易相关的消息
     */
    override fun requestData(needCount: <PERSON><PERSON><PERSON>, lastMsgId: String?) {
        UserRequest.queryMessageList(
            TYPE_MESSAGE.toString(), // 固定为消息类型
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            FUND_TRADE_CATEGORY_ID, // 固定为基金交易分类
            0
        ) {
            renderData(it)
        }
    }

    /**
     * 重写适配器创建，可以为基金交易消息提供特殊的适配器
     */
    override fun createAdapter(): AdpMessageList {
        return AdpMessageList(TYPE_MESSAGE) // 固定为消息类型
    }

    /**
     * 重写埋点ID
     */
    override fun getAnalyticsClickId(): String {
        return "611332" // 基金交易消息点击埋点
    }

    /**
     * 重写空视图文本
     */
    override fun getEmptyText(): String {
        return "暂无基金交易提醒"
    }

    /**
     * 重写空视图图片
     */
    override fun getEmptyImageResource(): Int {
        return R.mipmap.message_empty_img // 可以使用专门的基金交易空状态图片
    }
}
