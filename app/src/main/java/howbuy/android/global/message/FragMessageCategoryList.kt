package howbuy.android.global.message

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.TextView
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.nav.NavHelper
import com.howbuy.fund.base.router.RouterHelper
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.base.widget.xrecyclerdivider.builder.XLinearBuilder
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageCategory
import com.howbuy.global.user.entity.MessageCategoryList
import com.howbuy.hbrefresh.layout.api.RefreshLayout
import com.howbuy.hbrefresh.layout.listener.OnRefreshListener
import com.howbuy.lib.utils.MathUtils
import howbuy.android.global.PATH_FRAG_MESSAGE_LIST
import howbuy.android.global.R
import howbuy.android.global.databinding.FragMessageListBinding
import howbuy.android.global.message.FragMessageList.Companion.TYPE_MESSAGE

/**
 * 消息分类列表
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
class FragMessageCategoryList : AbsFragViewBinding<FragMessageListBinding>() {

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragMessageListBinding {
        return FragMessageListBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_message_list

    private lateinit var mAdapter: AdpMessageCategory

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        binding.refreshLayout.setOnRefreshListener { loadData() }
        binding.refreshLayout.setEnableLoadmore(false)
        mAdapter = AdpMessageCategory()
        mAdapter.setOnItemClickListener { adapter, view, position ->
            val category = mAdapter.getItem(position)
            onCategoryClick(category)
        }
        mAdapter.setEmptyView(emptyView())
        mAdapter.isUseEmpty = false
        binding.rv.addItemDecoration(
            XLinearBuilder(context).setSpacing(15f).setShowFirstTopLine(true).setShowLastLine(true).build()
        )
        binding.rv.adapter = mAdapter

    }

    override fun parseArgment(arg: Bundle?) {
        loadData()
    }

    private fun loadData() {
        showAlermDlg("加载中", false, false)
        UserRequest.queryMessageCategoryList(0) {
            renderData(it)
        }
    }

    private fun renderData(result: ReqResult<ReqNetOpt>) {
        if (activity == null || requireActivity().isFinishing) {
            return
        }
        binding.refreshLayout.finishRefresh()
        if (result.isSuccess && result.mData != null) {
            val body = result.mData as MessageCategoryList
            val categoryList = body.categoryList ?: mutableListOf()

            mAdapter.isUseEmpty = true
            mAdapter.setList(categoryList)

            // 更新父页面的未读数量
            (parentFragment as? FragMessageCenter)?.updateUnReadCount(MathUtils.forValI(body.unReadCount, 0))
        } else {
            mAdapter.isUseEmpty = true
            mAdapter.setList(mutableListOf())
        }
        showAlermDlg(null, 0)
    }

    /**
     * 分类点击事件
     */
    private fun onCategoryClick(category: MessageCategory) {
//        HbAnalytics.onClick("611331")
        RouterHelper.launchFrag(
            this, PATH_FRAG_MESSAGE_LIST,
            NavHelper.obtainArg(
                category.categoryName,
                ValConfig.IT_TYPE, TYPE_MESSAGE,
                ValConfig.IT_ID, category.categoryId
            )
        )
    }

    /**
     * 获取当前分类列表数据，用于外部调用
     */
    fun getCategoryList(): List<MessageCategory> {
        return mAdapter.data
    }

    /**
     * 刷新数据
     */
    fun refreshData() {
        loadData()
    }

    private fun emptyView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.sm_empty_page, binding.rv, false)
        val image = view.findViewById<ImageView>(R.id.iv_no_data)
        view.findViewById<TextView>(R.id.tv_tips).text = "暂无消息"
        val lp = image.layoutParams as MarginLayoutParams
        image.setImageResource(R.mipmap.message_empty_img)
        lp.topMargin = 60.dp2px()
        return view
    }
}
