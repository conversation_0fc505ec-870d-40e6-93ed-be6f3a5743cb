package howbuy.android.global.message.entity

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import kotlinx.android.parcel.Parcelize

/**
 * 交易消息实体类
 * 包含具体的交易信息字段
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
@Parcelize
data class TradeMessageItem(
    val messageId: String?,//消息id
    val messageTitle: String?,// 消息标题
    val messageDate: String?,// 消息时间 yyyyMMdd
    var readStatus: String?,// 是否已读 0否1是
    val url: String?,//跳转h5链接
    val content: Map<String, String>?,//显示内容
) : Parcelable

/**
 * 交易消息列表响应数据
 */
@Parcelize
data class TradeMessageResponse(
    val messageList: MutableList<TradeMessageItem>?//交易消息列表
) : AbsNormalBody(), Parcelable
