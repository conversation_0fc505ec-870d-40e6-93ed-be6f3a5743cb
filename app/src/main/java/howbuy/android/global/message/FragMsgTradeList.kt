package howbuy.android.global.message

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.router.provider.IWebProvider
import com.howbuy.router.proxy.Invoker
import howbuy.android.global.PATH_FRAG_MSG_TRADE_LIST
import howbuy.android.global.message.adapter.AdpMsgTradeList
import howbuy.android.global.message.entity.TradeMessageItem
import howbuy.android.global.message.entity.TradeMessageResponse

/**
 * 交易提醒消息列表
 * <AUTHOR>
 * @Date 2025/5/29
 * @Version V3.0
 */
@Route(path = PATH_FRAG_MSG_TRADE_LIST)
class FragMsgTradeList : AbsFragMessageList<TradeMessageItem>() {

    override fun createAdapter(): PinnedBaseQuickAdapter<PinnedHeaderEntity<TradeMessageItem>, BaseViewHolder> {
        return AdpMsgTradeList()
    }

    override fun parseArgment(arg: Bundle?) {
        super.parseArgment(arg)
        //TODO wy 交易提醒全部已读
    }

    override fun parseMessageList(responseData: Any): MutableList<TradeMessageItem> {
        return if (responseData is TradeMessageResponse) {
            responseData.messageList ?: mutableListOf()
        } else {
            mutableListOf()
        }
    }

    override fun getMessageDate(item: TradeMessageItem): String? {
        return item.messageDate
    }

    //进入列表就全部已读，不需要单条已读
    override fun handleMessageClick(data: TradeMessageItem, position: Int) {
        //TODO wy HbAnalytics.onClick("611330")
        context?.let {
            Invoker.getInstance().navigation(IWebProvider::class.java)
                .launchWebView(context, "", data.jump ?: "", null,null)
        }
    }

    override fun getMessageId(item: TradeMessageItem): String? = item.messageId

    /**
     * TODO wy 实现具体的API调用逻辑
     * 这里调用通用的消息列表API，不传分类ID
     */
    override fun requestData(needCount: Boolean, lastMsgId: String?) {
        UserRequest.queryMessageList(
            "0",
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            0
        ) {
            renderData(it)
        }
    }
}