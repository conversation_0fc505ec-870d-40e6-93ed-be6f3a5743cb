package howbuy.android.global.message

import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.global.user.UserRequest
import howbuy.android.global.PATH_FRAG_MESSAGE_LIST

/**
 * 消息列表 - 待办消息和通用消息列表
 * 继承抽象基类，只需实现具体的API调用逻辑
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
@Route(path = PATH_FRAG_MESSAGE_LIST)
class FragMessageList : AbsFragMessageList() {

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        binding.refreshLayout.isEnableOverScrollBounce = false
        binding.refreshLayout.isEnableLoadmore = true
        binding.refreshLayout.isEnableRefresh = true
        binding.refreshLayout.setEnableLoadmoreWhenContentNotFull(false)
        binding.refreshLayout.setOnRefreshLoadmoreListener(object : OnRefreshLoadmoreListener {
            override fun onRefresh(refreshlayout: RefreshLayout?) {
                loadData(ValConfig.LOAD_LIST_REFUSH)
            }

            override fun onLoadmore(refreshlayout: RefreshLayout?) {
                loadData(ValConfig.LOAD_LIST_PAGE)
            }
        })
        binding.rv.addItemDecoration(PinnedHeaderItemDecoration.Builder(PinnedBaseQuickAdapter.TYPE_PINNED_HEADER).create())
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_MESSAGE_TODO_DONE, MessageItem::class.java).observeBus(this) { message ->
            //待办消息完成，待办移除
            if (mPageType == TYPE_MESSAGE) {
                toDoDoneForMessage(message)
            } else {
                todoDone(message)
            }
        }
    }

    override fun parseArgment(arg: Bundle?) {
        mPageType = arg?.getInt(ValConfig.IT_TYPE, TYPE_MESSAGE) ?: TYPE_MESSAGE
        messageCategoryId = arg?.getString(ValConfig.IT_ID)
        mAdapter = AdpMessageList(mPageType)
        mAdapter.setOnItemClickListener { adapter, view, position ->
            val data = mAdapter.getItem(position).data
            if (data != null) {
                onMessageClick(data, position)
            }
        }
        mAdapter.setEmptyView(emptyView())
        binding.rv.adapter = mAdapter
        mAdapter.isUseEmpty = false
        loadData(ValConfig.LOAD_LIST_FIRST)
    }

    private fun loadData(loadType: Int) {
        mLoadType = loadType
        val needCount: Boolean
        val lastMsgId: String?
        if (loadType == ValConfig.LOAD_LIST_FIRST) {
            showAlermDlg("加载中", false, false)
        }
        if (loadType == ValConfig.LOAD_LIST_PAGE) {
            //下一页
            needCount = false
            lastMsgId = mAdapter.data.lastOrNull()?.data?.messageId
        } else {
            //第一页
            binding.rv.scrollToPosition(0)
            needCount = true
            lastMsgId = ""
        }
        UserRequest.queryMessageList(mPageType.toString(), needCount, lastMsgId, PAGE_SIZE.toString(), null, 0) {
            renderData(it)
        }
    }

    private fun renderData(result: ReqResult<ReqNetOpt>) {
        if (activity == null || requireActivity().isFinishing) {
            return
        }
        binding.refreshLayout.finishRefresh()
        binding.refreshLayout.finishLoadmore()
        if (result.isSuccess && result.mData != null) {
            val body = result.mData as MessageList
            val list = body.messageList ?: mutableListOf()
            if (mLoadType == ValConfig.LOAD_LIST_PAGE) {
                mAdapter.addData(formatData(list))
            } else {
                mAdapter.isUseEmpty = true
                mAdapter.setList(formatData(list))
                if (mPageType == TYPE_MESSAGE) {
                    (parentFragment as? FragMessageCenter)?.updateUnReadCount(MathUtils.forValI(body.unReadCount, 0))
                } else {
                    (parentFragment as? FragMessageCenter)?.updateTodoCount(MathUtils.forValI(body.todoCount, 0))
                }
                apiUserMessage().updateCount(MathUtils.forValI(body.unReadCount, 0), MathUtils.forValI(body.todoCount, 0))
            }
            binding.refreshLayout.isEnableLoadmore = list.size >= PAGE_SIZE
        } else {
            mAdapter.isUseEmpty = true
            if (mLoadType == ValConfig.LOAD_LIST_PAGE) {
                mAdapter.addData(mutableListOf())
            } else {
                mAdapter.setList(mutableListOf())
            }
        }
        showAlermDlg(null, 0)
    }

    /**
     * 格式化数据，用于日期按月分组添加header
     * yyyy年M月
     */
    private var lastTime = ""
    private fun formatData(list: MutableList<MessageItem>): MutableList<PinnedHeaderEntity<MessageItem>> {
        if (mLoadType != ValConfig.LOAD_LIST_PAGE) lastTime = ""
        val data = mutableListOf<PinnedHeaderEntity<MessageItem>>()
        list.forEach {
            val currentTime = DateUtils.timeFormat(it.messageDate, DateUtils.DATEF_YMD, "yyyy年M月")
            if (!TextUtils.isEmpty(currentTime) && !TextUtils.equals(lastTime, currentTime)) {
                data.add(PinnedHeaderEntity(null, PinnedBaseQuickAdapter.TYPE_PINNED_HEADER, currentTime))
                lastTime = currentTime!!
            }
            data.add(PinnedHeaderEntity(it, PinnedBaseQuickAdapter.TYPE_DATA, ""))
        }
        return data
    }

    private fun emptyView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.sm_empty_page, binding.rv, false)
        val image = view.findViewById<ImageView>(R.id.iv_no_data)
        view.findViewById<TextView>(R.id.tv_tips).text = "暂无消息"
        val lp = image.layoutParams as MarginLayoutParams
        image.setImageResource(R.mipmap.message_empty_img)
        lp.topMargin = 60.dp2px()
        return view
    }

    /**
     * 消息点击
     */
    private fun onMessageClick(data: MessageItem, position: Int) {
        HbAnalytics.onClick(if (mPageType == TYPE_MESSAGE) "611330" else "611340")
        context?.let {
            MessageLauncher.launcherMsg(it, data, mPageType == TYPE_MESSAGE, false)
        }
        if (mPageType == TYPE_MESSAGE && !TextUtils.equals("1", data.readStatus)) {
            data.readStatus = "1"
            mAdapter.notifyItemChanged(position)
            (parentFragment as? FragMessageCenter)?.reduceUnReadCount()
        }
    }

    /**
     * 未读消息全部已读
     */
    fun readAll() {
        mAdapter.data.forEach {
            if (it.data != null && !TextUtils.equals("1", it.data.readStatus)) {
                it.data.readStatus = "1"
            }
        }
        mAdapter.notifyDataSetChanged()
    }

    /**
     * 待办完成同步将消息列表中待办状态变更
     */
    private fun toDoDoneForMessage(message: MessageItem) {
        run loop@{
            mAdapter.data.forEachIndexed { index, item ->
                if (item.data != null && TextUtils.equals(message.messageId, item.data.messageId)) {
                    item.data.todoStatus = "1"
                    mAdapter.notifyItemChanged(index)
                    return@loop
                }
            }
        }
    }

    /**
     * 待办已完成
     */
    private fun todoDone(message: MessageItem) {
        //待办完成就一定减一，更新本地待办数量
        val count = apiUserMessage().todoCount()
        apiUserMessage().updateTodoCount(max(count - 1, 0))
        (parentFragment as? FragMessageCenter)?.reduceTodoCount()
        //删除数据
        var delIndex: Int = -1
        mAdapter.data.also { list ->
            list.forEachIndexed { index, item ->
                if (item.data != null && TextUtils.equals(item.data.messageId, message.messageId)) {
                    delIndex = index
                    return@also
                }
            }
        }
        //判断是否需要删除月份头
        var preItem: PinnedHeaderEntity<MessageItem>? = null
        var nextItem: PinnedHeaderEntity<MessageItem>? = null
        if (delIndex >= 1) {
            preItem = mAdapter.data[delIndex - 1]
        }
        if (delIndex < mAdapter.data.size - 1) {
            nextItem = mAdapter.data[delIndex + 1]
        }
        GlobalApp.getApp().runOnUiThread({
            if ((preItem != null && preItem.itemType == PinnedBaseQuickAdapter.TYPE_PINNED_HEADER) &&
                ((nextItem != null && nextItem.itemType == PinnedBaseQuickAdapter.TYPE_PINNED_HEADER)
                        || delIndex == mAdapter.itemCount - 1)
            ) {
                mAdapter.removeAt(delIndex)
                mAdapter.removeAt(delIndex - 1)
            } else {
                mAdapter.removeAt(delIndex)
            }
        }, 0)
    }
}