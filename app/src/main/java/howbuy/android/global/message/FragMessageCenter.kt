package howbuy.android.global.message

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.android.arouter.facade.annotation.Route
import com.howbuy.analytics.PvReportUtils
import com.howbuy.analytics.entity.HBAnalyticsBean
import com.howbuy.arouter_intercept_api.IGlobalInterceptCode
import com.howbuy.arouter_intercept_api.Intercept
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.aty.AtyEmpty
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.router.AtyBridgeHelper
import com.howbuy.fund.base.utils.touchSlopField
import com.howbuy.global.user.UserRequest
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.LogUtils
import com.howbuy.lib.utils.NoDbClickListener
import com.howbuy.lib.utils.StatusBarUtil
import howbuy.android.global.PATH_FRAG_MESSAGE_CENTER
import howbuy.android.global.R
import howbuy.android.global.databinding.FragMessageCenterBinding
import kotlin.math.max

/**
 * 消息中心
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
@Intercept(interceptArray = [IGlobalInterceptCode.GLOBAL_CHECK_LOGIN])
@Route(path = PATH_FRAG_MESSAGE_CENTER)
class FragMessageCenter : AbsFragViewBinding<FragMessageCenterBinding>() {
    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragMessageCenterBinding {
        return FragMessageCenterBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_message_center

    private lateinit var ivClean: View
    private val mTabAdapter by lazy {
        AdpMessageTab(mutableListOf(0, 0))
    }

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        val toolBarColor = ContextCompat.getColor(GlobalApp.getApp(), R.color.white)
        StatusBarUtil.setStatusBarLightMode(activity ?: return, toolBarColor, true)
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).setToolbarTitle("消息中心")
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).setToolbarLineVisibility(false)
        AtyBridgeHelper.getAtyEmptyApi(activity ?: return).actionBarToolBar?.setBackgroundColor(toolBarColor)
        (activity as AtyEmpty?)?.toolbarLayoutTitle?.let { viewGroup ->
            val params = viewGroup.layoutParams as Toolbar.LayoutParams
            params.gravity = Gravity.END
            val menuView = View.inflate(activity, R.layout.menu_message_center, null)
            ivClean = menuView.findViewById<ImageView>(R.id.iv_clean)
            menuView.findViewById<ImageView>(R.id.iv_setting).setOnClickListener(object : NoDbClickListener() {
                override fun onNoDoubleClick(v: View) {
//                    HbAnalytics.onClick("611350")
                    LogUtils.pop("跳转设置页面")
                }
            })
            ivClean.setOnClickListener(object : NoDbClickListener() {
                override fun onNoDoubleClick(v: View) {
                    HbAnalytics.onClick("611350")
                    readAllUnRead()
                }
            })
            viewGroup.addView(menuView, params)
        }
        binding.tab.adapter = mTabAdapter
        mTabAdapter.setOnItemClickListener { _, _, position ->
            binding.vp.setCurrentItem(position, false)
        }
        binding.vp.touchSlopField(4)
        binding.vp.offscreenPageLimit = 2
        binding.vp.adapter = object : FragmentStateAdapter(this) {
            override fun getItemCount() = 2

            override fun createFragment(position: Int): Fragment {
                return if (position == 0) {
                    // 消息Tab：显示消息分类列表
                    FragMessageCategoryList()
                } else {
                    // 待办Tab：显示原来的待办列表
                    FragTodoList()
                }
            }
        }
        binding.vp.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                onTabClick(position)
            }
        })
    }

    override fun parseArgment(arg: Bundle?) {
        val defaultIndex = arg?.getInt(ValConfig.IT_VALUE_1, 0) ?: 0
        binding.vp.currentItem = defaultIndex
//        UserDataHelper.getDataManager(DataIds.ID_USER_MESSAGE).addObserver(this, false, object :
//            UserDataObserver<MessageData> {
//            override fun onChanged(t: DataWrapper<MessageData>) {
//                if (activity == null || activity?.isFinishing == true) {
//                    return
//                }
//                if (t.data != null) {
//                    mTabAdapter.setList(
//                        mutableListOf(
//                            MathUtils.forValI(t.data?.unReadCount, 0),
//                            MathUtils.forValI(t.data?.todoCount, 0)
//                        )
//                    )
//                }
//            }
//        })
    }

    override fun onResumeSecond() {
        super.onResumeSecond()
        analyticsChildPage(binding.vp.currentItem)
    }

    fun updateUnReadCount(unReadCount: Int) {
        mTabAdapter.data[0] = unReadCount
        mTabAdapter.notifyItemChanged(0)
    }

    fun reduceUnReadCount() {
        val count = mTabAdapter.data[0]
        mTabAdapter.data[0] = max(count - 1, 0)
        mTabAdapter.notifyItemChanged(0)
    }

    fun reduceTodoCount() {
        val count = mTabAdapter.data[1]
        mTabAdapter.data[1] = max(count - 1, 0)
        mTabAdapter.notifyItemChanged(1)
    }

    fun updateTodoCount(todoCount: Int) {
        mTabAdapter.data[1] = todoCount
        mTabAdapter.notifyItemChanged(1)
    }

    private fun readAllUnRead() {
        if ((mTabAdapter.data.getOrNull(0) ?: 0) > 0) {
            UserRequest.readMessage(true)
            updateUnReadCount(0)
            // 刷新消息分类列表数据
            (childFragmentManager.findFragmentByTag("f0") as? FragMessageCategoryList)?.readAll()
        }
    }

    private fun onTabClick(position: Int) {
        analyticsChildPage(position)
        mTabAdapter.onSelect(position)
        if (position == 0) {
            ivClean.visibility = View.VISIBLE
        } else {
            ivClean.visibility = View.INVISIBLE
        }
    }

    /**
     * 子页面page埋点
     */
    private fun analyticsChildPage(position: Int) {
        LogUtils.e("analyticsChildPage", "消息页面埋点：$position")
        if (position == 0) {
            //TODO wy pv
//            val bean = HBAnalyticsBean("FragMessageList", "消息中心-消息列表页", "352660", "3")
//            PvReportUtils.reportPvManually(bean, context, null, null, null)
        } else if (position == 1) {
            val bean = HBAnalyticsBean("FragMessageList", "消息中心-待办列表页", "352670", "3")
            PvReportUtils.reportPvManually(bean, context, null, null, null)
        }
    }
}