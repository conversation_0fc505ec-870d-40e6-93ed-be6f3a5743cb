package howbuy.android.global.message

import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.base.widget.CenterImageSpan
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import howbuy.android.global.PATH_FRAG_MSG_HOLD_LIST
import howbuy.android.global.PATH_FRAG_MSG_TRADE_LIST
import howbuy.android.global.R

/**
 * 持仓动态消息列表
 * <AUTHOR>
 * @Date 2025/5/29
 * @Version V3.0
 */
@Route(path = PATH_FRAG_MSG_HOLD_LIST)
class FragMsgHoldList : AbsFragMessageList<MessageItem>() {

    override fun createAdapter(): PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder> {
        return AdpMsgHoldList()
    }

    override fun parseArgment(arg: Bundle?) {
        super.parseArgment(arg)
        //TODO wy 持仓动态全部已读
    }

    override fun parseMessageList(responseData: Any): MutableList<MessageItem> {
        return if (responseData is MessageList) {
            responseData.messageList ?: mutableListOf()
        } else {
            mutableListOf()
        }
    }

    override fun getMessageDate(item: MessageItem): String? {
        return item.messageDate
    }

    //进入列表就全部已读，不需要单条已读
    override fun handleMessageClick(data: MessageItem, position: Int) {
        //TODO wy HbAnalytics.onClick("611330")
        context?.let {
            MessageLauncher.launcherMsg(it, data, isMessage = true, isMineClick = false)
        }
    }

    override fun getMessageId(item: MessageItem): String? = item.messageId

    /**
     * TODO wy 实现具体的API调用逻辑
     * 这里调用通用的消息列表API，不传分类ID
     */
    override fun requestData(needCount: Boolean, lastMsgId: String?) {
        UserRequest.queryMessageList(
            "0",
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            0
        ) {
            renderData(it)
        }
    }
}

class AdpMsgHoldList : PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder>() {

    override fun addItemTypes() {
        addItemType(TYPE_PINNED_HEADER, R.layout.item_message_date)
        addItemType(TYPE_DATA, R.layout.item_msg_hold_list)
    }

    override fun convert(holder: BaseViewHolder, item: PinnedHeaderEntity<MessageItem>) {
        when (holder.itemViewType) {
            TYPE_PINNED_HEADER -> {
                holder.setText(R.id.tv_date, item.pinnedHeaderName)
            }

            TYPE_DATA -> {
                holder.bindingAdapterPosition.let {
                    val lp = holder.getView<View>(R.id.container).layoutParams as MarginLayoutParams
                    val nextItem = getItemOrNull(it + 1)
                    if (nextItem != null && nextItem.itemType == TYPE_PINNED_HEADER) {
                        lp.bottomMargin = 0
                    } else {
                        lp.bottomMargin = 15f.dp2px()
                    }
                }
                val messageItem = item.data
                if (messageItem != null) {
                    holder.setText(R.id.tv_desc, FundTextUtils.showTextEmpty(messageItem.messageDesc))
                        .setText(R.id.tv_date, FundTextUtils.formatDate(messageItem.messageDate, "yyyyMMdd", "M月d日", "yyyy年M月d日"))
                    val spTitle: SpannableString
                    if (TextUtils.equals("1", messageItem.readStatus)) {
                        //已读
                        spTitle = SpannableString(FundTextUtils.showTextEmpty(messageItem.messageTitle))
                    } else {
                        //未读
                        spTitle = SpannableString(" ${FundTextUtils.showTextEmpty(messageItem.messageTitle)}")
                        val drawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.dot_red_e94444)
                        drawable?.let {
                            it.setBounds(0, 0, DensityUtils.dp2px(9f), DensityUtils.dp2px(9f))
                            spTitle.setSpan(CenterImageSpan(it, DensityUtils.dp2px(5f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                        }
                    }
                    holder.setText(R.id.tv_title, spTitle)
                        .setGone(R.id.btn, TextUtils.equals("1", messageItem.readStatus))
                        .setGone(R.id.ic_arrow, !TextUtils.equals("1", messageItem.readStatus))
                }
            }
        }
    }

}