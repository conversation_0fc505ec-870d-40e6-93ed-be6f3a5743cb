package howbuy.android.global.message

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.TextView
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderItemDecoration
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.global.common.LiveDataEventKey
import com.howbuy.global.data_api.apiUserMessage
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.hbrefresh.layout.api.RefreshLayout
import com.howbuy.hbrefresh.layout.listener.OnRefreshLoadmoreListener
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.MathUtils
import howbuy.android.global.R
import howbuy.android.global.databinding.FragMessageListBinding
import kotlin.math.max

/**
 * 消息列表抽象基类
 * 提取公共逻辑，避免重复代码
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
abstract class AbsFragMessageList : AbsFragViewBinding<FragMessageListBinding>() {

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragMessageListBinding {
        return FragMessageListBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_message_list

    companion object {
        const val TYPE_MESSAGE = 0
        const val TYPE_TODO = 1
        protected const val PAGE_SIZE = 10
    }

    protected var mPageType = TYPE_MESSAGE
    protected var messageCategoryId: String? = null
    protected var mLoadType = 0
    protected lateinit var mAdapter: AdpMessageList
    private var lastTime = ""

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        initRefreshLayout()
        initRecyclerView()
        initLiveDataObserver()
    }

    /**
     * 初始化下拉刷新布局
     */
    private fun initRefreshLayout() {
        binding.refreshLayout.isEnableOverScrollBounce = false
        binding.refreshLayout.isEnableLoadmore = true
        binding.refreshLayout.isEnableRefresh = true
        binding.refreshLayout.setEnableLoadmoreWhenContentNotFull(false)
        binding.refreshLayout.setOnRefreshLoadmoreListener(object : OnRefreshLoadmoreListener {
            override fun onRefresh(refreshlayout: RefreshLayout?) {
                loadData(ValConfig.LOAD_LIST_REFUSH)
            }

            override fun onLoadmore(refreshlayout: RefreshLayout?) {
                loadData(ValConfig.LOAD_LIST_PAGE)
            }
        })
    }

    /**
     * 初始化RecyclerView
     */
    private fun initRecyclerView() {
        binding.rv.addItemDecoration(PinnedHeaderItemDecoration.Builder(PinnedBaseQuickAdapter.TYPE_PINNED_HEADER).create())
    }

    /**
     * 初始化LiveData观察者
     */
    private fun initLiveDataObserver() {
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_MESSAGE_TODO_DONE, MessageItem::class.java).observeBus(this) { message ->
            //待办消息完成，待办移除
            if (mPageType == TYPE_MESSAGE) {
                toDoDoneForMessage(message)
            } else {
                todoDone(message)
            }
        }
    }

    override fun parseArgment(arg: Bundle?) {
        mPageType = arg?.getInt(ValConfig.IT_TYPE, TYPE_MESSAGE) ?: TYPE_MESSAGE
        messageCategoryId = arg?.getString(ValConfig.IT_ID)

        // 创建适配器
        mAdapter = createAdapter()
        mAdapter.setOnItemClickListener { adapter, view, position ->
            val data = mAdapter.getItem(position).data
            if (data != null) {
                onMessageClick(data, position)
            }
        }
        mAdapter.setEmptyView(createEmptyView())
        binding.rv.adapter = mAdapter
        mAdapter.isUseEmpty = false

        // 加载数据
        loadData(ValConfig.LOAD_LIST_FIRST)
    }

    /**
     * 创建适配器，子类可以重写以提供不同的适配器
     */
    protected open fun createAdapter(): AdpMessageList {
        return AdpMessageList(mPageType)
    }

    /**
     * 创建空视图
     */
    protected open fun createEmptyView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.sm_empty_page, binding.rv, false)
        val image = view.findViewById<ImageView>(R.id.iv_no_data)
        view.findViewById<TextView>(R.id.tv_tips).text = getEmptyText()
        val lp = image.layoutParams as MarginLayoutParams
        image.setImageResource(getEmptyImageResource())
        lp.topMargin = 60.dp2px()
        return view
    }

    /**
     * 获取空视图文本，子类可以重写
     */
    protected open fun getEmptyText(): String {
        return "暂无消息"
    }

    /**
     * 获取空视图图片资源，子类可以重写
     */
    protected open fun getEmptyImageResource(): Int {
        return R.mipmap.message_empty_img
    }

    /**
     * 加载数据，子类需要实现具体的API调用
     */
    protected fun loadData(loadType: Int) {
        mLoadType = loadType
        val needCount: Boolean
        val lastMsgId: String?

        if (loadType == ValConfig.LOAD_LIST_FIRST) {
            showAlermDlg("加载中", false, false)
        }

        if (loadType == ValConfig.LOAD_LIST_PAGE) {
            //下一页
            needCount = false
            lastMsgId = mAdapter.data.lastOrNull()?.data?.messageId
        } else {
            //第一页
            binding.rv.scrollToPosition(0)
            needCount = true
            lastMsgId = ""
        }

        // 调用子类实现的具体API请求方法
        requestData(needCount, lastMsgId)
    }

    /**
     * 请求数据的抽象方法，子类需要实现具体的API调用
     */
    protected abstract fun requestData(needCount: Boolean, lastMsgId: String?)

    /**
     * 渲染数据的通用方法
     */
    protected fun renderData(result: ReqResult<ReqNetOpt>) {
        if (activity == null || requireActivity().isFinishing) {
            return
        }
        binding.refreshLayout.finishRefresh()
        binding.refreshLayout.finishLoadmore()

        if (result.isSuccess && result.mData != null) {
            val body = result.mData as MessageList
            val list = body.messageList ?: mutableListOf()

            if (mLoadType == ValConfig.LOAD_LIST_PAGE) {
                mAdapter.addData(formatData(list))
            } else {
                mAdapter.isUseEmpty = true
                mAdapter.setList(formatData(list))

                // 更新计数，子类可以重写此方法
                updateCounts(body)
            }
            binding.refreshLayout.isEnableLoadmore = list.size >= PAGE_SIZE
        } else {
            mAdapter.isUseEmpty = true
            if (mLoadType == ValConfig.LOAD_LIST_PAGE) {
                mAdapter.addData(mutableListOf())
            } else {
                mAdapter.setList(mutableListOf())
            }
        }
        showAlermDlg(null, 0)
    }

    /**
     * 更新计数，子类可以重写以实现不同的计数更新逻辑
     */
    protected open fun updateCounts(body: MessageList) {
        if (mPageType == TYPE_MESSAGE) {
            (parentFragment as? FragMessageCenter)?.updateUnReadCount(MathUtils.forValI(body.unReadCount, 0))
        } else {
            (parentFragment as? FragMessageCenter)?.updateTodoCount(MathUtils.forValI(body.todoCount, 0))
        }
        apiUserMessage().updateCount(MathUtils.forValI(body.unReadCount, 0), MathUtils.forValI(body.todoCount, 0))
    }

    /**
     * 格式化数据，用于日期按月分组添加header
     * yyyy年M月
     */
    protected fun formatData(list: MutableList<MessageItem>): MutableList<PinnedHeaderEntity<MessageItem>> {
        if (mLoadType != ValConfig.LOAD_LIST_PAGE) lastTime = ""
        val data = mutableListOf<PinnedHeaderEntity<MessageItem>>()
        list.forEach {
            val currentTime = DateUtils.timeFormat(it.messageDate, DateUtils.DATEF_YMD, "yyyy年M月")
            if (!TextUtils.isEmpty(currentTime) && !TextUtils.equals(lastTime, currentTime)) {
                data.add(PinnedHeaderEntity(null, PinnedBaseQuickAdapter.TYPE_PINNED_HEADER, currentTime))
                lastTime = currentTime!!
            }
            data.add(PinnedHeaderEntity(it, PinnedBaseQuickAdapter.TYPE_DATA, ""))
        }
        return data
    }

    /**
     * 消息点击
     */
    protected open fun onMessageClick(data: MessageItem, position: Int) {
        HbAnalytics.onClick(getAnalyticsClickId())
        context?.let {
            MessageLauncher.launcherMsg(it, data, mPageType == TYPE_MESSAGE, false)
        }
        if (mPageType == TYPE_MESSAGE && !TextUtils.equals("1", data.readStatus)) {
            data.readStatus = "1"
            mAdapter.notifyItemChanged(position)
            (parentFragment as? FragMessageCenter)?.reduceUnReadCount()
        }
    }

    /**
     * 获取埋点点击ID，子类可以重写
     */
    protected open fun getAnalyticsClickId(): String {
        return if (mPageType == TYPE_MESSAGE) "611330" else "611340"
    }

    /**
     * 未读消息全部已读
     */
    fun readAll() {
        mAdapter.data.forEach {
            if (it.data != null && !TextUtils.equals("1", it.data.readStatus)) {
                it.data.readStatus = "1"
            }
        }
        mAdapter.notifyDataSetChanged()
    }

    /**
     * 待办完成同步将消息列表中待办状态变更
     */
    protected fun toDoDoneForMessage(message: MessageItem) {
        run loop@{
            mAdapter.data.forEachIndexed { index, item ->
                if (item.data != null && TextUtils.equals(message.messageId, item.data.messageId)) {
                    item.data.todoStatus = "1"
                    mAdapter.notifyItemChanged(index)
                    return@loop
                }
            }
        }
    }

    /**
     * 待办已完成
     */
    protected fun todoDone(message: MessageItem) {
        //待办完成就一定减一，更新本地待办数量
        val count = apiUserMessage().todoCount()
        apiUserMessage().updateTodoCount(max(count - 1, 0))
        (parentFragment as? FragMessageCenter)?.reduceTodoCount()
        //删除数据
        var delIndex: Int = -1
        mAdapter.data.also { list ->
            list.forEachIndexed { index, item ->
                if (item.data != null && TextUtils.equals(item.data.messageId, message.messageId)) {
                    delIndex = index
                    return@also
                }
            }
        }
        //判断是否需要删除月份头
        var preItem: PinnedHeaderEntity<MessageItem>? = null
        var nextItem: PinnedHeaderEntity<MessageItem>? = null
        if (delIndex >= 1) {
            preItem = mAdapter.data[delIndex - 1]
        }
        if (delIndex < mAdapter.data.size - 1) {
            nextItem = mAdapter.data[delIndex + 1]
        }
        GlobalApp.getApp().runOnUiThread({
            if ((preItem != null && preItem.itemType == PinnedBaseQuickAdapter.TYPE_PINNED_HEADER) &&
                ((nextItem != null && nextItem.itemType == PinnedBaseQuickAdapter.TYPE_PINNED_HEADER)
                        || delIndex == mAdapter.itemCount - 1)
            ) {
                mAdapter.removeAt(delIndex)
                mAdapter.removeAt(delIndex - 1)
            } else {
                mAdapter.removeAt(delIndex)
            }
        }, 0)
    }
}
