package howbuy.android.global.message

import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.TextView
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderItemDecoration
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.config.ValConfig
import com.howbuy.fund.base.frag.AbsFragViewBinding
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.net.entity.http.ReqNetOpt
import com.howbuy.fund.net.entity.http.ReqResult
import com.howbuy.global.common.LiveDataEventKey
import com.howbuy.global.data_api.apiUserMessage
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.hbrefresh.layout.api.RefreshLayout
import com.howbuy.hbrefresh.layout.listener.OnRefreshLoadmoreListener
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.MathUtils
import howbuy.android.global.R
import howbuy.android.global.databinding.FragMessageListBinding
import kotlin.math.max

/**
 * 消息列表抽象基类
 * 提取公共逻辑，避免重复代码
 * @param T 消息实体类型
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
abstract class AbsFragMessageList<T> : AbsFragViewBinding<FragMessageListBinding>() {

    override fun createViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragMessageListBinding {
        return FragMessageListBinding.inflate(inflater, container, false)
    }

    override fun getFragLayoutId() = R.layout.frag_message_list

    companion object {
        const val PAGE_SIZE = 10
    }

    protected var mLoadType = 0
    protected lateinit var mAdapter: PinnedBaseQuickAdapter<PinnedHeaderEntity<T>, *>
    private var lastTime = ""

    override fun stepAllViews(root: View?, savedInstanceState: Bundle?) {
        super.stepAllViews(root, savedInstanceState)
        initRefreshLayout()
        initRecyclerView()
    }

    /**
     * 初始化下拉刷新布局
     */
    private fun initRefreshLayout() {
        binding.refreshLayout.isEnableOverScrollBounce = false
        binding.refreshLayout.isEnableLoadmore = true
        binding.refreshLayout.isEnableRefresh = true
        binding.refreshLayout.setEnableLoadmoreWhenContentNotFull(false)
        binding.refreshLayout.setOnRefreshLoadmoreListener(object : OnRefreshLoadmoreListener {
            override fun onRefresh(refreshlayout: RefreshLayout?) {
                loadData(ValConfig.LOAD_LIST_REFUSH)
            }

            override fun onLoadmore(refreshlayout: RefreshLayout?) {
                loadData(ValConfig.LOAD_LIST_PAGE)
            }
        })
        // 创建适配器
        mAdapter = createAdapter()
        mAdapter.setOnItemClickListener { adapter, view, position ->
            val data = mAdapter.getItem(position).data
            if (data != null) {
                handleMessageClick(data, position)
            }
        }
        mAdapter.setEmptyView(createEmptyView())
        binding.rv.adapter = mAdapter
        mAdapter.isUseEmpty = false
    }

    /**
     * 初始化RecyclerView
     */
    private fun initRecyclerView() {
        binding.rv.addItemDecoration(PinnedHeaderItemDecoration.Builder(PinnedBaseQuickAdapter.TYPE_PINNED_HEADER).create())
    }

    override fun parseArgment(arg: Bundle?) {
        // 加载数据
        loadData(ValConfig.LOAD_LIST_FIRST)
    }

    /**
     * 创建适配器，子类需要实现以提供具体的适配器
     */
    protected abstract fun createAdapter(): PinnedBaseQuickAdapter<PinnedHeaderEntity<T>, *>

    /**
     * 创建空视图
     */
    protected open fun createEmptyView(): View {
        val view = LayoutInflater.from(context).inflate(R.layout.sm_empty_page, binding.rv, false)
        val image = view.findViewById<ImageView>(R.id.iv_no_data)
        view.findViewById<TextView>(R.id.tv_tips).text = getEmptyText()
        val lp = image.layoutParams as MarginLayoutParams
        image.setImageResource(getEmptyImageResource())
        lp.topMargin = 60.dp2px()
        return view
    }

    /**
     * 获取空视图文本，子类可以重写
     */
    protected open fun getEmptyText(): String {
        return "暂无消息"
    }

    /**
     * 获取空视图图片资源，子类可以重写
     */
    protected open fun getEmptyImageResource(): Int {
        return R.mipmap.message_empty_img
    }

    /**
     * 加载数据，子类需要实现具体的API调用
     */
    protected fun loadData(loadType: Int) {
        mLoadType = loadType
        val needCount: Boolean
        val lastMsgId: String?

        if (loadType == ValConfig.LOAD_LIST_FIRST) {
            showAlermDlg("加载中", false, false)
        }

        if (loadType == ValConfig.LOAD_LIST_PAGE) {
            //下一页
            needCount = false
            lastMsgId = getMessageId(mAdapter.data.lastOrNull()?.data as T)
        } else {
            //第一页
            binding.rv.scrollToPosition(0)
            needCount = true
            lastMsgId = ""
        }

        // 调用子类实现的具体API请求方法
        requestData(needCount, lastMsgId)
    }

    /**
     * 请求数据的抽象方法，子类需要实现具体的API调用
     */
    protected abstract fun requestData(needCount: Boolean, lastMsgId: String?)

    /**
     * 渲染数据的通用方法
     */
    protected fun renderData(result: ReqResult<ReqNetOpt>) {
        if (activity == null || requireActivity().isFinishing) {
            return
        }
        binding.refreshLayout.finishRefresh()
        binding.refreshLayout.finishLoadmore()

        if (result.isSuccess && result.mData != null) {
            val list = parseMessageList(result.mData)

            if (mLoadType == ValConfig.LOAD_LIST_PAGE) {
                mAdapter.addData(formatData(list))
            } else {
                mAdapter.isUseEmpty = true
                mAdapter.setList(formatData(list))
                // 更新计数，子类可以重写此方法
                updateCounts(result.mData)
            }
            binding.refreshLayout.isEnableLoadmore = list.size >= PAGE_SIZE
        } else {
            mAdapter.isUseEmpty = true
            if (mLoadType == ValConfig.LOAD_LIST_PAGE) {
                mAdapter.addData(mutableListOf())
            } else {
                mAdapter.setList(mutableListOf())
            }
        }
        showAlermDlg(null, 0)
    }

    /**
     * 从响应数据中解析消息列表，子类需要实现
     */
    protected abstract fun parseMessageList(responseData: Any): MutableList<T>

    /**
     * 更新计数，子类可以重写以实现不同的计数更新逻辑
     */
    protected open fun updateCounts(responseData: Any) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 格式化数据，用于日期按月分组添加header
     * yyyy年M月
     */
    protected fun formatData(list: MutableList<T>): MutableList<PinnedHeaderEntity<T>> {
        if (mLoadType != ValConfig.LOAD_LIST_PAGE) lastTime = ""
        val data = mutableListOf<PinnedHeaderEntity<T>>()
        list.forEach {
            val messageDate = getMessageDate(it)
            val currentTime = DateUtils.timeFormat(messageDate, DateUtils.DATEF_YMD, "yyyy年M月")
            if (!TextUtils.isEmpty(currentTime) && !TextUtils.equals(lastTime, currentTime)) {
                data.add(PinnedHeaderEntity(null, PinnedBaseQuickAdapter.TYPE_PINNED_HEADER, currentTime))
                lastTime = currentTime!!
            }
            data.add(PinnedHeaderEntity(it, PinnedBaseQuickAdapter.TYPE_DATA, ""))
        }
        return data
    }

    /**
     * 获取消息日期，子类需要实现
     */
    protected abstract fun getMessageDate(item: T): String?

    /**
     * 处理消息点击，子类需要实现具体的点击逻辑
     */
    protected abstract fun handleMessageClick(data: T, position: Int)

    /**
     * 获取消息ID，用于待办处理，子类可以重写
     */
    protected open fun getMessageId(item: T): String? {
        return null
    }
}
