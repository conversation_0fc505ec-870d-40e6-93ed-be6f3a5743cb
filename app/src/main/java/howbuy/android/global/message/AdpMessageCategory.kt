package howbuy.android.global.message

import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.global.user.entity.MessageCategory
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.MathUtils
import howbuy.android.global.R

/**
 * 消息分类列表适配器
 * <AUTHOR>
 * @Date 2025/1/15
 * @Version V2.6
 */
class AdpMessageCategory : BaseQuickAdapter<MessageCategory, BaseViewHolder>(R.layout.item_message_category) {

    override fun convert(holder: BaseViewHolder, item: MessageCategory) {
        // 设置分类名称
        holder.setText(R.id.tv_name, FundTextUtils.showTextEmpty(item.categoryName))
        holder.setImageResource(R.id.iv_icon, getIconResource(item.categoryId))

        // 设置未读数量（消息分类只显示未读消息数量）
        val unReadCount = MathUtils.forValI(item.unReadCount, 0)
        if (unReadCount > 0) {
            holder.setVisible(R.id.tv_unread_count, true)
            holder.setText(R.id.tv_unread_count, FundTextUtils.formatMaxNum(item.unReadCount, 99))
        } else {
            holder.setVisible(R.id.tv_unread_count, false)
        }

        // 设置最新消息内容
        holder.setText(R.id.tv_latest_message, FundTextUtils.showTextEmpty(item.latestMessage,"暂无新消息"))
            .setText(R.id.tv_update_time, DateUtils.timeFormat(item.lastUpdateTime, "yyyyMMdd", "yyyy年MM月dd日"))
    }

    /**
     * 根据图标名称获取图标资源
     */
    private fun getIconResource(messageCategory: String?): Int {
        return when (messageCategory) {
            "1" -> R.mipmap.ic_msg_sys
            "2" -> R.mipmap.ic_msg_trade
            "3" -> R.mipmap.ic_msg_hold
            else -> R.mipmap.ic_msg_sys
        }
    }
}
