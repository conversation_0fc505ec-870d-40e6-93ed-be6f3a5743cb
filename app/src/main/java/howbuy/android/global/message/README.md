# 消息列表架构重构说明

## 概述

为了避免在多个消息类型的Fragment中重复编写相同的逻辑，我们抽取了一个抽象基类 `AbsFragMessageList`，将公共逻辑提取到基类中，不同的消息类型只需要继承基类并实现具体的API调用逻辑即可。

## 架构设计

### 抽象基类：AbsFragMessageList

包含以下公共逻辑：
- UI初始化（下拉刷新、上拉加载、RecyclerView配置）
- 数据格式化（按月分组添加header）
- 消息点击处理
- 待办完成处理
- 空视图处理
- LiveData观察者

### 具体实现类

#### 1. FragMessageList
- **用途**：处理待办消息和通用消息列表
- **特点**：原有的消息列表功能，支持传入分类ID
- **路由**：`PATH_FRAG_MESSAGE_LIST`

#### 2. FragMessageListByCategory
- **用途**：按分类显示的消息列表
- **特点**：专门处理特定分类的消息，从消息分类页面跳转过来
- **路由**：`PATH_FRAG_MESSAGE_LIST_BY_CATEGORY`

#### 3. FragFundTradeMessageList
- **用途**：基金交易提醒通知列表
- **特点**：固定显示基金交易分类的消息，可以有专门的UI定制
- **路由**：`PATH_FRAG_FUND_TRADE_MESSAGE_LIST`

## 使用方法

### 创建新的消息类型Fragment

1. **继承抽象基类**：
```kotlin
class FragYourMessageList : AbsFragMessageList() {
    // 实现具体逻辑
}
```

2. **实现必要的抽象方法**：
```kotlin
override fun requestData(needCount: Boolean, lastMsgId: String?) {
    // 实现具体的API调用
    UserRequest.queryMessageList(
        mPageType.toString(),
        needCount,
        lastMsgId,
        PAGE_SIZE.toString(),
        yourCategoryId, // 你的分类ID
        0
    ) {
        renderData(it)
    }
}
```

3. **可选：重写其他方法进行定制**：
```kotlin
// 重写埋点ID
override fun getAnalyticsClickId(): String {
    return "your_analytics_id"
}

// 重写空视图文本
override fun getEmptyText(): String {
    return "你的空视图文本"
}

// 重写空视图图片
override fun getEmptyImageResource(): Int {
    return R.mipmap.your_empty_image
}

// 重写适配器创建（如果需要特殊的适配器）
override fun createAdapter(): AdpMessageList {
    return YourCustomAdapter(mPageType)
}
```

4. **添加路由注解**：
```kotlin
@Route(path = PATH_FRAG_YOUR_MESSAGE_LIST)
class FragYourMessageList : AbsFragMessageList() {
    // ...
}
```

5. **在RouterPath.kt中添加路由常量**：
```kotlin
const val PATH_FRAG_YOUR_MESSAGE_LIST = "/global/main/FragYourMessageList"
```

### 跳转到消息列表

```kotlin
// 跳转到通用消息列表
RouterHelper.launchFrag(
    this, PATH_FRAG_MESSAGE_LIST,
    NavHelper.obtainArg(
        "消息列表",
        ValConfig.IT_TYPE, AbsFragMessageList.TYPE_MESSAGE,
        ValConfig.IT_ID, categoryId // 可选的分类ID
    )
)

// 跳转到按分类的消息列表
RouterHelper.launchFrag(
    this, PATH_FRAG_MESSAGE_LIST_BY_CATEGORY,
    NavHelper.obtainArg(
        "分类消息",
        ValConfig.IT_TYPE, AbsFragMessageList.TYPE_MESSAGE,
        ValConfig.IT_ID, categoryId
    )
)

// 跳转到基金交易消息列表
RouterHelper.launchFrag(
    this, PATH_FRAG_FUND_TRADE_MESSAGE_LIST,
    NavHelper.obtainArg("基金交易提醒")
)
```

## 优势

1. **代码复用**：公共逻辑只需要维护一份
2. **易于扩展**：新增消息类型只需要继承基类并实现API调用
3. **统一体验**：所有消息列表的交互逻辑保持一致
4. **易于维护**：修改公共逻辑只需要修改基类
5. **类型安全**：通过继承保证了接口的一致性

## 注意事项

1. 如果需要完全不同的UI布局，建议创建新的布局文件并重写相关方法
2. 如果需要不同的适配器逻辑，可以重写 `createAdapter()` 方法
3. 埋点ID需要根据具体的消息类型进行区分
4. 空视图的文本和图片可以根据消息类型进行定制
