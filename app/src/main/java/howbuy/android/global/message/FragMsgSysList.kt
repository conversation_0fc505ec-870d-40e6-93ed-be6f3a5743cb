package howbuy.android.global.message

import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.base.widget.CenterImageSpan
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import howbuy.android.global.PATH_FRAG_MSG_SYS_LIST
import howbuy.android.global.R

/**
 * 系统消息列表
 * 继承抽象基类，只需实现具体的API调用逻辑
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
@Route(path = PATH_FRAG_MSG_SYS_LIST)
class FragMsgSysList : AbsFragMessageList<MessageItem>() {

    override fun createAdapter(): PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder> {
        return AdpMsgSysList()
    }

    override fun parseMessageList(responseData: Any): MutableList<MessageItem> {
        return if (responseData is MessageList) {
            responseData.messageList ?: mutableListOf()
        } else {
            mutableListOf()
        }
    }

    override fun getMessageDate(item: MessageItem): String? {
        return item.messageDate
    }

    override fun handleMessageClick(data: MessageItem, position: Int) {
        HbAnalytics.onClick("611330")
        context?.let {
            MessageLauncher.launcherMsg(it, data, true, false)
        }
        if (!TextUtils.equals("1", data.readStatus)) {
            data.readStatus = "1"
            mAdapter.notifyItemChanged(position)
            //TODO wy 未读消息数量 -1
            //(parentFragment as? FragMessageCenter)?.reduceUnReadCount()
        }
    }

    override fun getMessageId(item: MessageItem): String? = item.messageId

    /**
     * 实现具体的API调用逻辑
     * 这里调用通用的消息列表API，不传分类ID
     */
    override fun requestData(needCount: Boolean, lastMsgId: String?) {
        UserRequest.queryMessageList(
            "0",
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            0
        ) {
            renderData(it)
        }
    }
}

class AdpMsgSysList : PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder>() {

    override fun addItemTypes() {
        addItemType(TYPE_PINNED_HEADER, R.layout.item_message_date)
        addItemType(TYPE_DATA, R.layout.item_msg_sys_list)
    }

    override fun convert(holder: BaseViewHolder, item: PinnedHeaderEntity<MessageItem>) {
        when (holder.itemViewType) {
            TYPE_PINNED_HEADER -> {
                holder.setText(R.id.tv_date, item.pinnedHeaderName)
            }

            TYPE_DATA -> {
                holder.bindingAdapterPosition.let {
                    val lp = holder.getView<View>(R.id.container).layoutParams as MarginLayoutParams
                    val nextItem = getItemOrNull(it + 1)
                    if (nextItem != null && nextItem.itemType == TYPE_PINNED_HEADER) {
                        lp.bottomMargin = 0
                    } else {
                        lp.bottomMargin = 15f.dp2px()
                    }
                }
                val messageItem = item.data
                if (messageItem != null) {
                    holder.setText(R.id.tv_desc, FundTextUtils.showTextEmpty(messageItem.messageDesc))
                        .setText(R.id.tv_date, FundTextUtils.formatDate(messageItem.messageDate, "yyyyMMdd", "M月d日", "yyyy年M月d日"))
                    val spTitle: SpannableString
                    if (TextUtils.equals("1", messageItem.readStatus)) {
                        //已读
                        spTitle = SpannableString(FundTextUtils.showTextEmpty(messageItem.messageTitle))
                    } else {
                        //未读
                        spTitle = SpannableString(" ${FundTextUtils.showTextEmpty(messageItem.messageTitle)}")
                        val drawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.drawable.dot_red_e94444)
                        drawable?.let {
                            it.setBounds(0, 0, DensityUtils.dp2px(9f), DensityUtils.dp2px(9f))
                            spTitle.setSpan(CenterImageSpan(it, DensityUtils.dp2px(5f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                        }
                    }
                    holder.setText(R.id.tv_title, spTitle)
                        .setGone(R.id.btn, TextUtils.equals("1", messageItem.readStatus))
                        .setGone(R.id.ic_arrow, !TextUtils.equals("1", messageItem.readStatus))
                }
            }
        }
    }

}