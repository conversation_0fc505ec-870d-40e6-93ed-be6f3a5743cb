package howbuy.android.global.message

import android.text.TextUtils
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.message.MessageLauncher
import howbuy.android.global.PATH_FRAG_MSG_SYS_LIST
import howbuy.android.global.message.adapter.AdpMsgSysList

/**
 * 系统消息列表
 * 继承抽象基类，只需实现具体的API调用逻辑
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
@Route(path = PATH_FRAG_MSG_SYS_LIST)
class FragMsgSysList : AbsFragMessageList<MessageItem>() {

    override fun createAdapter(): PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder> {
        return AdpMsgSysList()
    }

    override fun parseMessageList(responseData: Any): MutableList<MessageItem> {
        return if (responseData is MessageList) {
            responseData.messageList ?: mutableListOf()
        } else {
            mutableListOf()
        }
    }

    override fun getMessageDate(item: MessageItem): String? {
        return item.messageDate
    }

    override fun handleMessageClick(data: MessageItem, position: Int) {
        HbAnalytics.onClick("611330")
        context?.let {
            MessageLauncher.launcherMsg(it, data, true, false)
        }
        if (!TextUtils.equals("1", data.readStatus)) {
            data.readStatus = "1"
            mAdapter.notifyItemChanged(position)
            //TODO wy 未读消息数量 -1
            //(parentFragment as? FragMessageCenter)?.reduceUnReadCount()
        }
    }

    override fun getMessageId(item: MessageItem): String? = item.messageId

    /**
     * 实现具体的API调用逻辑
     * 这里调用通用的消息列表API，不传分类ID
     */
    override fun requestData(needCount: Boolean, lastMsgId: String?) {
        UserRequest.queryMessageList(
            "0",
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            0
        ) {
            renderData(it)
        }
    }
}