package howbuy.android.global.message.adapter

import android.text.TextUtils
import android.view.View
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.lib.utils.DateUtils
import howbuy.android.global.R
import howbuy.android.global.message.entity.TradeMessageItem

/**
 * 持仓动态消息
 * <AUTHOR>
 * @Date 2025/5/29
 * @Version V3.0
 */
class AdpMsgHoldList : PinnedBaseQuickAdapter<PinnedHeaderEntity<TradeMessageItem>, BaseViewHolder>() {

    override fun addItemTypes() {
        addItemType(TYPE_PINNED_HEADER, R.layout.item_message_date)
        addItemType(TYPE_DATA, R.layout.item_msg_trade_list)
    }

    override fun convert(holder: BaseViewHolder, item: PinnedHeaderEntity<TradeMessageItem>) {
        when (holder.itemViewType) {
            TYPE_PINNED_HEADER -> {
                // 日期分组头部
                holder.setText(R.id.tv_date, item.pinnedHeaderName)
            }
            TYPE_DATA -> {
                // 交易消息内容
                val tradeMessage = item.data
                if (tradeMessage != null) {
                    bindTradeMessage(holder, tradeMessage)
                }
            }
        }
    }

    /**
     * 绑定交易消息数据
     */
    private fun bindTradeMessage(holder: BaseViewHolder, tradeMessage: TradeMessageItem) {
        // 标题
        holder.setText(R.id.tv_title, tradeMessage.messageTitle ?: "基金交易提醒通知")

        // 消息日期
        val messageDate = formatMessageDate(tradeMessage.messageDate)
        holder.setText(R.id.tv_message_date, messageDate)

        // 产品名称
        holder.setText(R.id.tv_product_name, tradeMessage.productName ?: "")

        // 交易类型
        holder.setText(R.id.tv_trade_type, tradeMessage.tradeType ?: "")

        // 交易时间
        val tradeTime = formatTradeTime(tradeMessage.tradeTime)
        holder.setText(R.id.tv_trade_time, tradeTime)

        // 交易状态
        holder.setText(R.id.tv_trade_status, tradeMessage.tradeStatus ?: "")

        // 红点显示逻辑（未读显示，已读隐藏）
        val redDot = holder.getView<View>(R.id.red_dot)
        val isRead = TextUtils.equals("1", tradeMessage.readStatus)
        redDot.visibility = if (isRead) View.GONE else View.VISIBLE
    }

    /**
     * 格式化消息日期
     * 从 yyyyMMdd 格式转换为 yyyy年M月d日
     */
    private fun formatMessageDate(dateStr: String?): String {
        if (TextUtils.isEmpty(dateStr)) return ""
        return DateUtils.timeFormat(dateStr, DateUtils.DATEF_YMD, "yyyy年M月d日") ?: ""
    }

    /**
     * 格式化交易时间
     * 从 yyyyMMdd 格式转换为 yyyy年M月d日
     */
    private fun formatTradeTime(dateStr: String?): String {
        if (TextUtils.isEmpty(dateStr)) return ""
        return DateUtils.timeFormat(dateStr, DateUtils.DATEF_YMD, "yyyy年M月d日") ?: ""
    }
}