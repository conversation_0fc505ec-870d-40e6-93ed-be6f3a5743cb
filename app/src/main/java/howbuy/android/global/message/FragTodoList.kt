package howbuy.android.global.message

import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.content.ContextCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.howbuy.fund.base.adapter.pinned.PinnedBaseQuickAdapter
import com.howbuy.fund.base.adapter.pinned.PinnedHeaderEntity
import com.howbuy.fund.base.analytics.HbAnalytics
import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.fund.base.utils.FundTextUtils
import com.howbuy.fund.base.utils.dp2px
import com.howbuy.fund.base.widget.CenterImageSpan
import com.howbuy.global.common.LiveDataEventKey
import com.howbuy.global.data_api.apiUserMessage
import com.howbuy.global.user.UserRequest
import com.howbuy.global.user.entity.MessageItem
import com.howbuy.global.user.entity.MessageList
import com.howbuy.global.user.message.MessageLauncher
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.lib.utils.DensityUtils
import com.howbuy.lib.utils.MathUtils
import howbuy.android.global.PATH_FRAG_TODO_LIST
import howbuy.android.global.R
import kotlin.math.max

/**
 * 待办列表
 * 继承抽象基类，只需实现具体的API调用逻辑
 * <AUTHOR>
 * @Date 2024/7/9
 * @Version V2.2
 */
@Route(path = PATH_FRAG_TODO_LIST)
class FragTodoList : AbsFragMessageList<MessageItem>() {

    override fun createAdapter(): PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder> {
        return AdpTodoList()
    }

    override fun parseArgment(arg: Bundle?) {
        LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_MESSAGE_TODO_DONE, MessageItem::class.java).observeBus(this) { message ->
            todoDone(message)
        }
        super.parseArgment(arg)
    }

    override fun parseMessageList(responseData: Any): MutableList<MessageItem> {
        return if (responseData is MessageList) {
            responseData.messageList ?: mutableListOf()
        } else {
            mutableListOf()
        }
    }

    override fun getMessageDate(item: MessageItem): String? {
        return item.messageDate
    }

    override fun handleMessageClick(data: MessageItem, position: Int) {
        HbAnalytics.onClick("611340")
        MessageLauncher.launcherMsg(requireContext(), data, isMessage = false, isMineClick = false)
    }

    override fun updateCounts(responseData: Any) {
        if (responseData is MessageList) {
            (parentFragment as? FragMessageCenter)?.updateTodoCount(MathUtils.forValI(responseData.todoCount, 0))
            apiUserMessage().updateCount(
                MathUtils.forValI(responseData.unReadCount, 0),
                MathUtils.forValI(responseData.todoCount, 0)
            )
        }
    }

    override fun getMessageId(item: MessageItem): String? = item.messageId

    private fun todoDone(message: MessageItem) {
        //待办完成就一定减一，更新本地待办数量
        val count = apiUserMessage().todoCount()
        apiUserMessage().updateTodoCount(max(count - 1, 0))
        (parentFragment as? FragMessageCenter)?.reduceTodoCount()
        //删除数据
        var delIndex: Int = -1
        mAdapter.data.also { list ->
            list.forEachIndexed { index, item ->
                if (item.data != null && TextUtils.equals(item.data.messageId, message.messageId)) {
                    delIndex = index
                    return@also
                }
            }
        }
        //判断是否需要删除月份头
        var preItem: PinnedHeaderEntity<MessageItem>? = null
        var nextItem: PinnedHeaderEntity<MessageItem>? = null
        if (delIndex >= 1) {
            preItem = mAdapter.data[delIndex - 1]
        }
        if (delIndex < mAdapter.data.size - 1) {
            nextItem = mAdapter.data[delIndex + 1]
        }
        GlobalApp.getApp().runOnUiThread({
            if ((preItem != null && preItem.itemType == PinnedBaseQuickAdapter.TYPE_PINNED_HEADER) &&
                ((nextItem != null && nextItem.itemType == PinnedBaseQuickAdapter.TYPE_PINNED_HEADER)
                        || delIndex == mAdapter.itemCount - 1)
            ) {
                mAdapter.removeAt(delIndex)
                mAdapter.removeAt(delIndex - 1)
            } else {
                mAdapter.removeAt(delIndex)
            }
        }, 0)
    }

    /**
     * 实现具体的API调用逻辑
     * 这里调用通用的消息列表API，不传分类ID
     */
    override fun requestData(needCount: Boolean, lastMsgId: String?) {
        UserRequest.queryMessageList(
            "1",
            needCount,
            lastMsgId,
            PAGE_SIZE.toString(),
            0
        ) {
            renderData(it)
        }
    }
}

class AdpTodoList : PinnedBaseQuickAdapter<PinnedHeaderEntity<MessageItem>, BaseViewHolder>() {

    override fun addItemTypes() {
        addItemType(TYPE_PINNED_HEADER, R.layout.item_message_date)
        addItemType(TYPE_DATA, R.layout.item_todo_list)
    }

    override fun convert(holder: BaseViewHolder, item: PinnedHeaderEntity<MessageItem>) {
        when (holder.itemViewType) {
            TYPE_PINNED_HEADER -> {
                holder.setText(R.id.tv_date, item.pinnedHeaderName)
            }

            TYPE_DATA -> {
                holder.bindingAdapterPosition.let {
                    val lp = holder.getView<View>(R.id.container).layoutParams as MarginLayoutParams
                    val nextItem = getItemOrNull(it + 1)
                    if (nextItem != null && nextItem.itemType == TYPE_PINNED_HEADER) {
                        lp.bottomMargin = 0
                    } else {
                        lp.bottomMargin = 15f.dp2px()
                    }
                }
                val messageItem = item.data
                if (messageItem != null) {
                    holder.setText(R.id.tv_desc, FundTextUtils.showTextEmpty(messageItem.messageDesc))
                        .setText(R.id.tv_date, FundTextUtils.formatDate(messageItem.messageDate, "yyyyMMdd", "M月d日", "yyyy年M月d日"))
                    val spTitle = SpannableString(" ${FundTextUtils.showTextEmpty(messageItem.messageTitle)}")
                    val drawable = ContextCompat.getDrawable(GlobalApp.getApp(), R.mipmap.ic_todo)
                    drawable?.let {
                        it.setBounds(0, 0, DensityUtils.dp2px(22f), DensityUtils.dp2px(22f))
                        spTitle.setSpan(CenterImageSpan(it, DensityUtils.dp2px(5f)), 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                    holder.setText(R.id.tv_title, spTitle)
                }
            }
        }
    }

}