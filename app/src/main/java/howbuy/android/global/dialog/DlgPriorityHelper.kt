package howbuy.android.global.dialog

import com.howbuy.fund.base.livedata.LiveDataBus
import com.howbuy.global.common.LiveDataEventKey
import com.howbuy.global.common.workflow.Node
import com.howbuy.global.common.workflow.WorkFlow
import com.howbuy.global.common.workflow.WorkNode
import com.howbuy.lib.compont.GlobalApp
import com.howbuy.router.provider.IUpGradeProvider
import com.howbuy.router.proxy.Invoker

/**
 * App启动后弹框管理
 * 1. App版本更新弹框
 * 2. 设置二级密码提醒弹框
 * 3. 营销广告弹框
 * <AUTHOR>
 * @Date 2024/10/29
 * @Version V2.5
 */
class DlgPriorityHelper {
    companion object {
        const val NODE_UPDATE_APP = 0
        const val NODE_GESTURE_HINT = 10
        const val NODE_GLOBAL_AD = 20

        @Volatile
        private var instance: DlgPriorityHelper? = null

        fun getInstance(): DlgPriorityHelper {
            return instance ?: synchronized(this) {
                instance ?: DlgPriorityHelper().also { instance = it }
            }
        }
    }

    private var mWorkFlow: WorkFlow? = null

    fun getWorkFlow(): WorkFlow? {
        return mWorkFlow
    }

    fun showAllDialog() {
        if (mWorkFlow != null) {
            mWorkFlow = null
        }
        val builder = WorkFlow.Builder()
            .withNode(createAppUpdateNode())
            .withNode(createGestureHintNode())
            .withNode(createGlobalAdNode())
        //设置回调监听
        mWorkFlow = builder.setCallBack(object : WorkFlow.FlowCallBack {
            override fun onNodeChanged(nodeId: Int) {}
            override fun onFlowFinish() {
                release()
            }
        }).create()
        //开始递归遍历节点
        mWorkFlow?.start()
    }

    /**
     * 释放资源,需要在AtyTbMain关闭的时候,再释放一次,因为可能会触发精选页面的广告LiveData,
     * 又创建了该单例对象
     */
    fun release() {
        if (mWorkFlow != null) {
            mWorkFlow!!.dispose()
        }
        mWorkFlow = null
        instance = null
    }

    /**
     * App更新
     */
    private fun createAppUpdateNode(): WorkNode {
        return WorkNode.build(NODE_UPDATE_APP) { current ->
            Invoker.getInstance().navigation(IUpGradeProvider::class.java).doUpgrade(GlobalApp.getApp()) { current.onCompleted() }
        }
    }

    /**
     * 二级密码设置提醒弹框
     */
    private fun createGestureHintNode(): WorkNode {
        return WorkNode.build(NODE_GESTURE_HINT) {
            //私募全局弹框条件是每次都从网络接口获取数据判断,所以无须再些过滤一次条件
            LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_APP_START_DIALOG_DISPATCH, Node::class.java).postValue(it)
        }
    }

    /**
     * 全局营销广告弹框
     */
    private fun createGlobalAdNode(): WorkNode {
        return WorkNode.build(NODE_GLOBAL_AD) {
            //私募全局弹框条件是每次都从网络接口获取数据判断,所以无须再些过滤一次条件
            LiveDataBus.get().with(LiveDataEventKey.BUS_KEY_APP_START_DIALOG_DISPATCH, Node::class.java).postValue(it)
        }
    }
}