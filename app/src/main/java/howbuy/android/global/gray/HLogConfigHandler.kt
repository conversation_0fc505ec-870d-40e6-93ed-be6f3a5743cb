package howbuy.android.global.gray

import android.util.Log
import com.howbuy.fund.base.storage.CommonStorageUtils
import com.howbuy.fund.logupload.gain.LogGainManager
import com.howbuy.lib.utils.DateUtils
import com.howbuy.lib.utils.LogUtils
import howbuy.android.global.entity.GrayFunctionBean

/**
 * 处理日志回捞的灰度配置逻辑
 * <AUTHOR>
 * @version
 * @date
 */
class HLogConfigHandler {
    @Suppress("PrivatePropertyName")
    private val NO_CONSUMED = 0
    private val onceRecallPrefix = "NEW_HLOG_RECALL_ONCE_"
    private val holdOnRecallPrefix = "NEW_HLOG_RECALL_UNTIL_"

    fun onConfigReady(configList: List<GrayFunctionBean>?) {
        configList?:return
        val switchConfig = filterSwitchConfig(configList)
        switchConfig?.let {
            val on = "A" == it.status
            if (on) LogGainManager.getInstance().switchOn()
            else LogGainManager.getInstance().switchOff()
        }

        /*
         优先执行一次性的的上传配置
         如果没有一次性的配置，尝试有效截止时间内的配置
         */
        val onceUploadList = filterOneUpload(configList)
        if (onceUploadList.isNotEmpty()) {
            debug("once upload shot")
            scheduleHandleUploadTask()
            //保存该配置已消费
            CommonStorageUtils.putInt(onceUploadList.first().key, 1)
            return
        }

        val holdOnList = filterHoldOnUpload(configList)
        if (holdOnList.isNotEmpty()) {
            debug("multiple upload shot")
            scheduleHandleUploadTask()
            return
        }

        debug("no need upload")
    }

    /**
     * 开启一个日志上传任务
     */
    private fun scheduleHandleUploadTask() {
        LogGainManager.getInstance().uploadLogByUser(null/*object : ResultConsumer {
            override fun start() {
                debug("start to upload")
            }

            override fun onProgressChanged(curProgress: Long, totalProgress: Long) {
               debug("progress changed:$curProgress")
            }

            override fun onComplete(successCount: Int, failCount: Int) {
                if\
                LogUtils.pop("<<<<日志上传成功>>>>")
                debug("complete success count:$successCount, failed count:$failCount")
            }
        }*/)
    }

    private fun debug(string: String) {
        if (!LogUtils.mDebugLog) return
        Log.d("[Log-UploadCB]", string)
    }

    /**
     * 筛选出持续可上传的配置指令 && 截止时间有效
     */
    private fun filterHoldOnUpload(configList: List<GrayFunctionBean>): List<GrayFunctionBean> {
        return configList.filter {
            val keyMatch =  !it.key.isNullOrEmpty() && it.key.startsWith(holdOnRecallPrefix)
            var timeValid = false
            if (keyMatch) {
                val time = DateUtils.getTimeFormatLong(it.key.replace(holdOnRecallPrefix, ""), DateUtils.DATEF_YMD_6)
                val timeNow = System.currentTimeMillis()
                timeValid = timeNow < time
            }
            //key匹配成功，且截止时间没有过期
            keyMatch && timeValid && "A" == it.status
        }
    }

    /**
     * 筛选出没有被使用的一次性配置
     */
    private fun filterOneUpload(configList: List<GrayFunctionBean>): List<GrayFunctionBean> {
        return configList.filter {
            val keyMatch =  !it.key.isNullOrEmpty() && it.key.startsWith(onceRecallPrefix)
            //key匹配成功，日期匹配成功，且没有被消费过
            keyMatch
                    && DateUtils.getTimeFormatLong(it.key.replace(onceRecallPrefix, ""), DateUtils.DATEF_YMD_6) > 0L
                    && (CommonStorageUtils.getInt(it.key, NO_CONSUMED) == NO_CONSUMED)
                    && "A" == it.status
        }
    }

    fun filterSwitchConfig(configList: List<GrayFunctionBean>) : GrayFunctionBean? {
        return configList.firstOrNull { it.key == "HLOG_SWITCH_FUND_ANDROID" }
    }
}