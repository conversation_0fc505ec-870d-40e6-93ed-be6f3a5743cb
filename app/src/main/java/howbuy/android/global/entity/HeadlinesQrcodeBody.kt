package howbuy.android.global.entity

import android.os.Parcelable
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import howbuy.android.global.headlines.entity.HeadlinesItem
import kotlinx.android.parcel.Parcelize


/**
 * description.
 * 头条搜索-大家都在搜-接口数据
 * tao.liang
 * 2024/5/14
 */
@Parcelize
data class HeadlinesQrcodeBody(
    val iosImg: String?,  //iOS 二维码
    val androidImg: String?  //Android 二维码
) : AbsNormalBody(), Parcelable {

}