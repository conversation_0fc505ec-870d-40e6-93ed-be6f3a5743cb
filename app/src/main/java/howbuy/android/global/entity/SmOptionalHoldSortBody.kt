package howbuy.android.global.entity

import android.annotation.SuppressLint
import android.os.Parcelable
import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import kotlinx.android.parcel.Parcelize

/**
 * Create by tao.liang on 2021/12/13.
 * 私募自选动态消息数据模型
 */
@SuppressLint("ParcelCreator")
@Parcelize
data class SmOptionalHoldSortBody(

    val dataList: MutableList<Item>?, //数据列表

) : AbsNormalBody(), Parcelable {

    @Parcelize
    data class Item(
        val jjdm: String?,
        val top: String? //是否置顶 （1:是 0：否） --7.8.2 add
    ) : Parcelable

}
