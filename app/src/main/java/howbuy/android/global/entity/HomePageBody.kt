package howbuy.android.global.entity

import android.os.Parcelable

import com.howbuy.fund.net.entity.common.normal.AbsNormalBody
import com.howbuy.global.user.ad.AdItem
import howbuy.android.global.headlines.entity.HeadlinesItem
import kotlinx.android.parcel.Parcelize

/**
 * 首页数据
 * Created by tao.liang on 2022/5/7.
 */
@Parcelize
data class HomePageBody(
    //广告位 轮播图
    val bannerList: MutableList<AdItem>?,
    //主推功能区, 2.5 add 字段待服务端确认
    val iconList: MutableList<HomeIcon>?,
    val newbiesModuleTitle: String?,//新人专区标题-2.5.2 add
    val newbiesModuleList: MutableList<NewUserModuleItem>?,//新人专区, 2.5 add
    //储蓄罐签约模块 2.2.0 新增
    val cxgModule: CxgModule?,
    // 推荐模块
    val recommendModule: HomeRecomend?,
    //资讯模块
    val informationModule: InformationModule?

) : AbsNormalBody(), Parcelable {


    @Parcelize
    data class HomeIcon(
        val iconTitle: String?,//title
        val iconDesc: String?,//icon描述（副标题）
        val iconImgUrl: String?,//图片url
        val iconEvent: String?//事件\短命令
    ) : Parcelable

    @Parcelize
    data class NewUserModuleItem(
        val iconTitle: String?,//模块图片标题
        val iconImgUrl: String?,//模块图片地址
        val iconEvent: String?//跳转链接
    ) : Parcelable

    @Parcelize
    data class CxgModule(
        val img: String?,//模块图片地址
        val url: String?//跳转链接
    ) : Parcelable

    @Parcelize
    data class HomeRecomend(
        val bigTitle: String?,//大标题
        val moduleList: MutableList<HomeProduct>?//模块列表
    ) : Parcelable {


        @Parcelize
        data class HomeProduct(
            val title: String?,//主标题
            val subTitle: String?,//副标题
            val buttonName: String?,//按钮名称
            val buttonEvent: String?, //跳转链接
            val productList: MutableList<Prod>?//推荐产品列表
        ) : Parcelable {

            @Parcelize
            data class Prod(
                val fundCode: String?,//基金代码
                val shortName: String?,//基金简称
                val piType: String?,//piType PI产品类型(1:非PI产品(海外公募基金（香港）) 2:PI产品)//20240425
                val eventUrl: String?,//点击跳转地址
                val recommendReason: String?,//推荐理由
                val productLabel: String?,//产品标签,多个标签间用&分割
                val defaultLabel: String?,//配置标签,多个标签间用&分割
                val isZdyhb: String?, // 是否自定义收益(1:是 0:否)
                val qjhb: String?,//基金区间收益
                val hbDesc: String?,//基金区间收益描述
                val isNew: String?,//是否新品首发 (1:是 0:否)
                val riskLevel: String?// 产品风险等级（1-5）
            ) : Parcelable

        }
    }

    @Parcelize
    data class InformationModule(
        val title: String?,//大标题
        val buttonJumpUrl: String?,//点击跳转地址
        val newsList: MutableList<HeadlinesItem>?//模块列表
    ) : Parcelable
}
