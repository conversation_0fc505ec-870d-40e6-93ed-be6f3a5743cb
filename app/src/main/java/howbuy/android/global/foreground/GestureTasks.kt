package howbuy.android.global.foreground

import androidx.core.util.Consumer

/**
 * 手势指纹的任务封装, priority=1:
 * @param gestureAction 手势指纹的实际业务逻辑，在执行完毕后需要回调actionDone
 */
class GestureTasks(private var gestureAction: Consumer<Runnable>?) : ForegroundTask {
    override fun priority(): Int  = 1 //默认手势的优先级较为靠前

    override fun name(): String = "GestureTasks"

    override fun execute(actionDone: Runnable) {
        gestureAction?.accept(actionDone)
        gestureAction = null
    }

    override fun onCancel() {
        if (null != gestureAction) {
            gestureAction = null
        }
    }
}