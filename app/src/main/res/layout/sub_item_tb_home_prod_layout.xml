<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="@dimen/margin_12"
    android:paddingBottom="10dp">


    <TextView
        android:id="@+id/tv_sm_home_prod_jjjc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="4dp"
        android:fontFamily="sans-serif-medium"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="#4b4e61"
        android:textSize="16sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="新方程大类配置二号新大" />


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_sm_home_prod_jjjc"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="5dp"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.5"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/margin_5"
                        android:gravity="center_vertical"
                        android:minHeight="50dp"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tv_sm_home_prod_income"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:lines="1"
                            android:minHeight="27dp"
                            android:fontFamily="sans-serif-medium"
                            android:textColor="#e94444"
                            android:textSize="20sp"
                            app:autoSizeMaxTextSize="20sp"
                            app:autoSizeMinTextSize="12sp"
                            app:autoSizeTextType="uniform"
                            tools:text="5.54%" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tv_sm_home_prod_income_desc"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:ellipsize="end"
                            android:gravity="center_vertical"
                            android:lines="1"
                            android:minHeight="@dimen/margin_18"
                            android:textColor="#bbbdcb"
                            android:textSize="13sp"
                            app:autoSizeMaxTextSize="13sp"
                            app:autoSizeMinTextSize="9sp"
                            app:autoSizeTextType="uniform"
                            tools:text="近一年人人人人人人人人" />

                    </LinearLayout>

                </LinearLayout>

            </FrameLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingLeft="@dimen/margin_15">

                <TextView
                    android:id="@+id/tv_sm_home_prod_recom_reason"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:minHeight="27dp"
                    android:textColor="#2a3050"
                    android:textSize="15sp"
                    tools:text="推荐理由:低波动量化策略，稀缺机会" />

                <FrameLayout
                    android:id="@+id/lay_flow_labels"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="2dp"
                    android:gravity="center_vertical"
                    android:minHeight="@dimen/margin_18">

                    <com.howbuy.fund.base.widget.tag.TagLayout
                        android:id="@+id/flow_labels"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:gravity="center_vertical"
                        android:overScrollMode="never"
                        android:scrollbars="none"
                        app:lineMargin="0dp"
                        app:lines="1"
                        app:tagMargin="1dp" />
                </FrameLayout>


            </LinearLayout>


        </LinearLayout>

        <ImageView
            android:id="@+id/iv_prod_mask"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:background="@color/white"
            android:scaleType="fitXY"
            android:visibility="gone"
            tools:src="@mipmap/bg_hg_risk" />


    </FrameLayout>


</RelativeLayout>