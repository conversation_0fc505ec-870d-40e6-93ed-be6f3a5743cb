<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layTabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="15dp"
            android:paddingTop="15dp"
            android:paddingRight="15dp"
            android:paddingBottom="5dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tabBuy"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:gravity="center"
                android:text="购买开放"
                android:textColor="#ff262626"
                android:textSize="16sp"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@id/tabSell"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tabSell"
                android:layout_width="0dp"
                android:layout_height="32dp"
                android:gravity="center"
                android:text="赎回开放"
                android:textColor="#ff262626"
                android:textSize="16sp"
                app:layout_constraintLeft_toRightOf="@id/tabBuy"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="#EFF0F4" />

            <View
                android:id="@+id/strokeV"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/sm_stroke_r7_cdd3e8_05dp"
                app:layout_constraintBottom_toBottomOf="@id/tabBuy"
                app:layout_constraintLeft_toLeftOf="@id/tabBuy"
                app:layout_constraintRight_toRightOf="@id/tabSell"
                app:layout_constraintTop_toTopOf="@id/tabBuy" />

            <View
                android:layout_width="0.5dp"
                android:layout_height="0dp"
                android:background="#CDD3E8"
                app:layout_constraintBottom_toBottomOf="@id/tabBuy"
                app:layout_constraintLeft_toLeftOf="@id/tabBuy"
                app:layout_constraintRight_toRightOf="@id/tabSell"
                app:layout_constraintTop_toTopOf="@id/tabBuy" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/pager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="90dp"
        android:visibility="gone"
        tools:background="@color/white"
        tools:visibility="visible">
        <!--92dp的顶部间距，是因为空试图需要显示顶部的名称和Tab栏，高度合计92dp-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawableTop="@mipmap/gh_jjda_empty_img"
            android:drawablePadding="10dp"
            android:gravity="center"
            android:text="暂无数据"
            android:textColor="#ffc4c4c4"
            android:textSize="17sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>
</FrameLayout>