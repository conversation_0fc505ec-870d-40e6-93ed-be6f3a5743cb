<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:paddingStart="10dp"
    android:paddingEnd="10dp">

    <LinearLayout
        android:id="@+id/lay_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/lay_basic_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_corner8_white"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:drawablePadding="8dp"
                android:fontFamily="sans-serif-medium"
                android:gravity="center_vertical"
                android:text="基本信息"
                android:textColor="#ff333333"
                android:textSize="18sp"
                app:drawableStartCompat="@drawable/ic_fund_details_title" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_basic_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="1"
                tools:listitem="@layout/item_fund_details_basic_info" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lay_product_material"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/bg_corner8_white"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_weight="1"
                    android:drawablePadding="8dp"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center_vertical"
                    android:text="产品材料"
                    android:textColor="#ff333333"
                    android:textSize="18sp"
                    app:drawableStartCompat="@drawable/ic_fund_details_title" />

                <TextView
                    android:id="@+id/tv_see_pdf"
                    android:layout_width="wrap_content"
                    android:layout_height="44dp"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:paddingEnd="10dp"
                    android:text="查看已下载的文件"
                    android:textColor="@color/cl_5a6cb4"
                    android:textSize="14sp"
                    app:drawableLeftCompat="@mipmap/sm_icon_report" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_product_material"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="3"
                tools:listitem="@layout/item_fund_details_product_material" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_trade_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_corner8_white"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="44dp"
                android:layout_weight="1"
                android:drawablePadding="8dp"
                android:fontFamily="sans-serif-medium"
                android:gravity="center_vertical"
                android:text="交易须知"
                android:textColor="#ff333333"
                android:textSize="18sp"
                app:drawableStartCompat="@drawable/ic_fund_details_title" />

            <TextView
                android:id="@+id/tv_more_trade"
                android:layout_width="wrap_content"
                android:layout_height="44dp"
                android:gravity="center"
                android:visibility="gone"
                android:paddingEnd="10dp"
                android:text="更多交易须知>"
                android:textColor="#ff5a6cb4"
                android:textSize="14sp" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_trade_buy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_fund_details_trade_info" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_trade_operate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_fund_details_trade_info" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_trade_sale"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_fund_details_trade_info" />
    </LinearLayout>

</LinearLayout>