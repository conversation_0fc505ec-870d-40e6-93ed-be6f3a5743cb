<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/bg"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@drawable/bg_top_fund_details2"
        android:visibility="gone" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/lay_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <com.howbuy.component.widgets.VerticalNestScrollView
            android:id="@+id/scv_fund_main"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:scrollbars="none"
            android:visibility="gone"
            app:layout_behavior="@string/appbar_scrolling_view_behavior"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <FrameLayout
                    android:id="@+id/lay_fund_import"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:layout="@layout/frag_archive_import_info" />

                <!-- 业绩表现Tab: 包含业绩走势、风险指标 -->
                <LinearLayout
                    android:id="@+id/lay_fund_yjbx"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <!-- 业绩走势图模块 -->
                    <FrameLayout
                        android:id="@+id/lay_fund_yjzs"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:layout="@layout/frag_fund_chart_container" />

                    <!-- 风险指标 -->
                    <FrameLayout
                        android:id="@+id/lay_fund_risk"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp" />
                </LinearLayout>

                <FrameLayout
                    android:id="@+id/lay_fund_tab_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <FrameLayout
                    android:id="@+id/lay_fund_tab_news"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <!--底部免责声明-->
                <howbuy.android.global.widgets.SmDisclaimerFooterView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <View
                    android:id="@+id/v_space"
                    android:layout_width="0dp"
                    android:layout_height="60dp" />

            </LinearLayout>
        </com.howbuy.component.widgets.VerticalNestScrollView>

        <!--底部折叠模块,空白浮层,点击,滑动 消失-->
        <View
            android:id="@+id/drawer_bg_bottom_alert"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />
        <!--底部折叠模块-->
        <include
            android:id="@+id/lay_bottom_alert"
            layout="@layout/fund_details_bottom_alert"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:behavior_hideable="false"
            app:behavior_peekHeight="60dp"
            app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior"
            />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- 悬浮tab -->
    <LinearLayout
        android:id="@+id/lay_tab"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:alpha="0"
        android:background="#FFF1F1"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/fd_vertical_line" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcv_fund_tab"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:orientation="horizontal"
            android:paddingBottom="5dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="5"
            tools:listitem="@layout/item_fund_details_tab"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/trade_sub_tab"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:paddingBottom="5dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:layout_marginTop="-5dp"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3"
            tools:listitem="@layout/item_trade_sub_tab"
            tools:visibility="visible" />
    </LinearLayout>

    <!-- 底部按钮-包含平替产品-->
    <LinearLayout
        android:id="@+id/lay_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <FrameLayout
            android:id="@+id/lay_replace_product"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:layout_marginBottom="5dp"
            android:background="@drawable/bg_fund_details_replace_product"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:id="@+id/lay_prod"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="8dp"
                android:orientation="vertical"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:id="@+id/tv_replace_product_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="替代性产品提供"
                    android:layout_marginRight="5dp"
                    android:textColor="#ff6b6b6b"
                    android:textSize="13sp" />

                <TextView
                    android:id="@+id/tv_replace_product_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="10dp"
                    android:paddingTop="3dp"
                    android:fontFamily="sans-serif-medium"
                    android:textColor="#ff000000"
                    android:textSize="16sp"
                    tools:text="博时美元货币市场基金博时美元货币市场基金博时美元货币市场基金博时美元货币市场基金" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_replace_product_close"
                android:layout_width="23dp"
                android:layout_height="23dp"
                android:layout_gravity="end"
                android:paddingTop="7dp"
                android:paddingEnd="7dp"
                android:scaleType="center"
                android:src="@mipmap/ic_close_gray"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_buy_offline_hint"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#FFCCCC"
            android:gravity="center"
            android:padding="2.5dp"
            android:text="当前基金仅支持线下交易"
            android:textColor="#ffc51d25"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/lay_buy_btn"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@drawable/fd_bg_item_top_no_pressed"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingEnd="16dp">

            <LinearLayout
                android:id="@+id/lay_optional"
                android:layout_width="68dp"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_optional_flag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/icon_btn_optional" />

                <TextView
                    android:id="@+id/tv_optional_flag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingTop="2dp"
                    android:text="自选"
                    android:textColor="#666666"
                    android:textSize="12sp" />

            </LinearLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false">

                <com.howbuy.fund.base.widget.BubbleView
                    android:id="@+id/btn_buy"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_gravity="center"
                    android:clickable="false"
                    app:bubbleColor="#C51D25"
                    app:bubbleElevation="5dp"
                    app:bubbleIndicatorHeight="0dp"
                    app:bubbleRadius="20dp"
                    app:bubbleShadowColor="#33BF0000"
                    app:bubbleStrokeWidth="0dp">

                    <TextView
                        android:id="@+id/tv_btn"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center"
                        android:text="加载中..."
                        android:textColor="#ffffffff"
                        android:textSize="16sp" />
                </com.howbuy.fund.base.widget.BubbleView>

                <ImageView
                    android:id="@+id/ic_service"
                    android:layout_width="42dp"
                    android:layout_height="42dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginTop="-1dp"
                    android:src="@mipmap/icon_home_service4"
                    android:visibility="gone"
                    tools:visibility="visible" />
            </FrameLayout>
        </LinearLayout>
    </LinearLayout>

    <!--产品状态校验样式-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_prod_valid"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:clickable="true"
        android:visibility="gone"
        tools:visibility="gone">

        <!--如果是风险等级提示, 隐藏该占位-->
        <View
            android:id="@+id/iv_prod_valid_risk_space"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_prod_valid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_prod_valid_risk_space"
            app:layout_goneMarginTop="40dp"
            tools:src="@drawable/ic_fund_details_step2" />

        <TextView
            android:id="@+id/tv_prod_valie_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="#ff333333"
            android:textSize="19sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_prod_valid"
            tools:text="本产品仅支持专业投资者查看" />

        <!--如果是风险等级提示, 显示-->
        <TextView
            android:id="@+id/tv_prod_valie_riks_hint"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="38dp"
            android:layout_marginTop="13dp"
            android:layout_marginRight="38dp"
            android:gravity="center"
            android:textColor="#ff666666"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_prod_valie_title"
            tools:text="本产品的风险等级为“高风险(R5)”，您的风险承受能力等级为“平稳型(C3)”。本产品已超出了您的风险承受能力。"
            tools:visibility="gone" />

        <!--如果是风险等级提示, 显示-->
        <TextView
            android:id="@+id/tv_prod_valie_riks_know"
            android:layout_width="220dp"
            android:layout_height="40dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/bg_stroke_797979_r25"
            android:gravity="center"
            android:text="我知道了"
            android:textColor="#ff333333"
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_prod_valie_riks_hint"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/tv_prod_valie_contact_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="如有疑问，请联系您的专属服务人员或咨询客服"
            android:textColor="#ff666666"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_prod_valie_riks_know"
            app:layout_goneMarginTop="10dp" />

        <TextView
            android:id="@+id/tv_prod_valie_contact_tel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="5dp"
            android:textColor="#546ED4"
            android:textSize="14sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_prod_valie_contact_content"
            app:layout_goneMarginTop="10dp"
            tools:text="+852-37258088。" />

        <!--如果是风险等级提示, 隐藏-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/lay_prod_valid_step"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:background="@drawable/bg_f7f8fb_r10"
            android:orientation="vertical"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_prod_valie_contact_tel">


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:src="@drawable/gh_jjxq_icon_pibg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <TextView
                android:id="@+id/tv_prod_valid_step_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:textColor="#ff999999"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="*您可以完成开户并认证为专业投资者。"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_prod_valid_step_titile"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="30dp"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:textColor="#ff333333"
                android:textSize="16sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_prod_valid_step_subtitle"
                app:layout_goneMarginTop="30dp"
                tools:text="若您是专业投资者，可完成认证后再查看。" />


            <TextView
                android:id="@+id/tv_prod_valid_step_submit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/gh_jjxq_but_red"
                android:gravity="center"
                android:paddingBottom="3dp"
                android:text="去修改"
                android:textColor="#ffffffff"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_prod_valid_step_titile" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        仅在【开户进度=01-去开户/02-继续开户/04-修改开户资料】时展示，否则隐藏-->
        <TextView
            android:id="@+id/tv_prod_valid_step_hint"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="17dp"
            android:layout_marginRight="24dp"
            android:lineSpacingExtra="4dp"
            android:text="注：专业投资者认证，需上传资产证明文件。资产证明文件需包含发出的机构、本人姓名、一年内的发出日期、总值超过800万港元或等值外币的投资组合或流动资产(注明币种)。"
            android:textColor="#ff999999"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lay_prod_valid_step"
            tools:visibility="visible" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 不披露 -->
    <LinearLayout
        android:id="@+id/lay_bpl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingTop="120dp"
        android:visibility="gone"
        tools:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/bpl_img" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="26dp"
            android:gravity="center"
            android:text="本基金暂不披露!"
            android:textColor="#000000"
            android:textSize="20sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_10"
            android:gravity="center"
            android:text="无法查看详细信息，详情可咨询"
            android:textColor="#888888"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_bpl_phone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_10"
            android:gravity="center"
            android:minHeight="@dimen/margin_35"
            android:textColor="#6573AB"
            android:textSize="19sp"
            tools:text="+852-37258088" />
    </LinearLayout>
</FrameLayout>