<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="15dp"
    android:layout_marginEnd="15dp"
    android:layout_marginBottom="15dp"
    android:background="@drawable/bg_message_item"
    android:orientation="vertical"
    android:paddingBottom="12dp">

    <LinearLayout
        android:id="@+id/top_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingTop="15dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            app:lineHeight="25dp"
            android:fontFamily="sans-serif-medium"
            android:textColor="#ff2a3050"
            android:textSize="18sp"
            tools:text="海外储蓄罐底层基金更换通知海外储蓄罐底层基金更换通知海外储蓄罐底层基金更换通知" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-5dp"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:lineSpacingMultiplier="1.2"
        android:ellipsize="end"
        android:maxLines="5"
        android:textColor="#ff2a3050"
        android:textSize="14sp"
        tools:text="尊敬的客户，海外储蓄罐将于2024年5月10日进行底层基金变更，请您根据自身意愿选择是否同意变更。若逾期好买香港仍未收到阁下同意变更的回复，阁下的储蓄罐服务将会自动终止，如需使用该服务需重新申请开通。请尽快处理 >>" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:background="#e3e3e3" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#ff666666"
            android:textSize="14sp"
            tools:text="1月2日" />

        <TextView
            android:id="@+id/btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_corner15_e94444"
            android:fontFamily="sans-serif-medium"
            android:paddingStart="15dp"
            android:paddingTop="7.5dp"
            android:paddingEnd="15dp"
            android:paddingBottom="7.5dp"
            android:text="立刻查看"
            android:textColor="#ffffff"
            android:textSize="13sp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ic_arrow"
            android:layout_width="wrap_content"
            android:layout_height="13dp"
            android:src="@drawable/more_right" />
    </LinearLayout>
</LinearLayout>