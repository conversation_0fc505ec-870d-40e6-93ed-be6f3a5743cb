<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="15dp"
    android:layout_marginEnd="15dp"
    android:layout_marginBottom="15dp"
    android:background="@drawable/bg_message_item"
    android:orientation="vertical"
    android:padding="15dp">

    <!-- 标题和日期 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:layout_marginBottom="15dp">

        <!-- 红点 -->
        <View
            android:id="@+id/red_dot"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_circle_red"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="sans-serif-medium"
            android:textColor="#ff2a3050"
            android:textSize="16sp"
            tools:text="基金交易提醒通知" />

        <TextView
            android:id="@+id/tv_message_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#ff999999"
            android:textSize="14sp"
            tools:text="2025年4月8日" />

    </LinearLayout>

    <!-- 交易信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="10dp">

        <!-- 产品名称 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="产品名称："
                android:textColor="#ff666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_product_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#ff2a3050"
                android:textSize="14sp"
                tools:text="海外公募基金1" />

        </LinearLayout>

        <!-- 交易类型 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="交易类型："
                android:textColor="#ff666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_trade_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#ff2a3050"
                android:textSize="14sp"
                tools:text="赎回" />

        </LinearLayout>

        <!-- 交易时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="交易时间："
                android:textColor="#ff666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_trade_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#ff2a3050"
                android:textSize="14sp"
                tools:text="2025年3月10日" />

        </LinearLayout>

        <!-- 交易状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="交易状态："
                android:textColor="#ff666666"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_trade_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="#ff2a3050"
                android:textSize="14sp"
                tools:text="确认成功" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>