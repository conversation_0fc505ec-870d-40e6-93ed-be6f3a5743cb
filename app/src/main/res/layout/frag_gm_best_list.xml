<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="53dp"
            android:background="@drawable/bg_gm_best_top"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="138dp"
            android:background="@drawable/bg_gm_best_bottom"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.howbuy.component.widgets.CanScrollRecyclerView
            android:id="@+id/rcv_tab"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_sm_best_sub_tab" />

        <com.howbuy.hbrefresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlAccentColor="@android:color/white"
            app:srlDisableContentWhenRefresh="true"
            app:srlEnableAutoLoadmore="false"
            app:srlEnableLoadmore="false"
            app:srlEnableOverScrollBounce="false"
            app:srlEnablePreviewInEditMode="false"
            app:srlEnableRefresh="false"
            app:srlPrimaryColor="@color/fd_window_bg">

            <howbuy.android.global.widgets.HbRefreshHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_mk"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:itemCount="2"
                tools:listitem="@layout/gm_best_mk_list_item_layout" />

        </com.howbuy.hbrefresh.layout.SmartRefreshLayout>
    </LinearLayout>
</FrameLayout>