<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/start_margin"
        android:layout_width="20dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_tab"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_tab" />

    <TextView
        android:id="@id/tv_tab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/cl_2a3050"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/indicator"
        app:layout_constraintStart_toEndOf="@+id/start_margin"
        tools:text="你好" />

    <View
        android:id="@+id/end_margin"
        android:layout_width="20dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_tab"
        app:layout_constraintStart_toEndOf="@+id/tv_tab"
        app:layout_constraintTop_toTopOf="@+id/tv_tab" />

    <ImageView
        android:id="@+id/red_dot"
        android:layout_width="5dp"
        android:layout_height="5dp"
        android:background="@drawable/bg_circle_red"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/tv_tab"
        app:layout_constraintTop_toTopOf="@+id/tv_tab" />

    <TextView
        android:id="@+id/tv_unread_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_unread_msg"
        android:paddingStart="3dp"
        android:minWidth="15dp"
        android:paddingEnd="3dp"
        android:gravity="center"
        android:textColor="#ffffffff"
        android:textSize="9sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/tv_tab"
        app:layout_constraintTop_toTopOf="@+id/tv_tab" />

    <View
        android:id="@+id/indicator"
        android:layout_width="16dp"
        android:layout_height="3dp"
        android:background="@drawable/tab_indicator_red"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/tv_tab"
        app:layout_constraintStart_toStartOf="@+id/tv_tab" />
</androidx.constraintlayout.widget.ConstraintLayout>