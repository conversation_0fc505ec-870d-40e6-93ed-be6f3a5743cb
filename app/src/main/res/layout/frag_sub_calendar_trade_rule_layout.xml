<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/lay_trade_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_corner8_white"
        android:orientation="vertical"
        tools:visibility="visible">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_trade_buy"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="3dp"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_fund_details_trade_info" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_trade_operate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="3dp"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_fund_details_trade_info" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_trade_sale"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="3dp"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="1"
            tools:listitem="@layout/item_fund_details_trade_info" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/empty"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="90dp"
        android:visibility="gone"
        tools:background="@color/transparent"
        tools:visibility="gone">
        <!--92dp的顶部间距，是因为空试图需要显示顶部的名称和Tab栏，高度合计92dp-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:drawableTop="@mipmap/gh_jjda_empty_img"
            android:drawablePadding="10dp"
            android:gravity="center"
            android:text="暂无数据"
            android:textColor="#ffc4c4c4"
            android:textSize="17sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>

</FrameLayout>