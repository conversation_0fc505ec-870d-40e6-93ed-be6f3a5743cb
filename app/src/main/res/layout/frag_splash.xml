<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <howbuy.android.global.entry.EntryTouchImageView
        android:id="@+id/iv_full_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <TextView
        android:id="@+id/btn_skip"
        android:onClick="onXmlBtClick"
        android:layout_width="95dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:layout_marginRight="20dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:textSize="18sp"
        android:visibility="gone"
        tools:visibility="visible"
        android:textAllCaps="false"
        tools:text="跳过(3s)"
        android:gravity="center"
        android:textColor="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/fd_btn_transparent_bg_selector" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/buttonPositionGuide"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.84"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/animImgScroll"
        android:layout_marginBottom="5dp"
        app:layout_constraintBottom_toTopOf="@id/btnEntry"
        app:layout_constraintLeft_toLeftOf="@id/btnEntry"
        app:layout_constraintRight_toRightOf="@id/btnEntry"
        android:layout_width="25dp"
        android:layout_height="32dp"/>
    <TextView
        android:id="@+id/btnEntry"
        android:text="上滑或点击查看详情>"
        android:gravity="center"
        android:textSize="16dp"
        android:textColor="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/main_splash_btn_entry"
        app:layout_constraintBottom_toTopOf="@id/buttonPositionGuide"
        android:layout_width="230dp"
        android:layout_height="37dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>