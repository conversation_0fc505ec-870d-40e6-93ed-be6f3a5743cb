<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/lay_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@mipmap/img_ys_pupop"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="43dp"
        android:paddingEnd="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_hi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/cl_2a3050"
            android:textSize="25sp"
            android:fontFamily="sans-serif-medium"
            android:text="Hi，尊敬的用户" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2.5dp"
            android:text="感谢您信任并选择好买全球！"
            android:textColor="@color/cl_2a3050"
            android:textSize="20sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="5dp"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:textColor="@color/cl_878ba5"
        android:textSize="14sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lay_header" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="20dp"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/tv_sure"
        app:layout_constraintTop_toBottomOf="@+id/tv_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="20dp"
            android:paddingEnd="20dp">

            <TextView
                android:id="@+id/tv_sub_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.1"
                android:text="@string/privite_sub_content"
                android:textColor="@color/cl_bbbdcb" />
        </LinearLayout>
    </ScrollView>

    <com.howbuy.fund.base.widget.BubbleView
        android:id="@+id/tv_sure"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="20dp"
        app:bubbleShadowColor="#594E76D6"
        app:bubbleColor="#C51D25"
        app:bubbleRadius="10dp"
        app:bubbleElevation="5dp"
        app:bubbleIndicatorHeight="0dp"
        app:layout_constraintBottom_toTopOf="@+id/tv_cancel"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center"
            android:text="了解并同意"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </com.howbuy.fund.base.widget.BubbleView>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="25dp"
        android:text="@string/private_cancel_txt"
        android:textColor="@color/cl_878ba5"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>