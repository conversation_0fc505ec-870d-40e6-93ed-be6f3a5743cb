<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:gravity="center_vertical"
    tools:layout_gravity="right"
    android:layout_height="34dp">

    <ImageView
        android:id="@+id/deleteIcon"
        android:src="@mipmap/sm_btn_optional_icon_del"
        android:layout_gravity="center_vertical"
        android:layout_width="26dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:padding="5dp"
        android:alpha="0.4"
        android:layout_height="26dp"/>

    <TextView
        android:id="@+id/columnName"
        android:textColor="@color/white"
        android:textSize="16dp"
        tools:text="近一年涨幅"
        android:paddingRight="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/deleteIcon"
        app:layout_constraintRight_toLeftOf="@+id/positionIcon"
        android:paddingLeft="2dp"
        android:singleLine="true"
        android:ellipsize="end"
        android:layout_width="0dp"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/positionIcon"
        android:src="@mipmap/sm_btn_optional_icon_map"
        android:layout_gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/dragSortIcon"
        android:layout_width="40dp"
        android:layout_height="wrap_content"/>

    <ImageView
        android:id="@+id/dragSortIcon"
        android:src="@mipmap/sm_btn_optional_icon_sort"
        android:alpha="0.4"
        android:paddingLeft="15dp"
        android:paddingRight="15dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_gravity="center_vertical"
        android:layout_width="46dp"
        android:layout_height="match_parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>