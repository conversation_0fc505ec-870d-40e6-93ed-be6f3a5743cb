<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    tools:viewBindingIgnore="true"
    android:layout_height="wrap_content"
    android:background="#000000"
    android:orientation="vertical">

    <com.howbuy.fund.common.floatwindow.DragViewGroup
        android:id="@+id/dvg_video"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="true"
        android:minWidth="175dp"
        android:minHeight="98dp">

        <com.tencent.rtmp.ui.TXCloudVideoView
            android:id="@+id/tx_video_view"
            android:layout_width="175dp"
            android:layout_height="98dp" />

    </com.howbuy.fund.common.floatwindow.DragViewGroup>

    <ProgressBar
        android:id="@+id/pb_video"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center" />

    <LinearLayout
        android:id="@+id/lay_float_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_gradient_b3000000_to_ffffff_bottom"
        android:minHeight="32dp"
        android:orientation="horizontal"
        android:paddingRight="10dp">

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:paddingLeft="@dimen/margin_12"
            android:paddingTop="@dimen/margin_5"
            android:paddingRight="10dp"
            android:paddingBottom="@dimen/margin_5"
            android:src="@mipmap/ic_vhall_pause" />

        <SeekBar
            android:id="@+id/seek_video_player"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:clickable="true"
            android:max="100"
            android:maxHeight="3dp"
            android:minHeight="3dp"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:progressDrawable="@drawable/sm_group_seekbar_enable_bg"
            android:thumb="@null"
            android:visibility="visible"
            tools:progress="50" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/lay_float_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_gradient_a8000000_to_ffffff_top"
        android:minHeight="40dp">

        <ImageView
            android:id="@+id/iv_video_fullscreen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/ic_vhall_back" />

        <ImageView
            android:id="@+id/iv_video_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:clickable="true"
            android:padding="@dimen/dp_10"
            android:src="@mipmap/sm_icon_video_close_small" />
    </FrameLayout>
</FrameLayout>