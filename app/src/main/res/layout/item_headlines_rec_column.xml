<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">
    <!-- 这里多一层FrameLayout是因为当列表只有一个的时候需要对Item设置宽度 -->
    <FrameLayout
        android:layout_width="217dp"
        android:layout_height="100dp"
        android:background="@drawable/bg_corner8_white">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:src="@mipmap/sm_zx_image_zlbg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="10dp">

            <com.howbuy.fund.base.widget.FontTextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="top"
                android:maxLines="1"
                android:text="私募日报"
                android:textColor="#333335"
                android:textSize="20sp"
                app:fontType="YouSheBiaoTiHei" />

            <com.howbuy.component.widgets.ClipImageView
                android:id="@+id/iv"
                android:layout_width="196dp"
                android:layout_height="47dp"
                app:radius="5dp" />
        </LinearLayout>

    </FrameLayout>
</FrameLayout>