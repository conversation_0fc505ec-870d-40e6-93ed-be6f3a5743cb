<?xml version="1.0" encoding="utf-8"?>
<com.howbuy.global.common.NestedScrollCoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/lay_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/lay_collapse_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:statusBarScrim="@color/white">

            <LinearLayout
                android:id="@+id/lay_strategy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="12dp"
                android:layout_marginBottom="0dp"
                android:background="@mipmap/gh_hwyx_bg_tzjy"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="10dp"
                android:paddingTop="7dp"
                android:paddingEnd="10dp"
                android:paddingBottom="7dp">

                <ImageView
                    android:id="@+id/tv_strategy_title"
                    android:layout_width="37dp"
                    android:layout_height="37dp"
                    android:layout_marginEnd="10dp" />

                <TextView
                    android:id="@+id/tv_strategy_advice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="#ff333333"
                    android:textSize="14sp"
                    tools:text="投资性价比显著，2024年趋势性行情正待开启。" />

            </LinearLayout>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <com.howbuy.component.widgets.CanScrollRecyclerView
            android:id="@+id/rcv_sub_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_sm_best_sub_tab" />
    </com.google.android.material.appbar.AppBarLayout>

    <com.howbuy.hbrefresh.layout.SmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:srlAccentColor="@android:color/white"
        app:srlDisableContentWhenRefresh="true"
        app:srlEnableAutoLoadmore="false"
        app:srlEnableLoadmore="false"
        app:srlEnableOverScrollBounce="false"
        app:srlEnablePreviewInEditMode="false"
        app:srlEnableRefresh="false"
        app:srlPrimaryColor="@color/fd_window_bg">

        <howbuy.android.global.widgets.HbRefreshHeader
            android:id="@+id/header_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:itemCount="2"
            tools:listitem="@layout/item_sm_best_product" />

    </com.howbuy.hbrefresh.layout.SmartRefreshLayout>
</com.howbuy.global.common.NestedScrollCoordinatorLayout>