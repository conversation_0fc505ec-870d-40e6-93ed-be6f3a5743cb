<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:paddingBottom="10dp">

    <ImageView
        android:id="@+id/iv_shotscreen_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/margin_20"
        android:paddingTop="17dp"
        android:paddingRight="@dimen/margin_20"
        android:paddingBottom="@dimen/margin_20">

        <ImageView
            android:id="@+id/iv_person_head"
            android:layout_width="65dp"
            android:layout_height="65dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitXY"
            android:visibility="gone"
            android:layout_marginRight="15dp"
            tools:visibility="gone"
            android:src="@mipmap/sm_person_head" />

        <ImageView
            android:id="@+id/iv_screenshot_qrcode"
            android:layout_width="110dp"
            android:layout_height="110dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitXY"
            android:visibility="visible"
            tools:visibility="visible"
            android:layout_marginRight="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_screenshot_username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/cl_262626"
                android:textSize="20sp"
                android:textStyle="bold"
                android:text="好买全球" />

            <TextView
                android:id="@+id/tv_screenshot_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="邀您观看直播"
                android:textColor="@color/cl_262626"
                android:textSize="17sp" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>