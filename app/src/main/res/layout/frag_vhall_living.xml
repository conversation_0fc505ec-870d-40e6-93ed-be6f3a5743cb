<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <FrameLayout
        android:id="@+id/lay_video_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true" />

<!--    <TextView-->
<!--        android:id="@+id/tv_last_play_pos_hint"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_gravity="center"-->
<!--        android:background="@drawable/bg_000000_solid_10"-->
<!--        android:paddingLeft="15dp"-->
<!--        android:paddingTop="5dp"-->
<!--        android:paddingRight="15dp"-->
<!--        android:paddingBottom="5dp"-->
<!--        android:text="已为您定位上次观看位置"-->
<!--        android:textColor="#ffffffff"-->
<!--        android:textSize="14sp"-->
<!--        android:visibility="gone"-->
<!--        app:layout_constraintBottom_toBottomOf="parent"-->
<!--        app:layout_constraintLeft_toLeftOf="parent"-->
<!--        app:layout_constraintRight_toRightOf="parent"-->
<!--        app:layout_constraintTop_toTopOf="parent"-->
<!--        tools:visibility="visible" />-->


    <LinearLayout
        android:id="@+id/lay_audio"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="#3A3A3A"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="30dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_audio_img"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginEnd="8dp"
            android:src="@mipmap/sm_bk_pla_whi" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="音频播放中..."
            android:textColor="#ffffffff"
            android:textSize="15sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/lay_in_mic_float_btn_land"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|right"
        android:layout_marginRight="100dp"
        android:layout_marginBottom="@dimen/dp_10"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/iv_mic_enable_land"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginRight="30dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/sm_palyer_microing" />


        <ImageView
            android:id="@+id/iv_camera_enable_land"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginRight="30dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tv_mic_enable"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/sm_palyer_caming" />

        <ImageView
            android:id="@+id/iv_hangup_land"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:background="@mipmap/sm_palyer_han" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/container_land_chat"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_30"
        android:layout_marginBottom="50dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/lay_dlna"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#3A3A3A"
        android:clickable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="连接成功，投屏中…"
            android:textColor="#ffffffff"
            android:textSize="15sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_dlna_change"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_corner50_stroke_white"
                android:paddingStart="30dp"
                android:paddingTop="6dp"
                android:paddingEnd="30dp"
                android:paddingBottom="6dp"
                android:text="换设备"
                android:textColor="#ffffffff"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tv_dlna_stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:background="@drawable/bg_corner50_white"
                android:paddingStart="30dp"
                android:paddingTop="6dp"
                android:paddingEnd="30dp"
                android:paddingBottom="6dp"
                android:text="关闭投屏"
                android:textColor="#ff3a3a3a"
                android:textSize="13sp" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_top"
        android:layout_width="match_parent"
        android:layout_height="37dp"
        android:background="@drawable/bg_vhall_video_control_top"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="30dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_watch_num"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:lines="1"
            android:visibility="gone"
            android:layout_marginEnd="20dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            tools:visibility="visible"
            tools:text="123456人看过" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center_vertical"
            android:lines="1"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="invisible"
            tools:visibility="visible"
            tools:text="这是标题这是标题这是标题这是标题这是标题这是标题这是标题这是标题这是标题这是标题" />

        <ImageView
            android:id="@+id/iv_share"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="15dp"
            android:paddingEnd="7.5dp"
            android:src="@mipmap/ic_vhall_share" />

        <ToggleButton
            android:id="@+id/tb_full_chat"
            android:layout_width="65dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@android:color/transparent"
            android:checked="false"
            android:drawableEnd="@drawable/toggle_btn_vhall_full_chat"
            android:drawablePadding="0dp"
            android:paddingStart="7.5dp"
            android:paddingEnd="7.5dp"
            android:textColor="@color/white"
            android:textOff="互动"
            android:textOn="互动"
            android:textSize="12sp"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_menu"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingStart="7.5dp"
            android:paddingEnd="15dp"
            android:src="@mipmap/ic_vhall_menu" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="37dp"
        android:background="@drawable/bg_vhall_video_control_bottom"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_play"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="15dp"
            android:src="@mipmap/ic_vhall_play" />

        <TextView
            android:id="@+id/tv_play_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingEnd="3dp"
            android:text="00:00:00"
            android:layout_marginStart="9dp"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <View
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/iv_dlna_mic_change"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingEnd="19dp"
            android:src="@mipmap/ic_sm_vhall_dlna_2_mic"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_fullscreen"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingEnd="19dp"
            android:src="@mipmap/ic_vhall_fullscreen" />

        <TextView
            android:id="@+id/tv_quality"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_fff_stroke_2"
            android:paddingStart="14dp"
            android:paddingTop="4dp"
            android:paddingEnd="14dp"
            android:paddingBottom="4dp"
            android:textColor="#ffffffff"
            android:textSize="13sp"
            android:visibility="gone"
            tools:text="标清"
            tools:visibility="visible" />
    </LinearLayout>

    <include
        android:id="@+id/lay_error"
        layout="@layout/include_sm_vhall_error_layout"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="30dp"
        android:layout_height="37dp"
        android:paddingStart="14dp"
        android:paddingTop="6dp"
        android:paddingEnd="8dp"
        android:paddingBottom="6dp"
        android:src="@mipmap/ic_vhall_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/error_share"
        android:layout_width="wrap_content"
        android:layout_height="37dp"
        android:layout_gravity="end"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@mipmap/ic_vhall_share" />

    <ImageView
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/spinner" />
</androidx.constraintlayout.widget.ConstraintLayout>