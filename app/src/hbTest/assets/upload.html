<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择图片或拍照</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        h1 {
            text-align: center;
            margin-top: 50px;
        }
        p {
            text-align: center;
            margin-top: 20px;
        }
        button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        img {
            display: block;
            margin: 20px auto;
            max-width: 100%;
        }
    </style>
</head>
<body>
<h1>选择图片或拍照</h1>
<p>点击下面的按钮选择一张图片或者拍照。</p>
<div style="border-style:solid;border-width:0.5pt;border-color:gray">
    <textarea style="color:#FFFFFF;background-color:#000000;width:100%;height:100px" id="textareaout">信息输出</textarea>
</div>
<button id="choose">选择</button>
<button id="choosePicBtn">选择图片</button>
<button id="takePicBtn">拍照</button>
<button id="choosePdfBtn">选择PDF文件</button>
<img id="myPic" src="" alt="图片加载失败">
</body>
</html>

<script>
        function printout(msg) {
                document.getElementById('textareaout').value = msg;
                setTimeout(function() {alert(msg);}, 500);
                window.log("msg");
            }
         document.getElementById('choose').addEventListener('click', function() {
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*,application/pdf';
            input.capture = 'camera';
            input.multiple = true;
            input.onchange = function(e) {
                var file = e.target.files[0];
<!--                printout("照片地址："+URL.createObjectURL(file));-->
                var reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('myPic').src = e.target.result;
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });
        document.getElementById('choosePicBtn').addEventListener('click', function() {
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.multiple = true;
            input.onchange = function(e) {
                var file = e.target.files[0];
<!--                printout("照片地址："+URL.createObjectURL(file));-->
                var reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('myPic').src = e.target.result;
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });
        document.getElementById('takePicBtn').addEventListener('click', function() {
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'camera';
            input.onchange = function(e) {
                var file = e.target.files[0];
<!--                printout("拍照地址："+URL.createObjectURL(file));-->
                var reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('myPic').src = e.target.result;
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });
        document.getElementById('choosePdfBtn').addEventListener('click', function() {
            var input = document.createElement('input');
            input.type = 'file';
            input.accept = 'application/pdf';
            input.onchange = function(e) {
                var file = e.target.files[0];

                var reader = new FileReader();
                reader.onload = function(e) {
                    var pdfUrl = URL.createObjectURL(file);
                    printout("pdf文件地址："+pdfUrl);
                    window.open(pdfUrl);
                };
                reader.readAsDataURL(file);
            };
            input.click();
        });
    </script>
