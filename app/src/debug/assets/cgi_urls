{"urls": {"CRM_CGI_HKACCOUNT_CUST_APP_GETPERSONALCENTERINFO": {"path": "hkaccount/cust/app/getpersonalcenterinfo", "host": "h801"}, "CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEORIDNO": {"path": "hkaccount/login/loginappbymobileoridno", "host": "h801"}, "HK_V220_QUERYMESSAGESTAUTS": {"path": "/hk/v220/querymessagestauts.json", "ver": "false", "h5path": "/hk/v220/querymessagestauts.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_BALANCE_QUERY": {"path": "hkaccount/balance/query", "host": "h801"}, "HK_V260_NEWS": {"path": "/hk/v260/news.json", "ver": "false", "h5path": "/hk/v260/news.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_COMMON_CHECKHKAPPUPDATE": {"path": "hkaccount/common/checkhkappupdate", "encryption": "false", "host": "h801"}, "CRM_CGI_HKACCOUNT_COMMON_GETEMAILSUFFIXLIST": {"path": "hkaccount/common/getemailsuffixlist", "encryption": "false", "host": "h801"}, "HK_SEARCHDATA_NEW": {"path": "/hk_searchdata_new.do", "ver": "false", "h5path": "/hk_searchdata_new.do", "h5islocal": "false", "host": "h105"}, "MAIN_V754_CONFIGPARAMS": {"path": "/main/v754/cdn/setting/configparams.json", "ver": "false", "encryption": "false", "h5path": "/main/v754/cdn/setting/configparams.json", "h5islocal": "false", "host": "h101"}, "HK_V200_HKHBCHART": {"path": "/hk/v200/hkhbchart.json", "ver": "false", "h5path": "/hk/v200/hkhbchart.json", "h5islocal": "false", "host": "h106"}, "HK_V200_GETHKBUYSTATUS": {"path": "/hk/v200/gethkbuystatus.json", "ver": "false", "h5path": "/hk/v200/gethkbuystatus.json", "h5islocal": "false", "host": "h106"}, "HK_V200_HOTSALES": {"path": "/hk/v200/hotsales.json", "ver": "false", "h5path": "/hk/v200/hotsales.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_FUND_HOLD_QUERY_FUND_LIST": {"path": "fund/hold/queryfundlist", "host": "h801"}, "HK_V210_DISCUSSIONUP": {"path": "/hk/v210/discussionup.json", "ver": "false", "h5path": "/hk/v210/discussionup.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_LOGIN_APPLOGOUT": {"path": "hkaccount/login/applogout", "host": "h801"}, "CRM_CGI_HKACCOUNT_COMMON_SAVEASSETSTATUS": {"path": "hkaccount/common/saveassetstatus", "host": "h801"}, "HK_V210_STATISTICANALYSE": {"path": "/hk/v210/statisticanalyse.json", "ver": "false", "h5path": "/hk/v210/statisticanalyse.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_COMMON_APP_ADVERTISING": {"path": "hkaccount/common/app/advertising", "host": "h801"}, "HK_V220_MESSAGEWINDOW": {"path": "/hk/v220/messagewindow.json", "ver": "false", "h5path": "/hk/v220/messagewindow.json", "h5islocal": "false", "host": "h106"}, "HK_V200_HKHOMEPAGE": {"path": "/hk/v200/hkhomepage.json", "ver": "false", "h5path": "/hk/v200/hkhomepage.json", "h5islocal": "false", "host": "h106"}, "HK_V200_GETHKLSYJLIST": {"path": "/hk/v200/gethklsyjlist.json", "ver": "false", "h5path": "/hk/v200/gethklsyjlist.json", "h5islocal": "false", "host": "h106"}, "HK_V200_FUNDINFO": {"path": "/hk/v200/fundinfo.json", "ver": "false", "h5path": "/hk/v200/fundinfo.json", "h5islocal": "false", "host": "h106"}, "HK_V250_HKHCCHART": {"path": "/hk/v250/hkhcchart.json", "ver": "false", "h5path": "/hk/v250/hkhcchart.json", "h5islocal": "false", "host": "h106"}, "HK_V250_CHARTSHOWFLAG": {"path": "/hk/v250/chartshowflag.json", "ver": "false", "h5path": "/hk/v250/chartshowflag.json", "h5islocal": "false", "host": "h106"}, "HK_V250_PKRECOMMENDFUND": {"path": "/hk/v250/pkrecommendfund.json", "ver": "false", "h5path": "/hk/v250/pkrecommendfund.json", "h5islocal": "false", "host": "h106"}, "HK_PK_SEARCHFUND": {"path": "/hk_pk_searchfund.do", "ver": "false", "h5path": "/hk_pk_searchfund.do", "h5islocal": "false", "host": "h105"}, "HK_V250_PKCHARTHBCOMPARE": {"path": "/hk/v250/pkcharthbcompare.json", "ver": "false", "h5path": "/hk/v250/pkcharthbcompare.json", "h5islocal": "false", "host": "h106"}, "HK_V250_PKCHARTHCCOMPARE": {"path": "/hk/v250/pkcharthccompare.json", "ver": "false", "h5path": "/hk/v250/pkcharthccompare.json", "h5islocal": "false", "host": "h106"}, "HK_V210_COLUMNFUNCTIONALAREA": {"path": "/hk/v210/columnfunctionalarea.json", "ver": "false", "h5path": "/hk/v210/columnfunctionalarea.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_LOGIN_NEEDLOGINEXIT": {"path": "/hkaccount/login/needloginexit", "host": "h801"}, "CRM_CGI_HKACCOUNT_COMMON_GETCOUNTRY": {"path": "hkaccount/common/getcountry", "encryption": "false", "host": "h801"}, "TRADE_CGI_GATEWAY_SECURITYKEY": {"path": "securitykey.htm", "encryption": "false", "host": "h203"}, "HK_V200_BASE_GETANNOUNCEMENT": {"path": "/hk/v200/base/getannouncement.json", "ver": "false", "h5path": "/hk/v200/base/getannouncement.json", "h5islocal": "false", "host": "h106"}, "HK_V210_NEWSCOLUMN": {"path": "/hk/v210/newscolumn.json", "ver": "false", "h5path": "/hk/v210/newscolumn.json", "h5islocal": "false", "host": "h106"}, "HK_V260_HOTSEARCH": {"h5islocal": "false", "host": "h106", "path": "/hk/v260/hotsearch.json", "ver": "false", "h5path": "/hk/v260/hotsearch.json"}, "HK_V220_DEALSTATUS": {"path": "/hk/v220/dealstatus.json", "ver": "false", "h5path": "/hk/v220/dealstatus.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_VERIFYCODE_GETAPPLOGINMSGVERIFYCODE": {"path": "hkaccount/verifycode/getapploginmsgverifycode", "host": "h801"}, "HK_V210_DISCUSSIONLIST": {"path": "/hk/v210/discussionlist.json", "ver": "false", "h5path": "/hk/v210/discussionlist.json", "h5islocal": "false", "host": "h106"}, "HK_V200_GETHKLSJZLIST": {"path": "/hk/v200/gethklsjzlist.json", "ver": "false", "h5path": "/hk/v200/gethklsjzlist.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_COMMON_GETIDTYPELIST": {"path": "hkaccount/common/getidtypelist", "encryption": "false", "host": "h801"}, "CRM_CGI_HKACCOUNT_PASSWORD_LOGINVERIFY": {"path": "/hkaccount/password/loginpasswordverify", "host": "h801"}, "CRM_CGI_HKACCOUNT_LOGIN_LOGINAPPBYMOBILEANDVERIFYCODE": {"path": "hkaccount/login/loginappbymobileandverifycode", "host": "h801"}, "CRM_CGI_HKACCOUNT_BALANCE_FUNDTAB_QUERY": {"path": "hkaccount/balance/fundtab/query", "host": "h801"}, "CRM_CGI_HKACCOUNT_BALANCE_CASHTAB_QUERY": {"path": "hkaccount/balance/cashtab/query", "host": "h801"}, "HK_V200_GETHKBJJZLIST": {"path": "/hk/v200/gethkbjjzlist.json", "ver": "false", "h5path": "/hk/v200/gethkbjjzlist.json", "h5islocal": "false", "host": "h106"}, "HK_V210_NEWSINFO": {"path": "/hk/v210/newsinfo.json", "ver": "false", "h5path": "/hk/v210/newsinfo.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_PASSWORD_CHANGELOGINPASSWORD": {"path": "/hkaccount/password/changeloginpassword", "host": "h801"}, "CRM_CGI_HKACCOUNT_PASSWORD_SETLOGINPASSWORD": {"path": "/hkaccount/password/setloginpassword", "host": "h801"}, "HK_V200_ARCHIVEPAGEINFO": {"path": "/hk/v200/archivepageinfo.json", "ver": "false", "h5path": "/hk/v200/archivepageinfo.json", "h5islocal": "false", "host": "h106"}, "CRM_CGI_HKACCOUNT_LOGIN_LOGLIST": {"path": "/hkaccount/login/loginloglist", "host": "h801"}, "HK_V210_DISCUSSIONADD": {"path": "/hk/v210/discussionadd.json", "ver": "false", "h5path": "/hk/v210/discussionadd.json", "h5islocal": "false", "host": "h106"}, "HK_V210_NEWSRELATEDRECOMMEND": {"path": "/hk/v210/newsrelatedrecommend.json", "ver": "false", "h5path": "/hk/v210/newsrelatedrecommend.json", "h5islocal": "false", "host": "h106"}, "HK_V220_MESSAGELIST": {"path": "/hk/v220/messagelist.json", "ver": "false", "h5path": "/hk/v220/messagelist.json", "h5islocal": "false", "host": "h106"}, "HK_V200_BASE_GETBANNER": {"path": "/hk/v200/base/getbanner.json", "ver": "false", "h5path": "/hk/v200/base/getbanner.json", "h5islocal": "false", "host": "h106"}, "HK_V260_SEARCHNEWS": {"path": "/hk_v260_searchnews.do", "ver": "false", "h5path": "/hk_v260_searchnews.do", "h5islocal": "false", "host": "h105"}, "HK_V210_HOTARTICLES": {"path": "/hk/v210/hotarticles.json", "ver": "false", "h5path": "/hk/v210/hotarticles.json", "h5islocal": "false", "host": "h106"}, "MAIN_HK_CGIURLSCONFIG": {"path": "/main/hk/cgiurlsconfig.json", "ver": "false", "h5path": "/main/hk/cgiurlsconfig.json", "h5islocal": "false", "host": "h104"}, "CRM_CGI_HKACCOUNT_COMMON_APP_GETNOTICE": {"path": "hkaccount/common/app/getnotice", "host": "h801"}, "CRM_CGI_DTMSPRODUCT_MOBILEAREA_QUERY": {"path": "dtmsproduct/mobilearea/query", "encryption": "false", "host": "h801"}, "HK_USER_YUYUE_PRODUCT_PRECONTRACT": {"path": "/hk/user/yuyue/product/precontract.json", "ver": "false", "h5path": "/hk/user/yuyue/product/precontract.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SIMUFAVORITEGROUPLIST": {"path": "/hk/v240/simufavoritegrouplist.json", "ver": "false", "h5path": "/hk/v240/simufavoritegrouplist.json", "h5islocal": "false", "host": "h106"}, "HK_V240_USERSIMUFAVORITETOP": {"path": "/hk/v240/usersimufavoritetop.json", "ver": "false", "h5path": "/hk/v240/usersimufavoritetop.json", "h5islocal": "false", "host": "h106"}, "HK_V240_USERSIMUFAVORITESORT": {"path": "/hk/v240/usersimufavoritesort.json", "ver": "false", "h5path": "/hk/v240/usersimufavoritesort.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SIMUFAVORITECUSTOMGROUPOPERATE": {"path": "/hk/v240/simufavoritecustomgroupoperate.json", "ver": "false", "h5path": "/hk/v240/simufavoritecustomgroupoperate.json", "h5islocal": "false", "host": "h106"}, "HK_V240_BATCHOPERATEFAVORITEGROUPRELATION": {"path": "/hk/v240/batchoperatefavoritegrouprelation.json", "ver": "false", "h5path": "/hk/v240/batchoperatefavoritegrouprelation.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SIMUHOLDJJDMINDEX": {"path": "/hk/v240/simuholdjjdmindex.json", "ver": "false", "h5path": "/hk/v240/simuholdjjdmindex.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SIMUFAVORITESWITCHOPERATE": {"path": "/hk/v240/simufavoriteswitchoperate.json", "ver": "false", "h5path": "/hk/v240/simufavoriteswitchoperate.json", "h5islocal": "false", "host": "h106"}, "HK_V240_FAVORITECUSTOMGROUPOPERATEBATCH": {"path": "/hk/v240/favoritecustomgroupoperatebatch.json", "ver": "false", "h5path": "/hk/v240/favoritecustomgroupoperatebatch.json", "h5islocal": "false", "host": "h106"}, "HK_V240_GETFAVORITELISTBYGROUPID": {"path": "/hk/v240/getfavoritelistbygroupid.json", "ver": "false", "h5path": "/hk/v240/getfavoritelistbygroupid.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SIMUCCTOTOPALL": {"path": "/hk/v240/simucctotopall.json", "ver": "false", "h5path": "/hk/v240/simucctotopall.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SMFAVORITEFUNDLIST": {"path": "/hk/v240/smfavoritefundlist.json", "ver": "false", "h5path": "/hk/v240/smfavoritefundlist.json", "h5islocal": "false", "host": "h106"}, "HK_V240_FAVORITERECOMMENDPRODUCTLIST": {"path": "/hk/v240/favoriterecommendproductlist.json", "ver": "false", "h5path": "/hk/v240/favoriterecommendproductlist.json", "h5islocal": "false", "host": "h106"}, "HK_V240_USERFAVORITEMODIFYBATCH": {"path": "/hk/v240/userfavoritemodifybatch.json", "ver": "false", "h5path": "/hk/v240/userfavoritemodifybatch.json", "h5islocal": "false", "host": "h106"}, "HK_V240_BATCHSORTFAVORITEBYGROUPID": {"path": "/hk/v240/batchsortfavoritebygroupid.json", "ver": "false", "h5path": "/hk/v240/batchsortfavoritebygroupid.json", "h5islocal": "false", "host": "h106"}, "HK_V240_SIMUFAVBATCHREMOVE": {"path": "/hk/v240/simufavbatchremove.json", "ver": "false", "h5path": "/hk/v240/simufavbatchremove.json", "h5islocal": "false", "host": "h106"}, "HK_USER_YUYUE_COOPERATION_PRECONTRACT": {"path": "/hk/user/yuyue/cooperation/precontract.json", "ver": "false", "h5path": "/hk/user/yuyue/cooperation/precontract.json", "h5islocal": "false", "host": "h106"}, "MAIN_V828_XWYZ_GETHOST": {"path": "/main/v828/xwyz/gethost.json", "ver": "false", "encryption": "false", "h5path": "/main/v828/xwyz/gethost.json", "h5islocal": "false", "host": "h101"}, "HK_MAIN_GRAYSCALE_GET_FEATURE_LISTS": {"path": "/main/grayscale/getFeatureLists.json", "ver": "false", "h5path": "/main/grayscale/getFeatureLists.json", "h5islocal": "false", "host": "h101"}}, "global": {"host_address": {"hostadd4": "http://trade-cgi-gateway.it42.k8s.howbuy.com/gateway/", "hostadd3": "http://trade-cgi-gateway.it42.k8s.howbuy.com/crm-cgi/ext/", "hostadd2": "http://s.it37.k8s.howbuy.com/", "hostadd1": "http://data.it42.k8s.howbuy.com/cgi/", "hostadd5": "http://hk-data-cgi.it42.k8s.howbuy.com/hkd-cgi/"}, "host_address_name": {"hostadd4": "CRM香港接口", "hostadd3": "cgi交易", "hostadd2": "cgi搜索", "hostadd1": "data金融数据接口", "hostadd5": "海外数据服务接口"}, "host_type": {"h701": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd1", "memo": "urls配置"}, "h801": {"safePolicy": "1", "encryption": "true", "hostArr": "hostadd3", "memo": "crm香港"}, "h802": {"safePolicy": "1", "encryption": "true", "hostArr": "hostadd6", "memo": "crm香港-gateway"}, "h101": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd1", "memo": "cgi非交易首页"}, "h102": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd1", "memo": "cgi自选|评论"}, "h201": {"safePolicy": "1", "encryption": "true", "hostArr": "hostadd3", "memo": "cgi海外交易"}, "h103": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd1", "memo": "cgi公募"}, "h202": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd6", "memo": "cab地址api-boot"}, "h104": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd1", "memo": "cgi私募"}, "h203": {"safePolicy": "1", "encryption": "true", "hostArr": "hostadd4", "memo": "cgi海外交易-gateway"}, "h105": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd2", "memo": "cgi搜索"}, "h106": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd5", "memo": "hk-data-cgi接口"}, "h601": {"safePolicy": "1", "encryption": "false", "hostArr": "hostadd1", "memo": "日志上报域名"}}}, "newestTime": "2020-09-11 11:06:09"}