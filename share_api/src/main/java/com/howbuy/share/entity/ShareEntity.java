package com.howbuy.share.entity;

/**
 * //重新定义分享数据Model
 *
 * Created by tao.liang on 2015/10/27.
 */
public class ShareEntity {

    private String title; //分享的标题
    private String content; //分享的内容
    private String contentUrl; //分享的url
    private Object bmp; //分享的图片

    public ShareEntity(String title, String content, String contentUrl, Object bmp) {
        this.title = title;
        this.content = content;
        this.contentUrl = contentUrl;
        this.bmp = bmp;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentUrl() {
        return contentUrl;
    }

    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }

    public Object getBmp() {
        return bmp;
    }

    public void setBmp(Object bmp) {
        this.bmp = bmp;
    }
}
