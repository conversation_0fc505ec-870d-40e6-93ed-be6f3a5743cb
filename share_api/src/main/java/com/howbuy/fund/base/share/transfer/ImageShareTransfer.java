package com.howbuy.fund.base.share.transfer;

import com.howbuy.lib.interfaces.IShareHelper;
import com.howbuy.share.entity.ShareItem;
import com.howbuy.share.entity.ShareMsg;

import java.util.ArrayList;
import java.util.List;

public class ImageShareTransfer implements DataTransfer {
    @Override
    public List<ShareItem> transfer(Object object) {
        if (!(object instanceof ImageShareBean)) {
            return null;
        }
        List<ShareItem> items=new ArrayList<>();
        ShareMsg msgEmpty=new ShareMsg();
        for (int i = 1; i <= 6; i++) {
            if(i== IShareHelper.SHARE_TYPE_SINA||i== IShareHelper.SHARE_TYPE_MORE||i==IShareHelper.SHARE_TYPE_QQZone) {
                continue;
            }
            ShareItem item = ShareItemUtil.getShareItem(i);
            item.shareMsg = msgEmpty;
            items.add(item);
        }
        return items;
    }

}
