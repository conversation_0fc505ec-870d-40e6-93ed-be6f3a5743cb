package com.howbuy.router.provider;

import android.graphics.Bitmap;
import android.view.View;
import android.webkit.WebView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.template.IProvider;

import io.reactivex.Observable;

/**
 * 通用截屏接口
 */
public interface IScreenShotService extends IProvider {
    /**
     * 对WebView进行长截图
     */
    @Nullable
    Bitmap shotWebView(@NonNull WebView webView);

    @Nullable
    Bitmap shotScrollView(@NonNull NestedScrollView scrollView, @Nullable String colorStr);

    Bitmap shotScrollViewWithNoBgColor(@NonNull NestedScrollView scrollView);

    /**
     * Recycleview
     * viewpager中带有Fragment子页面,无法生成内容
     * recycleview中带有子Recycleview的title 必须 width是mathch_parent.
     * 同功能同{@link #shotRecyclerView(RecyclerView, int, int, Bitmap)}后3个参数为：maxCount =0, maxHeight =-1, spaceBitmap = null
     */
    Bitmap shotRecyclerView(RecyclerView view);

    /**
     * 功能同{@link #shotRecyclerView(RecyclerView, int, int, Bitmap)}中后2个参数值为-1, null
     * @param view
     * @param maxCount
     * @return
     */
    @Nullable
    Bitmap shotRecyclerView(@NonNull RecyclerView view,
                            int maxCount);

    /**
     * @param view  RecyclerView, 截图时,addItemDecoration无法截图出来,需要通过一个spaceBitmap拼接实现
     * @param maxCount  给定一个最大可截屏的数量
     * @param maxHeight 给定一个最大可截屏的高度
     */
    @Nullable
    Bitmap shotRecyclerView(RecyclerView view,
                            int maxCount,
                            int maxHeight,
                            Bitmap spaceBitmap);


    /**
     * <ul>创建截屏,仅对以下几种View可以实现长截屏，其他的都只能截取一屏的内容
     * <li>{@link WebView}</li>
     * <li>{@link NestedScrollView}</li>
     * <li>{@link RecyclerView}</li>
     * </ul>
     * @param view     目标view
     * @param addColor 是否添加背景色（#f2f5f9）
     * @return
     */
    @NonNull
    Observable<Bitmap> createShotFlow(@NonNull View view, boolean addColor);
}
