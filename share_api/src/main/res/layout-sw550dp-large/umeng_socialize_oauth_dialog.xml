<?xml version="1.0" encoding="utf-8"?>
<!--suppress AndroidMissingOnClickHandler -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:padding="5dp" >

    <RelativeLayout
        android:layout_width="match_parent"
        android:background="#0086DC"
        android:id="@+id/umeng_socialize_titlebar"
        android:layout_height="54dp">
        <TextView
            android:layout_width="wrap_content"
            android:id="@+id/umeng_title"
            android:textSize="18sp"
            android:text=""
            android:textColor="#ffffff"
            android:layout_centerInParent="true"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="60dp"
            android:id="@+id/umeng_back"

            android:visibility="visible"
            android:onClick="onCancel"
            android:layout_height="match_parent">
            <ImageButton

                android:scaleType="center"
                android:layout_centerVertical="true"
                android:layout_marginLeft="15dp"
                android:background="@drawable/umeng_socialize_back_icon"
                android:layout_width="12dp"

                android:layout_height="20dp" />
        </RelativeLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="分享"
            android:layout_centerInParent="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="10dp"
            android:id="@+id/umeng_share_btn"
            android:padding="4dp"
            android:background="@drawable/umeng_socialize_btn_bg"
            android:textColor="#ffffff"
            />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/umeng_socialize_follow"
        android:layout_width="fill_parent"
        android:layout_height="38dp"
        android:layout_alignParentBottom="true"

        android:visibility="visible" >

        <CheckBox
            android:id="@+id/umeng_socialize_follow_check"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="12dp"

            android:checked="true" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="2dp"
            android:layout_toRightOf="@id/umeng_socialize_follow_check"
            android:textSize="14sp"
            android:text="关注官方微博"
            android:textColor="#949BA4" />
    </RelativeLayout>

    <!--<LinearLayout-->
        <!--android:id="@+id/webView_container"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="match_parent"-->
        <!--android:layout_above="@id/umeng_socialize_follow"-->
        <!--android:layout_below="@id/umeng_socialize_titlebar"-->
        <!--android:orientation="horizontal"-->
        <!--android:visibility="visible" />-->

    <WebView
    android:id="@+id/webView"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:background="#F2F5F6"

    android:layout_above="@id/umeng_socialize_follow"
    android:layout_below="@id/umeng_socialize_titlebar"
    android:visibility="visible" />

    <RelativeLayout
        android:id="@+id/progress_bar_parent"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_above="@id/umeng_socialize_follow"
        android:layout_below="@id/umeng_socialize_titlebar"
        android:background="#F2F5F6" >

        <LinearLayout
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:orientation="horizontal" >

            <ProgressBar
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_margin="10dp" />
        </LinearLayout>
    </RelativeLayout>

</RelativeLayout>