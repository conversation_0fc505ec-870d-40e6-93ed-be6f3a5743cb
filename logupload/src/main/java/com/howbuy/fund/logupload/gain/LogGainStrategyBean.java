package com.howbuy.fund.logupload.gain;

import android.os.Parcel;
import android.os.Parcelable;

import com.howbuy.fund.net.entity.common.normal.AbsNormalBody;

/**
 * Create by zsm on 2021/4/14.
 **/
public class LogGainStrategyBean extends AbsNormalBody implements Parcelable {
    private String collect;//日志sdk总开关（1开启 0关闭）
    private String isUpload;//日志开关
    private String realTime;//本地日志回捞开关（1 开启 0 关闭）
    private String expireDayCount;//日志文件有效期天数（默认3天）
    private String timeInterval;//实时上传时间间隔（默认30s）
    private String logLevel;//日志级别（1-debug 2-info 3-error）
    private String allFileSize;//文件存储最大容量（默认200M）
    private String maxFileSize;//单文件存储最大容量（默认10M）
    private String encryptKeys;//增量接口上下行数据脱敏字段，以|分隔 例：pwd|tradePwd
    private String timestamp;//时间戳（取值：最后一次修改日志执行策略时间，用于客户端判断是否需要更新本地策略）

    public String getCollect() {
        return collect;
    }

    public String getIsUpload() {
        return isUpload;
    }

    public String getRealTime() {
        return realTime;
    }

    public String getExpireDayCount() {
        return expireDayCount;
    }

    public String getTimeInterval() {
        return timeInterval;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public String getAllFileSize() {
        return allFileSize;
    }

    public String getMaxFileSize() {
        return maxFileSize;
    }

    public String getEncryptKeys() {
        return encryptKeys;
    }

    public String getTimestamp() {
        return timestamp;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.collect);
        dest.writeString(this.isUpload);
        dest.writeString(this.realTime);
        dest.writeString(this.expireDayCount);
        dest.writeString(this.timeInterval);
        dest.writeString(this.logLevel);
        dest.writeString(this.allFileSize);
        dest.writeString(this.maxFileSize);
        dest.writeString(this.encryptKeys);
        dest.writeString(this.timestamp);
    }

    public LogGainStrategyBean() {
    }

    protected LogGainStrategyBean(Parcel in) {
        this.collect = in.readString();
        this.isUpload = in.readString();
        this.realTime = in.readString();
        this.expireDayCount = in.readString();
        this.timeInterval = in.readString();
        this.logLevel = in.readString();
        this.allFileSize = in.readString();
        this.maxFileSize = in.readString();
        this.encryptKeys = in.readString();
        this.timestamp = in.readString();
    }

    public static final Creator<LogGainStrategyBean> CREATOR = new Creator<LogGainStrategyBean>() {
        @Override
        public LogGainStrategyBean createFromParcel(Parcel source) {
            return new LogGainStrategyBean(source);
        }

        @Override
        public LogGainStrategyBean[] newArray(int size) {
            return new LogGainStrategyBean[size];
        }
    };
}
