package com.howbuy.fund.logupload.gain;

import androidx.annotation.NonNull;

import java.io.File;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class UploadTask {
    private static final String TAG = "[Log-UploadTask]";

    public ResultConsumer resultConsumer;
    /**是否对正在写入的日志文件，先进行归档然后上传*/
    public boolean archiveAndUploadNewLog;
    private Map<String, File> uploadFiles;
    private Map<String, Long> uploadProgress;
    private long fileSizeTotal;
    /**文件未上传部分的大小*/
    private long remainTotal;
    private final List<String> succeedList = new LinkedList<>();
    private final List<String> failedList = new LinkedList<>();

    public void init(@NonNull Map<String, File> uploadFiles) {
        this.uploadFiles = uploadFiles;
        uploadProgress = new HashMap<>();
        for (File file : uploadFiles.values()) {
            fileSizeTotal += file.length();
        }
        this.remainTotal = fileSizeTotal;
    }

    public void updateProgress(@NonNull String fileName, long progress) {
        log("updateProgress, file:%s, progress:%d", fileName, progress);
        File file = uploadFiles.get(fileName);
        if (file == null) {
            return;
        }
        updateProgress(file, progress);
    }

    public void updateProgress(@NonNull File file, long progress) {
        uploadProgress.put(file.getName(), file.length() - progress);

        long lastSize = 0L;
        for (Long fileLast : uploadProgress.values()) {
            if (null == fileLast) continue;
            lastSize += fileLast;
            if (fileLast > 0) {
                break;
            }
        }
        remainTotal = lastSize;
        if (remainTotal <= 0 && null != resultConsumer) {
            onComplete();
        }
    }


    private void onFileComplete(@NonNull File file) {
        Long progress = uploadProgress.get(file.getName());
        if (null != progress) {
            updateProgress(file.getName(), file.length() - progress);
        } else {
            updateProgress(file.getName(), file.length());
        }
    }

    public void onSuccess(@NonNull File file) {
        log("file upload succeed, name:%s", file.getName());
        succeedList.add(file.getName());
        onFileComplete(file);
    }

    public void onSuccess(@NonNull String fileName) {
        File file = uploadFiles.get(fileName);
        if (null == file) return;
        onSuccess(file);
    }

    public void onFailed(@NonNull String fileName) {
        log("file upload failed, name:%s", fileName);
        File file = uploadFiles.get(fileName);
        if (null == file) return;
        onFileComplete(file);
        failedList.add(fileName);
    }

    public void onFailed(@NonNull File file) {
        log("file upload failed, name:%s", file.getName());
        onFileComplete(file);
        failedList.add(file.getName());
    }

    public void onComplete() {
        log("upload complete");
        if (null != resultConsumer) {
            resultConsumer.onComplete(succeedList.size(), failedList.size());
        }
    }

    private static void log(String pattern, Object... args) {
        DebugLog.log(TAG, pattern, args);
    }
}
