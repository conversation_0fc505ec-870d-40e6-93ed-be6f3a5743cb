package com.howbuy;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Looper;
import android.text.TextUtils;
import com.google.gson.GsonUtils;
import com.howbuy.entity.DnsBean;
import com.howbuy.entity.ErrorDnsBean;
import com.howbuy.fund.base.mvp.rx.RxDisposableObserverN;
import com.howbuy.fund.base.mvp.rx.ScheduleUtil;
import com.howbuy.fund.logupload.BuzzExeceptionUploadMgr;
import com.howbuy.fund.net.http.ReqParams;
import com.howbuy.fund.net.util.OkhttpRequestBuilderUtils;
import com.howbuy.guomi_api.IGuomiProvider;
import com.howbuy.http.okhttp3.Call;
import com.howbuy.http.okhttp3.Callback;
import com.howbuy.http.okhttp3.OkHttpClient;
import com.howbuy.http.okhttp3.Response;
import com.howbuy.lib.compont.GlobalApp;
import com.howbuy.lib.utils.LogUtils;
import com.howbuy.router.proxy.Invoker;
import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 检测DNS网络状态的请求，在app启动时就进行
 * 特殊点，网络监测的时间为5s，要在OkHttp配置ok后进行
 */
public class HttpReportDnsCall {
    private static final String TAG = "HttpReportDnsCall";
    public static final int TIMEOUT_TIME = 5000;
    public final static String EHOWBUY_CDN = "https://trade.ehowbuy.com/trace/trace.htm";
    public final static String EHOWBUY = "https://dtrade.ehowbuy.com/trace/trace.htm";
    public static final String EHOWBUY_CDN_HOST = "trade.ehowbuy.com";
    public static final String HOWBUY_CDN_HOST = "data.ehowbuy.com";
    public static final String HOWBUY_CDN = "https://data.howbuy.com/trace/trace.htm";
    public static final String HOWBUY = "https://ddata.howbuy.com/trace/trace.htm";
    /**cnd上报key*/
    public static final String KEY_CND = "cdnType";
    /**
     * 进行异常上报的地址
     */
    public final static String REPORT_IP = "https://detect.howbuy.com/report/report.htm";

    public final static int EHOWBUY_CDN_TYPE = 1;

    public final static int HOWBUY_CDN_TYPE = 2;

    private final int DNS_LOCAL_TYPE = 3; // localDNS获取域名
    private final int DNS_LOCAL_REPORT_TYPE = 4; // 上报

    public final static int EHOWBUY_TYPE = 5;

    public final static int HOWBUY_TYPE = 6;

    public static final int DNS_LOCAL_FOR_PERFORMANCE_TYPE = 7;//性能上报获取本地dns

    public static final long ERROR_THRESHOLD = 1000_0000L;


    private static HttpReportDnsCall mInstance = null;

    //请求耗时
    private final Map<String, Long> requestCostMap = Collections.synchronizedMap(new HashMap<>());

    //性能上报非公共参数
    private final Map<String, Object> performanceMap = Collections.synchronizedMap(new HashMap<>());

    private final AtomicInteger requestCnt = new AtomicInteger();
    private OkHttpClient mClient;
    //cnd响应ip
    private final Map<String, String> cdnIpMap = Collections.synchronizedMap(new HashMap<>(2));
    //请求cdn host 对应ip
    private int getIpRequestCnt = 0;

    private final int[] mDetectArray = {EHOWBUY_CDN_TYPE, HOWBUY_CDN_TYPE, EHOWBUY_TYPE, HOWBUY_TYPE};
    private final StringBuffer mStringBufferTrace = new StringBuffer();
    /** 调用执行次数 */
    private final AtomicInteger mCallCnt = new AtomicInteger(0);

    private HttpReportDnsCall() {
        getIpAddress(EHOWBUY_CDN_HOST);
        getIpAddress(HOWBUY_CDN_HOST);
    }


    protected OkHttpClient initClient() {
        if (mClient == null) {
            OkHttpClient.Builder mOkHttpClientBuilder = new OkHttpClient.Builder();
            mOkHttpClientBuilder.connectTimeout(TIMEOUT_TIME, TimeUnit.SECONDS);
            mClient = mOkHttpClientBuilder.build();
        }
        return mClient;
    }

    public static HttpReportDnsCall getInstance() {
        if (mInstance == null) {
            mInstance = new HttpReportDnsCall();
        }
        return mInstance;
    }

    /**
     * @param url          请求的url
     * @param originParams 参数
     */
    public void doGetAsync(String url, HashMap<String, Object> originParams, int handType) {
        try {
            ReqParams param = new ReqParams();
            param.setUrl(url);
            param.setCls(DnsBean.class);
            param.setUriKey(url);
            param.setParams(originParams);
            param.setHandType(handType);
            Call call = initClient().newCall(OkhttpRequestBuilderUtils.getInstance().createBuilder(param).build());
            requestStart(handType);
            call.enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    requestResult(handType, false, "", e == null ? "" : e.getMessage());
                }

                @Override
                public void onResponse(Call call, Response response){
                    String responseContent = "";
                    try {
                        if (response.body()!=null) {
                            responseContent = response.body().string();
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    requestResult(handType, true, responseContent, "");
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void doGetAsync(String url, HashMap<String, Object> originParams, Callback callback){
        try {
            ReqParams param = new ReqParams();
            param.setUrl(url);
            param.setCls(DnsBean.class);
            param.setUriKey(url);
            param.setParams(originParams);
            param.setHandType(0);
            Call call = initClient().newCall(OkhttpRequestBuilderUtils.getInstance().createBuilder(param).build());
            call.enqueue(callback);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void requestResult(int handleType, boolean success, String json, String errorMsg){
        onRequestFinish(handleType);
        LogUtils.d(TAG, "type = " + handleType + " ok = " + success);
        if (success) {
            if (handleType == DNS_LOCAL_TYPE){
                DnsBean dnsBean = GsonUtils.toObj(json, DnsBean.class);
                if (dnsBean != null && TextUtils.equals(dnsBean.getCode(), "200")) {
                    doGetAsync(REPORT_IP, getParms(dnsBean.getLocaldns().getDns(), 1, ""), DNS_LOCAL_REPORT_TYPE);
                } else {
                    reportCdnError(errorMsg, json, 1);
                }
            } else if (handleType == DNS_LOCAL_FOR_PERFORMANCE_TYPE) {
                //性能上报
                DnsBean dnsBean = GsonUtils.toObj(json, DnsBean.class);
                if (dnsBean != null && TextUtils.equals(dnsBean.getCode(), "200")) {
                    HashMap<String, Object> publicParas = getParms(dnsBean.getLocaldns().getDns(), 2);
                    performanceMap.putAll(publicParas);
                    checkIpAddressResult();
                }
            }
        } else {
            initReport(handleType);
            reportLocalDnsErrorIfNeeded(handleType, errorMsg, json);
        }
    }

    private void initReport(int type) {
        if (type == EHOWBUY_CDN_TYPE || type == HOWBUY_CDN_TYPE) {
            //若支持Https请求，则先请求Type3，否则直接上报
            doGetAsync(getLocalDnsUrl(), null, DNS_LOCAL_TYPE);
        }
    }

    private void reportLocalDnsErrorIfNeeded(int type, String errorMsg, String json){
        if (type == DNS_LOCAL_TYPE){
            reportCdnError(errorMsg, json, 1);
        }
    }


    private void reportCdnError(final String errorMsg,
                                final String json,
                                final int cndType) {
        ErrorDnsBean errorDnsBean = new ErrorDnsBean();
        errorDnsBean.errorMsg = errorMsg;
        errorDnsBean.dnsBean = GsonUtils.toObj(json, DnsBean.class);
        String dns = errorDnsBean.dnsBean == null || errorDnsBean.dnsBean.getLocaldns() == null ? "" : errorDnsBean.dnsBean.getLocaldns().getDns();
        //LogUtils.d(TAG, "errorMsg = " + GsonUtils.toJson(errorDnsBean));
        doGetAsync(REPORT_IP, getParms(dns, cndType, GsonUtils.toJson(errorDnsBean)), DNS_LOCAL_REPORT_TYPE);
    }

    private boolean needAnalysis(int type) {
        return type == EHOWBUY_CDN_TYPE ||
                type == HOWBUY_CDN_TYPE ||
                type == EHOWBUY_TYPE ||
                type == HOWBUY_TYPE;
    }

    private void onRequestFinish(int type){
        if (!needAnalysis(type)){
            return;
        }

        executeTaskInMain(() -> dispatchRequestCnt(type));
    }

    private synchronized void dispatchRequestCnt(int type){
        //请求失败耗时
        Long requestStartTime = requestCostMap.get(getRequestUrl(type));
        if (requestStartTime == null) {
            requestStartTime = 0L;
        }
        mStringBufferTrace.append(" dispatchRequestCnt enter requestTime = ").append(requestStartTime);
        long requestCost;
        long currentTimeMillis = System.currentTimeMillis();
        requestCost = currentTimeMillis - requestStartTime;
        requestCostMap.remove(getRequestUrl(type));
        requestCostMap.put(getRequestUrl(type), requestCost);
        mStringBufferTrace.append(" dispatchRequestCnt finish cost = ").append(requestCost);
        int decrementAndGet = requestCnt.incrementAndGet();
        if (decrementAndGet == mDetectArray.length) {
            doAnalysis();
        }
    }

    private void requestStart(int type){
        if (needAnalysis(type) && mCallCnt.getAndAdd(0) <= 1){
            executeTaskInMain(() -> saveRequestTime(type));
        }
    }


    private synchronized void saveRequestTime(final int type) {
        //偶发执行多次?
        long timeMillis = System.currentTimeMillis();
        String requestUrl = getRequestUrl(type);
        if (requestCostMap.get(requestUrl) != null) {
            //已经记录,重复回调
            mStringBufferTrace.append(" type = ").append(type).append(" saveRequestTime more than once");
            LogUtils.d(TAG, "saveRequestTime return - type = " + type + " because call more than once");
            return;
        }
        LogUtils.d("localNDS", "url = "
                + requestUrl
                + " time = "
                + timeMillis
                + " type = " + type);
        requestCostMap.put(requestUrl, timeMillis);
        mStringBufferTrace.append(" type = ").append(type).append(" saveRequestTime success");
    }

    private void doAnalysis() {
        //cdn ehowbuy 耗时
        Long cdnEHowbuyCost = requestCostMap.get(getRequestUrl(EHOWBUY_CDN_TYPE));
        //cdn howbuy 耗时
        Long cdnHowbuyCost = requestCostMap.get(getRequestUrl(HOWBUY_CDN_TYPE));
        //直连 ehowbuy 耗时
        Long eHowbuyCost = requestCostMap.get(getRequestUrl(EHOWBUY_TYPE));
        //直连 howbuy 耗时
        Long howbuyCost = requestCostMap.get(getRequestUrl(HOWBUY_TYPE));
        int callCount = mCallCnt.getAndAdd(0);
        //1 耗时 = 787 2耗时 = 679 5耗时 = 670 6耗时 = 660
        LogUtils.d(TAG, "call count = " + callCount + " EHOWBUY_CDN 耗时 = " + cdnEHowbuyCost + " HOWBUY_CDN 耗时 = " + cdnHowbuyCost  + " EHOWBUY 耗时 = " + eHowbuyCost + " HOWBUY 耗时 = " + howbuyCost + " thread-" + Thread.currentThread().getName());
        if (cdnEHowbuyCost == null || cdnHowbuyCost == null || eHowbuyCost == null || howbuyCost == null) {
            return;
        }
        //存入请求时间原来是异步的,导致可能刚存入正好触发上报没来得及存入耗时就会上报当前时间戳

        if (cdnEHowbuyCost >= ERROR_THRESHOLD
                || cdnHowbuyCost >= ERROR_THRESHOLD
                || eHowbuyCost >= ERROR_THRESHOLD
                || howbuyCost >= ERROR_THRESHOLD) {
            BuzzExeceptionUploadMgr.getInstance().uploadBuzActionToElk("CDN性能统计异常", "cdn_howbuy_costTime=" + cdnHowbuyCost
                    + ",cdn_ehowbuy_costTime = " + cdnEHowbuyCost
                    + ",howbuy_costTime = " + howbuyCost
                    + ",ehowbuy_costTime = " + eHowbuyCost + ", trace = " + mStringBufferTrace, false);
            return;
        }


        if (callCount > 1) {
            //执行多次上报
            BuzzExeceptionUploadMgr.getInstance().uploadBuzActionToElk("CDN性能统计异常",
                    "call count = " + mCallCnt.getAndAdd(0)
                    + "cdn_howbuy_costTime=" + cdnHowbuyCost
                    + ",cdn_ehowbuy_costTime = " + cdnEHowbuyCost
                    + ",howbuy_costTime = " + howbuyCost
                    + ",ehowbuy_costTime = " + eHowbuyCost + ", trace = " + mStringBufferTrace , false);
        }

        //cdn与直连howbuy耗时差 (6)-(2)
        long diffHowbuy = howbuyCost - cdnHowbuyCost;
        //cdn与直连ehowbuy耗时差 (5)-(1)
        long diffEHowbuy = eHowbuyCost - cdnEHowbuyCost;

        performanceMap.clear();
        performanceMap.put("cdn_howbuy_costTime", String.valueOf(cdnHowbuyCost));
        performanceMap.put("cdn_ehowbuy_costTime", String.valueOf(cdnEHowbuyCost));
        performanceMap.put("howbuy_costTime", String.valueOf(howbuyCost));
        performanceMap.put("ehowbuy_costTime", String.valueOf(eHowbuyCost));
        performanceMap.put("diff_howbuy", String.valueOf(diffHowbuy));
        performanceMap.put("diff_ehowbuy", String.valueOf(diffEHowbuy));

        //性能上报
        doGetAsync(getLocalDnsUrl(), null, DNS_LOCAL_FOR_PERFORMANCE_TYPE);
    }

    /**
     * @return localDNS获取域名
     */
    public String getLocalDnsUrl() {
        return getLocalDnsUrl(true);
    }

    public String getLocalDnsUrl(boolean useHttps){
        StringBuilder sb = new StringBuilder(useHttps ? "https://" : "http://");
        sb.append(Invoker.getInstance().navigation(IGuomiProvider.class).getTokenId() + System.currentTimeMillis());
        sb.append(".ldns.ehowbuy.com/api/dns/detect");
        return sb.toString();
    }

    private HashMap<String, Object> getParms(String dns, int cdnType) {
        return  getParms(dns, cdnType, "");
    }

    private HashMap<String, Object> getParms(String dns, int cdnType, String errorMsg) {
        IGuomiProvider guomiProvider = Invoker.getInstance().navigation(IGuomiProvider.class);
        Context context = guomiProvider.getApp();
        String hboneNo = guomiProvider.getHboneNo();
        String buildType = guomiProvider.getBuildType();
        String tokenId = guomiProvider.getTokenId();
        String version = guomiProvider.getVersion();
        String deviceId = guomiProvider.getDeviceId();
        String netWorkType = guomiProvider.getNetWorkType();


        HashMap<String, Object> parms = new HashMap<>();
        parms.put("netType", netWorkType);
        parms.put("dns", dns);
        parms.put(KEY_CND, String.valueOf(cdnType)); // 1异常上报,2性能上报
        parms.put("hboneNo", hboneNo);
        parms.put("parPhoneModel", "android");
        parms.put("appEnvir", TextUtils.equals(buildType, "release") ? "release": "debug"); //release ；debug
        parms.put("tokenId", tokenId);
        parms.put("deviceId", deviceId);
        parms.put("version", version);
        parms.put("errorMsg", errorMsg);
        return parms;
    }

    private HashMap<String, Object> getParms(int cdnType, String errorMsg){
        return getParms("", cdnType, errorMsg);
    }

    /**
     *
     * @param type handleType
     * @return 获取请求地址
     */
    public static String getRequestUrl(int type){
        switch (type){
            case EHOWBUY_CDN_TYPE:
                return EHOWBUY_CDN;
            case EHOWBUY_TYPE:
                return EHOWBUY;
            case HOWBUY_CDN_TYPE:
                return HOWBUY_CDN;
            case HOWBUY_TYPE:
                return HOWBUY;
            default:
                return "";
        }
    }

    public void reset() {
        performanceMap.clear();
        requestCostMap.clear();
        requestCnt.compareAndSet(0, 0);

    }

    public void start() {
        mCallCnt.incrementAndGet();
        for (int detectType : mDetectArray) {
            HttpReportDnsCall.getInstance().doGetAsync(getRequestUrl(detectType), null, detectType);
        }
    }
    
    private void executeTaskInMain(Runnable runnable) {
        if (runnable == null) {
            return;
        }
        if (Looper.getMainLooper() == Looper.myLooper()) {
            runnable.run();
        } else {
            GlobalApp.getApp().runOnUiThread(runnable, 0);
        }
    }

    @SuppressLint("CheckResult")
    private void getIpAddress(String ip){
        getIpRequestCnt++;
        long timeMillis = System.currentTimeMillis();
        Observable.create((ObservableOnSubscribe<String>)  emitter -> {
            try {
                InetAddress inetAddress = InetAddress.getByName(ip);
                String hostAddress = inetAddress.getHostAddress();
                LogUtils.d(TAG, "ip = " + ip + " hostAddress = " + hostAddress + " cost = " + (System.currentTimeMillis() - timeMillis) + " thread = " + Thread.currentThread().getName());
                emitter.onNext(hostAddress);
            } catch (UnknownHostException e) {
                e.printStackTrace();
                LogUtils.d(TAG, "获取ip异常 = " + e.getLocalizedMessage());
                emitter.onNext("");
            }
        }).compose(ScheduleUtil.ioMain()).subscribe(new RxDisposableObserverN<String>() {
            @Override
            public void onNext(final String o) {
                cdnIpMap.put(getCdnParasField(ip), o);
                checkIpAddressResult();
            }
        });

    }

    private String getCdnParasField(String host){
        switch (host) {
            case EHOWBUY_CDN_HOST:
                return "trade_cdn_ip";
            case HOWBUY_CDN_HOST:
                return "data_cdn_ip";
        }
        return "";
    }

    private void checkIpAddressResult(){
        //走到这里cdnType一定为2
        //1.请求ip完成,2.性能上报获取dns成功
        //防止ip获取之后直接上报一次没有非公共参数的
        if (getIpRequestCnt != 0 && cdnIpMap.size() == getIpRequestCnt && !performanceMap.isEmpty() && performanceMap.get(KEY_CND) != null) {
            //cdn请求完毕
            HashMap<String, Object> requestMap = new HashMap<>();
            requestMap.putAll(performanceMap);
            requestMap.putAll(cdnIpMap);
            LogUtils.d(TAG, "性能上报 " + requestMap + "cdnIpMap = " + cdnIpMap);
            doGetAsync(REPORT_IP, requestMap, DNS_LOCAL_REPORT_TYPE);
        }
    }
}
