package com.howbuy.entity;

import android.os.Parcel;
import android.os.Parcelable;

public class DnsBean implements Parcelable{
    private String code;
    private String msg;
    Localdns localdns;

    protected DnsBean(Parcel in) {
        code = in.readString();
        msg = in.readString();
        localdns = in.readParcelable(Localdns.class.getClassLoader());
    }

    public static final Creator<DnsBean> CREATOR = new Creator<DnsBean>() {
        @Override
        public DnsBean createFromParcel(Parcel in) {
            return new DnsBean(in);
        }

        @Override
        public DnsBean[] newArray(int size) {
            return new DnsBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(code);
        parcel.writeString(msg);
        parcel.writeParcelable(localdns, i);
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public Localdns getLocaldns() {
        return localdns;
    }

    public static class Localdns implements Parcelable {
        private String dns;

        public Localdns(Parcel in) {
            dns = in.readString();
        }

        public static final Creator<Localdns> CREATOR = new Creator<Localdns>() {
            @Override
            public Localdns createFromParcel(Parcel in) {
                return new Localdns(in);
            }

            @Override
            public Localdns[] newArray(int size) {
                return new Localdns[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel parcel, int i) {
            parcel.writeString(dns);
        }

        public String getDns() {
            return dns;
        }
    }
}
