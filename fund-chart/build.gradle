plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

//apply from: "${rootProject.projectDir}/arouter-kotlin-config.gradle"
android {
    compileSdkVersion rootProject.ext.commonVersions.compileSdkVersion
    defaultConfig {
        minSdkVersion rootProject.ext.commonVersions.minSdkVersion
        targetSdkVersion rootProject.ext.commonVersions.targetSdkVersion
    }
}

dependencies {
    implementation rootProject.ext.dependencies["mpandroid-chart"]
    implementation rootProject.ext.dependencies['hb-utils']

    testImplementation 'junit:junit:4.13.2'

    //解除依赖传递---------------------------------------
//    implementation rootProject.ext.dependencies['support:recyclerview']
//    implementation rootProject.ext.dependencies['recyclerView-adapter-helper']
}
