package com.howbuy.fund.chart.mpchart.line;

import android.content.Context;
import android.graphics.Canvas;
import android.text.TextUtils;
import android.util.AttributeSet;

import com.github.mikephil.charting.charts.BarLineChartBase;
import com.github.mikephil.charting.data.BarLineScatterCandleBubbleData;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.interfaces.datasets.IBarLineScatterCandleBubbleDataSet;
import com.github.mikephil.charting.utils.MPPointF;

/**
 * Created by tao.liang on 2016/9/28.
 */
public abstract class HbFundLineChartBase<T extends BarLineScatterCandleBubbleData<? extends
        IBarLineScatterCandleBubbleDataSet<? extends Entry>>> extends BarLineChartBase<T> {

    /**
     * if true, can response single or double finger touch event
     */
    private boolean mCustomFingerTouchEnable = false;

    /**
     * if true, land status can execute double touch function
     */
    private boolean mCanDoubleFingerTouchable = true;
    /**
     * 手指触摸的事件回调
     */
    private IHbFundLineChartListener touchListener = null;
    /**
     * 触摸触发高亮线
     * 场景: chart在viewpager页面中,同时未禁用viewpager的页面切换,需要触发高亮线操作,需要设置该属性,
     * 否则,左右滑动高亮操作时,会触发页面的viewpager切换,导致高亮操作左右滑动不了(滑动冲突)
     */
    private boolean mCustomGesutreSingleTap = false;

    /**
     * 触摸视图,高亮是否显示小圆点,
     * 如果需要小圆点,在满足n线条的绘制点都是相同个数下,可以只对本基金设置高亮线处理,
     * 同时添加该样式就行,不用每条线都加上高亮效果
     */
    private boolean isNeedHighLightDot = false;
    /**
     * 高亮线处的实心小圆点半径
     */
    private float highLightDotRadius;
    /**
     * 高亮线处的实心小圆点颜色(使用线条的颜色)
     */
    private int highLightDotColor;
    /**
     * 高亮线处的空心小圆点半径
     */
    private float highLightHoleDotRadius;
    /**
     * 高亮线处的空心小圆点颜色
     */
    private int highLightHoleDotColor;

    /**
     * 绘制高亮线小圆点时,使用线条的颜色
     * 默认:true
     */
    private boolean drawHighLightDotUseLineDataSetColor = true;

    /**
     * 高亮线是否是虚线
     */
    private boolean isHighLightLineDash = true;
    private boolean isTouchOutSideAction = true;

    /**
     * 绘制灰白背景条数默认7个
     */
    private int mCeilCount = 7;

    /**
     * text that is displayed when the chart is empty
     */
    private String mNoDataText = "";
    /**
     * touch delay execute time
     */
    private int mTouchDownTime = 150;
    /**
     * action_up auto execute, cancel highlight
     */
    private boolean mAutoCancelHighLight = true;
    private int mTouchSlopTime = 100;

    public boolean isDrawHighLightDotUseLineDataSetColor() {
        return drawHighLightDotUseLineDataSetColor;
    }

    /**
     * 绘制高亮线交汇处的实心小圆点时,是否使用当前线条的颜色, 默认:使用当前条线的颜色
     *
     * @param drawHighLightDotUseLineDataSetColor true:使用线条颜色作为绘制实心小圆点的颜色
     */
    public void setDrawHighLightDotUseLineDataSetColor(boolean drawHighLightDotUseLineDataSetColor) {
        this.drawHighLightDotUseLineDataSetColor = drawHighLightDotUseLineDataSetColor;
    }

    public float getHighLightDotRadius() {
        return highLightDotRadius;
    }

    public boolean isNeedHighLightDot() {
        return isNeedHighLightDot;
    }

    /**
     * 设置是否需要显示高亮线小圆点样式
     *
     * @param needHighLightDot true:显示手指触摸高亮线样式时,显示小圆点样式(实心+[空心])
     */
    public void setNeedHighLightDot(boolean needHighLightDot) {
        isNeedHighLightDot = needHighLightDot;
    }

    /**
     * 设置高亮线 实心小圆点半径
     *
     * @param highLightDotRadius 实心圆点半径
     */
    public void setHighLightDotRadius(float highLightDotRadius) {
        this.highLightDotRadius = highLightDotRadius;
    }

    public float getHighLightHoleDotRadius() {
        return highLightHoleDotRadius;
    }

    /**
     * 设置高亮线 空心小圆点 半径
     *
     * @param highLightDotHoleRadius 空心圆点半径
     */
    public void setHighLightHoleDotRadius(float highLightDotHoleRadius) {
        this.highLightHoleDotRadius = highLightDotHoleRadius;
    }

    public int getHighLightHoleDotColor() {
        return highLightHoleDotColor;
    }

    /**
     * 设置高亮线 空心小圆点 颜色
     *
     * @param highLightDotHoleColor 空心圆颜色
     */
    public void setHighLightHoleDotColor(int highLightDotHoleColor) {
        this.highLightHoleDotColor = highLightDotHoleColor;
    }

    /**
     * 设置手指触摸事件回调
     *
     * @param listener 回调
     */
    public void setFingerTouchListener(IHbFundLineChartListener listener) {
        this.touchListener = listener;
    }

    public IHbFundLineChartListener getFingerTouchListener() {
        return this.touchListener;
    }

    public boolean isCustomFingerTouchEnable() {
        return mCustomFingerTouchEnable;
    }

    /**
     * 设置是否可以手指触摸
     *
     * @param mCustomFingerTouchEnable     是否支持手指触摸(默认为false)
     * @param custCanDoubleFigureTouchable 是否支持双手指触摸(默认为true)
     */
    public void setCustomFingerTouchEnable(boolean mCustomFingerTouchEnable, boolean custCanDoubleFigureTouchable) {
        this.mCustomFingerTouchEnable = mCustomFingerTouchEnable;
        this.mCanDoubleFingerTouchable = custCanDoubleFigureTouchable;
    }

    public boolean isCanDoubleFingerTouchable() {
        return mCanDoubleFingerTouchable;
    }

    /**
     * 设置背景样式
     *
     * @param customGridBgStyle true:表示绘制灰白相间背景,false:可以通过设置基本属性绘制
     */
    public void setCustomGridBgStyle(boolean customGridBgStyle) {
        ((HbFundXAxisRenderer) mXAxisRenderer).setCustomGridBgStyle(customGridBgStyle);
    }

    /**
     * 设置触摸是否立即生效,高亮显示指示
     */
    public void setTouchImmediate(boolean autoCancelHighLight) {
        this.mTouchDownTime = 0;
        this.mTouchSlopTime = 0;
        this.mAutoCancelHighLight = autoCancelHighLight;

    }

    public int getmTouchDownTime() {
        return mTouchDownTime;
    }


    public int getmTouchSlopTime() {
        return mTouchSlopTime;
    }

    public boolean ismAutoCancelHighLight() {
        return mAutoCancelHighLight;
    }


    /**
     * 设置绘制条数
     *
     * @param ceilCount 为能为0(用于计算除数不能为0,如果为0,重置为1)
     */
    public void setCeilCount(int ceilCount) {
        if (ceilCount == 0) {
            ceilCount = 1;
        }
        this.mCeilCount = ceilCount;
    }

    public boolean isCustomGestureSingleTap() {
        return mCustomGesutreSingleTap;
    }

    public void setCustomGestureSingleTap(boolean mCustomSingleTap) {
        this.mCustomGesutreSingleTap = mCustomSingleTap;
    }

    public int getHighLightDotColor() {
        return highLightDotColor;
    }

    /**
     * 设置绘制高亮线交汇时的小圆点颜色
     * 处理特殊情况:有时候使用线条颜色绘制高亮小圆点的颜色,不满足需求,可设置该颜色值
     * 同时,需要设置 {@link #setDrawHighLightDotUseLineDataSetColor(boolean)}为 false
     *
     * @param highLightDotColor
     */
    public void setHighLightDotColor(int highLightDotColor) {
        this.highLightDotColor = highLightDotColor;
    }

    public boolean isHighLightLineDash() {
        return isHighLightLineDash;
    }

    /**
     * 设置高亮线是否为虚线样式: 默认为虚线样式
     *
     * @param highLightLineDash
     */
    public void setHighLightLineDash(boolean highLightLineDash) {
        isHighLightLineDash = highLightLineDash;
    }
    /**
     * 设置高亮线是否为虚线样式: 默认为虚线样式
     *
     * @param isTouchOutSideAction
     */
    public void setTouchOutSideAction(boolean touchOutSideAction) {
        isTouchOutSideAction = touchOutSideAction;
    }

    public boolean isTouchOutSideAction() {
        return isTouchOutSideAction;
    }

    /**
     * 隐藏顶部边框线: 默认: 不隐藏
     *
     * @param hideYAxisTopLine
     */
    public void setHideYAxisTopLine(boolean hideYAxisTopLine) {
        ((HbFundYAxisRenderer) mAxisRendererRight).setHideYAxisTopLine(hideYAxisTopLine);
    }

    /**
     * 是否绘制图表 顶部的边框
     * @param drawTopBorder
     */
    public void setDrawTopBorder(boolean drawTopBorder) {
        ((HbFundXAxisRenderer) mXAxisRenderer).setDrawTopBorder(drawTopBorder);
    }

    public HbFundLineChartBase(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public HbFundLineChartBase(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public HbFundLineChartBase(Context context) {
        super(context);
    }

    @Override
    protected void init() {
        super.init();
        mChartTouchListener = new HbLineChartTouchListener(this, mViewPortHandler.getMatrixTouch(), 3f);
        mXAxisRenderer = new HbFundXAxisRenderer(mViewPortHandler, mXAxis, mLeftAxisTransformer,
                false, mCeilCount, true);
        mAxisRendererRight = new HbFundYAxisRenderer(mViewPortHandler, mAxisRight, mRightAxisTransformer, false);
    }

    /**
     * Sets the text that informs the user that there is no data available with
     * which to draw the chart.
     *
     * @param text
     */
    @Override
    public void setNoDataText(String text) {
        mNoDataText = text;
    }

    @Override
    protected void onDraw(Canvas canvas) {
//        super.onDraw(canvas);
        if (mData == null) {
            boolean hasText = !TextUtils.isEmpty(mNoDataText);
            if (hasText) {
                MPPointF c = getCenter();
                canvas.drawText(mNoDataText, c.x, c.y, mInfoPaint);
            }
            return;
        }

        long starttime = System.currentTimeMillis();
        // execute all drawing commands
        drawGridBackground(canvas);
        if (mAutoScaleMinMaxEnabled) {
            autoScale();
        }
        if (mAxisLeft.isEnabled())
            mAxisRendererLeft.computeAxis(mAxisLeft.mAxisMinimum, mAxisLeft.mAxisMaximum, mAxisLeft.isInverted());

        if (mAxisRight.isEnabled())
            mAxisRendererRight.computeAxis(mAxisRight.mAxisMinimum, mAxisRight.mAxisMaximum, mAxisRight.isInverted());

        if (mXAxis.isEnabled())
            mXAxisRenderer.computeAxis(mXAxis.mAxisMinimum, mXAxis.mAxisMaximum, false);

        mXAxisRenderer.renderAxisLine(canvas);
        mAxisRendererLeft.renderAxisLine(canvas);
        mAxisRendererRight.renderAxisLine(canvas);
        mXAxisRenderer.renderGridLines(canvas);
        mAxisRendererLeft.renderGridLines(canvas);
        mAxisRendererRight.renderGridLines(canvas);

        if (mXAxis.isEnabled() && mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isEnabled() && mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isEnabled() && mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        // make sure the data cannot be drawn outside the content-rect
        int clipRestoreCount = canvas.save();
        canvas.clipRect(mViewPortHandler.getContentRect());

        if (mRenderer != null) {
            mRenderer.drawData(canvas);
        }
//        mRenderer.drawData(canvas);

        // if highlighting is enabled
        if (valuesToHighlight())
            if (mRenderer != null) {
                mRenderer.drawHighlighted(canvas, mIndicesToHighlight);
            }
//            mRenderer.drawHighlighted(canvas, mIndicesToHighlight);

        // Removes clipping rectangle
        canvas.restoreToCount(clipRestoreCount);

        mRenderer.drawExtras(canvas);

        if (mXAxis.isEnabled() && !mXAxis.isDrawLimitLinesBehindDataEnabled())
            mXAxisRenderer.renderLimitLines(canvas);

        if (mAxisLeft.isEnabled() && !mAxisLeft.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererLeft.renderLimitLines(canvas);

        if (mAxisRight.isEnabled() && !mAxisRight.isDrawLimitLinesBehindDataEnabled())
            mAxisRendererRight.renderLimitLines(canvas);

        mXAxisRenderer.renderAxisLabels(canvas);
        mAxisRendererLeft.renderAxisLabels(canvas);
        mAxisRendererRight.renderAxisLabels(canvas);

        if (isClipValuesToContentEnabled()) {
            clipRestoreCount = canvas.save();
            canvas.clipRect(mViewPortHandler.getContentRect());
            if (mRenderer != null) {
                mRenderer.drawValues(canvas);
            }
//            mRenderer.drawValues(canvas);

            canvas.restoreToCount(clipRestoreCount);
        } else {
            if (mRenderer != null) {
                mRenderer.drawValues(canvas);
            }
            //mRenderer.drawValues(canvas);
        }

        mLegendRenderer.renderLegend(canvas);

        drawDescription(canvas);

        drawMarkers(canvas);
    }


}