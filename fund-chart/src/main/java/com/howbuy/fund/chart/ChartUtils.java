package com.howbuy.fund.chart;

import androidx.annotation.NonNull;

import com.howbuy.lib.utils.MathUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Create by zsm on 2018/8/29.
 **/
public class ChartUtils {

    /**
     * 根据规则绘制y轴label坐标值
     *
     * @param yMin y数据最小值
     * @param yMax y数据最大值
     * @return float[4], y轴下限，y轴上限，label个数，label小数点位数
     */
    public static float[] getYAxisLabel(float yMin, float yMax) {
        float[] AXIS_BASE = new float[]{0.01f, 0.02f, 0.05f, 0.1f, 0.2f, 0.5f, 1, 2, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1000, 2000, 3000, 5000, 10000, 20000, 30000, 50000};
        return getYAxisLabel(yMin, yMax, AXIS_BASE);
    }

    /**
     * 算法类似于@see {@link #getYAxisLabel(float, float)}，只是增加了[100000, 200000, 300000, 500000, 1000000, 2000000, 3000000, 5000000]
     * 解决值<0.01的场景, 增加 [0.0001f, 0.0002f, 0.0003f, 0.0004f, 0.0005f, 0.001f, 0.002f, 0.003f, 0.004f, 0.005f]
     * @param yMin
     * @param yMax
     * @return
     */
    public static float[] getYAxisLabelNew(float yMin, float yMax) {
        float[] AXIS_BASE = new float[]{0.0001f, 0.0002f, 0.0003f, 0.0004f, 0.0005f, 0.001f, 0.002f, 0.003f, 0.004f, 0.005f, 0.01f, 0.02f, 0.05f, 0.1f, 0.2f, 0.5f, 1, 2, 5, 10, 20, 30, 50, 100, 200, 300, 500, 1000, 2000, 3000, 5000, 10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000, 2000000, 3000000, 5000000};
        return getYAxisLabel(yMin, yMax, AXIS_BASE);
    }

    private static float[] getYAxisLabel(float yMin, float yMax, @NonNull float[] AXIS_BASE) {
        float[] labels = new float[4];

        float minAxis, maxAxis;
        float min = yMin, max = yMax;
        if (yMin > 0) min = 0;
        if (yMax < 0) max = 0;
        float d = (max - min) / 5.0f;
        float D = AXIS_BASE[AXIS_BASE.length - 1];
        for (int i = 0; i < AXIS_BASE.length - 1; i++) {
            if (d >= AXIS_BASE[i] && d < AXIS_BASE[i + 1]) {
                D = AXIS_BASE[i + 1];
                break;
            }
        }
        if (min >= 0) {
            minAxis = ((int) (min / D)) * D;
        } else {
            minAxis = ((int) (min / D) - (min % D == 0 ? 0 : 1)) * D;
        }
        if (max >= 0) {
            maxAxis = ((int) (max / D) + (max % D == 0 ? 0 : 1)) * D;
        } else {
            maxAxis = ((int) (max / D)) * D;
        }
        int numOfBits = 0;
        String strNum = String.valueOf(D);
        int bitPos = strNum.indexOf(".");
        if (bitPos != -1) {
            int value = MathUtils.forValI(strNum.substring(bitPos + 1), 0);
            if (value > 0) {
                numOfBits = strNum.length() - bitPos - 1;
            }
        }
        labels[0] = minAxis;
        labels[1] = maxAxis;
        labels[2] = (maxAxis - minAxis) / D + 1;
        labels[3] = numOfBits;
        return labels;
    }

    /**
     * 根据规则绘制净值y轴label坐标值(净值大于0)
     *
     * @param min y数据最小值, mix > 0
     * @param max y数据最大值, max > 0
     * @return double[3], y轴下限，y轴上限，label个数
     */
    public static float[] getNetValueYAxisLabel(float min, float max) {
        float[] AXIS_BASE = new float[]{0.0001f, 0.0002f, 0.0003f, 0.0004f, 0.0005f, 0.001f, 0.002f, 0.003f, 0.004f, 0.005f, 0.01f,
                0.02f, 0.03f, 0.04f, 0.05f, 0.1f, 0.2f, 0.3f, 0.4f, 0.5f, 1, 2, 3, 4, 5, 10, 20, 30, 40, 50, 100};
        float[] labels = new float[3];

        BigDecimal minBigDecimal = new BigDecimal(min + "");
        BigDecimal maxBigDecimal = new BigDecimal(max + "");
        BigDecimal dBigDecimal = (maxBigDecimal.subtract(minBigDecimal)).divide(BigDecimal.valueOf(5f), 5, RoundingMode.HALF_UP);
        float minAxis, maxAxis;
        float d = dBigDecimal.floatValue();
        float D = AXIS_BASE[AXIS_BASE.length - 1];
        for (int i = 0; i < AXIS_BASE.length - 1; i++) {
            if (d <= AXIS_BASE[i]) {
                D = AXIS_BASE[i];
                break;
            }
        }
        minAxis = ((int) (min / D)) * D;
        if (min == max) {
            maxAxis = ((int) (max / D) + 1) * D;
        } else {
            maxAxis = ((int) (max / D) + (maxBigDecimal.remainder(new BigDecimal(D + "")).floatValue() == 0 ? 0 : 1)) * D;
        }
        labels[0] = minAxis;
        labels[1] = maxAxis;
        BigDecimal minAxisBigDecimal = new BigDecimal(minAxis + "");
        BigDecimal maxAxisBigDecimal = new BigDecimal(maxAxis + "");
        labels[2] = maxAxisBigDecimal.subtract(minAxisBigDecimal).divide(new BigDecimal(D + ""), 0, RoundingMode.HALF_UP).add(BigDecimal.valueOf(1)).intValue();
//        if (labels[2] < 4) labels[2] = 4;
        return labels;
    }

}