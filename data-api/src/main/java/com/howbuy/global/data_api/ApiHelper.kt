package com.howbuy.global.data_api

import com.alibaba.android.arouter.facade.template.IProvider
import com.howbuy.router.proxy.Invoker


/**
 * 获取用户信息
 */
fun apiUserInfo(): ApiUserInfo = get(ApiUserInfo::class.java)
fun apiUserMessage(): ApiUserMessage = get(ApiUserMessage::class.java)

fun <T : IProvider> get(cls: Class<T>): T {
    return Invoker.getInstance().navigation(cls)
}


fun getHkCustNo(): String? {
    return apiUserInfo().getHkCustNo()
}

fun isLogined(): Boolean {
    return apiUserInfo().isLogined()
}

