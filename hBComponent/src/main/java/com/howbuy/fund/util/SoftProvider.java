package com.howbuy.fund.util;

import android.app.Activity;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewTreeObserver;
import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;

public class SoftProvider implements LifecycleEventObserver {
    private static final String TAG = "SoftProvider";

    private final Activity mActivity;

    private final View mRootView;

    private final ViewTreeObserver.OnGlobalLayoutListener mOnGlobalLayoutListener;

    private int mSoftHeight = 0;

    private final SoftProviderListener mSoftProviderListener;
    //window可见高度
    private int mWindowHeight = 0;

    public SoftProvider(Activity activity, View rootView, SoftProviderListener mSoftProviderListener) {
        if (activity instanceof LifecycleOwner) {
            ((LifecycleOwner) activity).getLifecycle().addObserver(this);
        }
        mActivity = activity;
        mRootView = rootView;
        this.mSoftProviderListener = mSoftProviderListener;

        mOnGlobalLayoutListener = () -> {
            int localSoftHeight = 0;
            Rect root = new Rect();
            mRootView.getWindowVisibleDisplayFrame(root);
            if (mWindowHeight == 0) {
                mWindowHeight = root.height();
            } else {
                localSoftHeight = mWindowHeight - root.bottom;
            }
            if (mSoftHeight != localSoftHeight) {
                mSoftHeight = localSoftHeight;
                notifySoftState(mSoftHeight);
            }
        };
        mRootView.getViewTreeObserver().addOnGlobalLayoutListener(mOnGlobalLayoutListener);
    }


    private void notifySoftState(int softHeight) {
        if (mSoftProviderListener != null) {
            mSoftProviderListener.SoftStateChange(Math.max(softHeight, 0));
        }
    }

    /**
     * dialog中需要手动调用
     */
    public void onDestroy() {
        if (mActivity instanceof LifecycleOwner) {
            ((LifecycleOwner) mActivity).getLifecycle().removeObserver(this);
        }
        mRootView.getViewTreeObserver().removeOnGlobalLayoutListener(mOnGlobalLayoutListener);
    }

    public int getSoftHeight() {
        return mSoftHeight;
    }

    @Override
    public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
        if (event == Lifecycle.Event.ON_DESTROY) {
            onDestroy();
        }
    }

    public interface SoftProviderListener {
        void SoftStateChange(int height);
    }
}
