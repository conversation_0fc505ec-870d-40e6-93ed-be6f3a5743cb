package com.howbuy.fund.base.vm.viewadapter.recyclerview

import androidx.recyclerview.widget.RecyclerView
import com.howbuy.fund.base.vm.command.BindingFunction2
import com.howbuy.fund.base.widget.DividerGridItemDecoration
import com.howbuy.fund.base.widget.GridItemDecoration
import com.howbuy.fund.base.widget.SpacesItemDecoration
import com.howbuy.lib.utils.DensityUtils


/**
 * RecyclerView 分割线
 */
object LineManagers {
    @JvmStatic
    fun both(): BindingFunction2<RecyclerView, RecyclerView.ItemDecoration> {
        return BindingFunction2 {
            DividerLine(
                    it.context,
                    DividerLine.LINE_BOTH
            )
        }
    }

    @JvmStatic
    fun horizontal(): BindingFunction2<RecyclerView, RecyclerView.ItemDecoration> {
        return BindingFunction2 {
            DividerLine(
                    it.context,
                    DividerLine.LINE_HORIZONTAL
            )
        }
    }

    @JvmStatic
    fun vertical(): BindingFunction2<RecyclerView, RecyclerView.ItemDecoration> {
        return BindingFunction2 {
            DividerLine(
                    it.context,
                    DividerLine.LINE_VERTICAL
            )
        }
    }

    /**
     * 空白间隔
     * @spaceSize 间隔宽度
     * @isHorizontal 空白间隔是否是横的
     */
    @JvmStatic
    fun space(spaceSize: Float, isHorizontal: Boolean): BindingFunction2<RecyclerView, RecyclerView.ItemDecoration> {
        return BindingFunction2 {
            if (isHorizontal) {
                SpacesItemDecoration(DensityUtils.dp2px(spaceSize))
            } else {
                SpacesItemDecoration(DensityUtils.dp2px(spaceSize), 0)
            }
        }
    }

    @JvmStatic
    fun spaceGrid(spanCount: Int, spaceSize: Float): BindingFunction2<RecyclerView, RecyclerView.ItemDecoration> {
        return BindingFunction2 {
            GridItemDecoration(spanCount, DensityUtils.dp2px(spaceSize), DensityUtils.dp2px(spaceSize))
        }
    }

    /**
     * 表格间隔
     * @dividerWidth in px
     */
    @JvmStatic
    fun dividerGrid(dividerWidth: Int, dividerHeight: Int): BindingFunction2<RecyclerView, RecyclerView.ItemDecoration> {
        return BindingFunction2 {
            DividerGridItemDecoration(it.context, dividerWidth, dividerHeight)
        }
    }
}