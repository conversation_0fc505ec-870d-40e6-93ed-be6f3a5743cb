package com.howbuy.fund.base.vm.entity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * Create by zsm on 2020/11/19.
 **/
@SuppressLint("ParcelCreator")
@Parcelize
data class StartActivityEntity (
    val intent: Intent,

    val requestCode: Int = -1,

    val options: Bundle? = null
) : Parcelable