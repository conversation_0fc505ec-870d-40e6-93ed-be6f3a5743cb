package com.howbuy.component.widgets;

import android.annotation.TargetApi;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import androidx.annotation.ColorLong;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;


/**
 * 带圆角的三角形气泡Drawable（常用于说明性文案）。目前支持以下功能:
 * <ul>
 *     <li>设置三角型的高度（底边长为其2倍）. 属性：{@link #baseOfTriangle}</li>
 *     <li>设置三角型的水平偏移位置（相对于左边）. 属性：{@link #xOff}</li>
 *     <li>设置三角型的朝向（有上、下2种选项）. 属性：{@link #triangleDirection}</li>
 *     <li>设置圆角大小. 属性：{@link #radius}, {@link #radii}(4个圆角大小不一)</li>
 *     <li>设置边框宽度. 属性：{@link #widthStroke}</li>
 *     <li>设置边框颜色. 属性：{@link #}</li>
 *     <li>设置背景颜色. 属性：{@link #}</li>
 *     <li>设置绘制方式. 属性：{@link #}, 详情查看{@link Style}</li>
 * </ul>
 */
public class TriangleRoundRectDrawable extends Drawable {
    private float totalWidth;
    private float totalHeight;
    private Paint paint = new Paint();
    /**圆角矩形的path*/
    private Path roundRectPath = new Path();
    /**三角形path*/
    private Path trianglePath = new Path();
    /**path合并后的结果path*/
    private Path resultPath = new Path();

    /**竖直朝向时，其为三角形的高度, 宽是其2倍；水平朝向时，其为三角形的宽度，高度为其2倍*/
    private float baseOfTriangle = 30;
    private float xOff = 90;
    /**边的颜色*/
    @ColorLong
    private int colorStroke = 0xffeaeaea;
    /**背景色，当style为{@link Style#FILL} 或{@link Style#FILL_AND_STROKE}时有效*/
    @ColorLong
    private int colorBg = 0xffffffff;
    /**边框宽度*/
    private int widthStroke = 2;

    /**圆角radius*/
    private int radius = 30;
    /**4个圆角的rx,ry*/
    private float[] radii;
    /**三角形朝向*/
    private TriangleDirection triangleDirection = TriangleDirection.DOWN;
    private Style style = Style.FILL;

    /**
     * 三角形朝向
     */
    public enum TriangleDirection {
        /**向上*/
        UP,
        /**向下*/
        DOWN,
        LEFT,
        RIGHT
    }
    public enum Style {
        /**只绘制背景*/
        FILL,
        /**只绘制边框*/
        STROKE,
        /**绘制 背景+边框*/
        FILL_AND_STROKE
    }

    public TriangleRoundRectDrawable() {
    }

    public float getTriangleHeight() {
        return baseOfTriangle;
    }

    public void setTriangleHeight(float baseOfTriangle) {
        this.baseOfTriangle = baseOfTriangle;
        initPath();
    }

    public float getxOff() {
        return xOff;
    }

    /**
     * 设置三角的水平位置：
     * @param xOff >0 距离左侧偏移大小(px)，<0则为右侧
     */
    public void setxOff(float xOff) {
        this.xOff = xOff;
        initPath();
    }

    public int getColorStroke() {
        return colorStroke;
    }

    public void setColorStroke(int colorStroke) {
        this.colorStroke = colorStroke;
        initPath();
    }

    public int getRadius() {
        return radius;
    }

    public void setRadius(int radius) {
        this.radius = radius;
        initPath();
    }

    public float[] getRadii() {
        return radii;
    }

    public void setRadii(float[] radii) {
        this.radii = radii;
        initPath();
    }

    public int getWidthStroke() {
        return widthStroke;
    }

    public void setWidthStroke(int widthStroke) {
        this.widthStroke = widthStroke;
        initPath();
    }

    public TriangleDirection getTriangleDirection() {
        return triangleDirection;
    }

    public void setTriangleDirection(TriangleDirection triangleDirection) {
        this.triangleDirection = triangleDirection;
        initPath();
    }

    public Style getStyle() {
        return style;
    }

    public void setStyle(Style style) {
        this.style = style;
        initPath();
    }

    public int getColorBg() {
        return colorBg;
    }

    public void setColorBg(int colorBg) {
        this.colorBg = colorBg;
    }

    @TargetApi(5)
    private void initPath() {
        int widthStroke = this.widthStroke;
        if (style == Style.FILL) widthStroke = 0;

        float[] radii;
        if (null != this.radii && this.radii.length == 8) {
            radii = this.radii;
        } else {
            radii = new float[8];
            //left top
            radii[0] = radius;
            radii[1] = radius;
            //right top
            radii[2] = radius;
            radii[3] = radius;
            //right bottom
            radii[4] = radius;
            radii[5] = radius;
            //left bottom
            radii[6] = radius;
            radii[7] = radius;
        }

        final float roundRectTop = calculateRoundRectTop();
        final float roundRectBottom = calculateRoundRectBottom();
        final float roundRectLeft = calculateRoundRectLeft();
        final float roundRectRight = calculateRoundRectRight();
        roundRectPath.reset();

        trianglePath.reset();
        if (triangleDirection == TriangleDirection.UP || TriangleDirection.DOWN == triangleDirection) {
            //三角形顶点的朝向为竖直方向
            roundRectPath.addRoundRect(new RectF( roundRectLeft, roundRectTop, roundRectRight, roundRectBottom), radii, Path.Direction.CW);
            if (Math.abs(xOff) < (roundRectRight - (2 * baseOfTriangle))) {
                trianglePath.reset();
                float y = triangleDirection == TriangleDirection.UP? roundRectTop : roundRectBottom;
                float xTl = xOff >= 0? xOff : roundRectRight + xOff - (2 * baseOfTriangle);
                trianglePath.moveTo(xTl, y);
                trianglePath.lineTo(xTl + baseOfTriangle, triangleDirection == TriangleDirection.UP? 0 : ( roundRectBottom + baseOfTriangle - widthStroke / 2f));
                trianglePath.lineTo(xTl + 2 * baseOfTriangle, y);
                trianglePath.close();
            }
        } else {
            float left = TriangleDirection.LEFT == triangleDirection? roundRectLeft + baseOfTriangle : roundRectLeft;
            float right = TriangleDirection.LEFT == triangleDirection? roundRectRight : roundRectRight - baseOfTriangle;
            roundRectPath.addRoundRect(new RectF(left, roundRectTop, right, roundRectBottom), radii, Path.Direction.CW);
            //三角形的顶点朝向为水平朝向的时候
            //left: 左边一个点, 右边
            float y = (roundRectBottom + roundRectLeft) / 2; //垂直居中
            if (TriangleDirection.LEFT == triangleDirection) {
                trianglePath.moveTo(roundRectLeft, y); //这里借用triangleHeight表示
                trianglePath.lineTo(roundRectLeft + baseOfTriangle + widthStroke / 2f, y - baseOfTriangle);
                trianglePath.lineTo(roundRectLeft + baseOfTriangle + widthStroke / 2f, y + baseOfTriangle);
            } else {
                trianglePath.moveTo(roundRectRight - widthStroke / 2f, y); //这里借用triangleHeight表示
                trianglePath.lineTo(roundRectRight - baseOfTriangle, y - baseOfTriangle);
                trianglePath.lineTo(roundRectRight - baseOfTriangle, y + baseOfTriangle);
            }
            trianglePath.close();
        }

        resultPath.reset();
        resultPath.op(roundRectPath, trianglePath, Path.Op.UNION);
    }

    /**
     * 圆角矩形的Top位置
     */
    private float calculateRoundRectTop() {
        int widthStroke = this.widthStroke;
        if (style == Style.FILL) widthStroke = 0;
        if (triangleDirection == TriangleDirection.UP) return baseOfTriangle + widthStroke / 2f;
        return widthStroke / 2f;
    }

    private float calculateRoundRectBottom() {
        int widthStroke = this.widthStroke;
        if (style == Style.FILL) widthStroke = 0;
        if (triangleDirection == TriangleDirection.DOWN) return totalHeight - widthStroke/2f - baseOfTriangle;
        return totalHeight - widthStroke;
    }

    private float calculateRoundRectLeft() {
        int widthStroke = this.widthStroke;
        if (style == Style.FILL) widthStroke = 0;
        //ignore left, right
        return widthStroke / 2f;
    }

    private float calculateRoundRectRight() {
        int widthStroke = this.widthStroke;
        if (style == Style.FILL) widthStroke = 0;
        return totalWidth - widthStroke/2f;
    }

    @Override
    public void setBounds(int left, int top, int right, int bottom) {
        super.setBounds(left, top, right, bottom);
        this.totalWidth = right;
        this.totalHeight = bottom;
        initPath();
    }

    @Override
    protected void onBoundsChange(Rect bounds) {
        super.onBoundsChange(bounds);
        this.totalWidth = bounds.width();
        this.totalHeight = bounds.height();

        initPath();
    }

    @Override
    public void draw(@NonNull Canvas canvas) {
        canvas.save();

        drawBackground(canvas);
        drawStroke(canvas);

        canvas.restore();
    }

    private void drawBackground(@NonNull Canvas canvas) {
        if (style == Style.STROKE) return;
        if (colorBg == 0) return;

        paint.setStyle(Paint.Style.FILL);
        paint.setColor(colorBg);
        canvas.drawPath(resultPath, paint);
    }

    private void drawStroke(@NonNull Canvas canvas) {
        if(style == Style.FILL) return;
        this.paint.setAntiAlias(true);
        this.paint.setColor(colorStroke);
        this.paint.setStyle(Paint.Style.STROKE);
        if (style == Style.STROKE) {
            this.paint.setStrokeWidth(widthStroke);
            this.paint.setStrokeJoin(Paint.Join.ROUND);
            this.paint.setStrokeCap(Paint.Cap.ROUND);
        }
        canvas.drawPath(resultPath, paint);
    }

    @Override
    public void setAlpha(int alpha) {
        paint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        paint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.OPAQUE;
    }
}