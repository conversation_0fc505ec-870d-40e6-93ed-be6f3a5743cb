package com.howbuy.component.widgets;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;

import com.howbuy.component.R;


/**
 * 仿iphone带进度的进度条，线程安全的View，可直接在线程中更新进度
 * <AUTHOR>
 * @data 2018/12/19
 *
 */
public class RoundProgressBar extends View {
	/**
	 * 圈 ，画笔对象的引用
	 */
	private Paint paint;

	/**
	 * 文字画笔的颜色
	 */
	private Paint mPaint;

	/**
	 * 文字画笔的颜色
	 */
	private Paint mPaintProgress;

	/**
	 * 圆环的颜色
	 */
	private int roundColor;
	
	/**
	 * 圆环进度的颜色
	 */
	private int roundProgressColor;
	
	/**
	 * 中间进度百分比的字符串的颜色
	 */
	private int textColor;
	
	/**
	 * 中间进度百分比的字符串的字体
	 */
	private float textSize;
	/**
	 * 已下载文字大小
	 */
	private float textDesSize;
	/**
	 * 已下载文字内容
	 */
	private String textDesText;

	/**
	 * 已下载文字内容字符串的颜色
	 */
	private int descTextColor;
	/**
	 * 圆环的宽度
	 */
	private float roundWidth;
	/**
	 * 进度条的宽度
	 */
	private float roundProgressWidth;

	/**
	 * 最大进度
	 */
	private int max;
	
	/**
	 * 当前进度
	 */
	private int progress;
	/**
	 * 是否显示中间的进度
	 */
	private boolean textIsDisplayable;
	
	/**
	 * 进度的风格，实心或者空心
	 */
	private int style;
	
	public static final int STROKE = 0;
	public static final int FILL = 1;
	
	public RoundProgressBar(Context context) {
		this(context, null);
	}

	public RoundProgressBar(Context context, AttributeSet attrs) {
		this(context, attrs, 0);
	}
	
	public RoundProgressBar(Context context, AttributeSet attrs, int defStyle) {
		super(context, attrs, defStyle);
		TypedArray mTypedArray = context.obtainStyledAttributes(attrs,
				R.styleable.RoundProgressBar);
		
		//获取自定义属性和默认值
		roundColor = mTypedArray.getColor(R.styleable.RoundProgressBar_roundColor, Color.RED);
		roundProgressColor = mTypedArray.getColor(R.styleable.RoundProgressBar_roundProgressColor, Color.GREEN);
		textColor = mTypedArray.getColor(R.styleable.RoundProgressBar_textColor, Color.GREEN);
		textSize = mTypedArray.getDimension(R.styleable.RoundProgressBar_textSize, 15);

		descTextColor = mTypedArray.getColor(R.styleable.RoundProgressBar_textDesColor, Color.WHITE);
		textDesSize = mTypedArray.getDimension(R.styleable.RoundProgressBar_textDesSize, 12);
		textDesText = mTypedArray.getString(R.styleable.RoundProgressBar_textDesText);
		if (TextUtils.isEmpty(textDesText)){
			textDesText = "已下载";
		}
		roundWidth = mTypedArray.getDimension(R.styleable.RoundProgressBar_roundWidth, 10);
		roundProgressWidth = mTypedArray.getDimension(R.styleable.RoundProgressBar_roundProgressWidth, 5);
		max = mTypedArray.getInteger(R.styleable.RoundProgressBar_max, 100);
		textIsDisplayable = mTypedArray.getBoolean(R.styleable.RoundProgressBar_textIsDisplayable, true);
		style = mTypedArray.getInt(R.styleable.RoundProgressBar_style, 0);
		
		mTypedArray.recycle();
        init();
	}


	private void init(){
		paint = new Paint();
		mPaint = new Paint();
		mPaintProgress = new Paint();
		mPaint.setColor(Color.parseColor("#bbbdcb"));
		mPaint.setStyle(Paint.Style.FILL); //设置空心
		mPaintProgress.setStyle(Paint.Style.FILL);
		mPaintProgress.setAntiAlias(true);
		mPaint.setStrokeWidth(1); //设置圆环的宽度
		mPaint.setTextSize(textDesSize);
		mPaint.setAntiAlias(true);  //消除锯齿


	}

	@Override
	protected void onDraw(Canvas canvas) {
		super.onDraw(canvas);
		paint.setColor(roundColor); //设置圆环的颜色
		paint.setStyle(Paint.Style.STROKE); //设置空心
		paint.setStrokeWidth(roundWidth); //设置圆环的宽度
		paint.setAntiAlias(true);  //消除锯齿

		/**
		 * 画最外层的大圆环
		 */
		int centre = getWidth()/2; //获取圆心的x坐标
		int radius = (int) (centre - roundWidth/2); //圆环的半径
		canvas.drawCircle(centre, centre, radius, paint); //画出圆环

		/**
		 * 画进度百分比
		 */
		mPaintProgress.setStrokeWidth(0);
		mPaintProgress.setColor(textColor);
		mPaintProgress.setTextSize(textSize);
		int percent = (int)(((float)progress / (float)max) * 100);  //中间的进度百分比，先转换成float在进行除法运算，不然都为0

		String proText = percent + "%" ;
		Rect bounds = new Rect();
		mPaintProgress.getTextBounds(proText, 0, proText.length(), bounds);

		//String detailTips = "已下载" ;
		Rect bounds1 = new Rect();
		mPaint.getTextBounds(textDesText, 0, textDesText.length(), bounds1);
		mPaint.setColor(descTextColor);
		if(textIsDisplayable){ // && percent != 0
			canvas.drawText(proText, centre - bounds.width() / 2, centre + bounds.height()/2 -bounds1.height()/2, mPaintProgress); //画出进度百分比
		}

		/**
		 * "已下载"文字
		 */
		canvas.drawText(textDesText, centre - bounds1.width() / 2, centre + bounds.height()/2 + bounds1.height()/2+5, mPaint);
		/**
		 * 画圆弧 ，画圆环的进度
		 */
		
		//设置进度是实心还是空心
		paint.setStrokeWidth(roundProgressWidth); //设置圆环的宽度
		paint.setColor(roundProgressColor);  //设置进度的颜色
		paint.setStrokeCap(Paint.Cap.ROUND);
		RectF oval = new RectF(centre - radius, centre - radius, centre
				+ radius, centre + radius);  //用于定义的圆弧的形状和大小的界限
		
		switch (style) {
		case STROKE:{
			paint.setStyle(Paint.Style.STROKE);
			canvas.drawArc(oval, -90, 360 * progress / max, false, paint);  //根据进度画圆弧
			break;
		}
		case FILL:{
			paint.setStyle(Paint.Style.FILL_AND_STROKE);
			if(progress !=0)
				canvas.drawArc(oval, -90, 360 * progress / max, true, paint);  //根据进度画圆弧
			break;
		}
		}
		
	}
	
	
	public synchronized int getMax() {
		return max;
	}

	/**
	 * 设置进度的最大值
	 * @param max
	 */
	public synchronized void setMax(int max) {
		if(max < 0){
			throw new IllegalArgumentException("max not less than 0");
		}
		this.max = max;
	}

	/**
	 * 获取进度.需要同步
	 * @return
	 */
	public synchronized int getProgress() {
		return progress;
	}

	/**
	 * 设置进度，此为线程安全控件，由于考虑多线的问题，需要同步
	 * 刷新界面调用postInvalidate()能在非UI线程刷新
	 * @param progress
	 */
	public synchronized void setProgress(int progress) {
		if(progress < 0){
			throw new IllegalArgumentException("progress not less than 0");
		}
		if(progress > max){
			progress = max;
		}
		if(progress <= max){
			this.progress = progress;
			postInvalidate();
		}
		
	}
	
	
	public int getCricleColor() {
		return roundColor;
	}

	public void setCricleColor(int cricleColor) {
		this.roundColor = cricleColor;
	}

	public int getCricleProgressColor() {
		return roundProgressColor;
	}

	public void setCricleProgressColor(int cricleProgressColor) {
		this.roundProgressColor = cricleProgressColor;
	}

	public int getTextColor() {
		return textColor;
	}

	public void setTextColor(int textColor) {
		this.textColor = textColor;
	}

	public float getTextSize() {
		return textSize;
	}

	public void setTextSize(float textSize) {
		this.textSize = textSize;
	}

	public float getRoundWidth() {
		return roundWidth;
	}

	public void setRoundWidth(float roundWidth) {
		this.roundWidth = roundWidth;
	}



}
