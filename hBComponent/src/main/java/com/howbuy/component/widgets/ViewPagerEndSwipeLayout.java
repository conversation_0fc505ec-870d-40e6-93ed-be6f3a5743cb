package com.howbuy.component.widgets;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.FrameLayout;

import androidx.core.view.MotionEventCompat;
import androidx.core.view.ViewCompat;
import androidx.customview.widget.ViewDragHelper;

/**
 * 滑动到最后还能继续滑动
 */
public class ViewPagerEndSwipeLayout extends FrameLayout {
    public static final String TAG="HorizontalDrawerLayout";
    private ViewDragHelper mTopViewDragHelper;
    private View mBackgroundView;
    private View mSwipeView;
    private boolean mCanSwipeFlag;
    private InitGuideSwipeListener mListener;
    private boolean isDrag = false;
    private float mInitXpos;
    private float mCurMovePercent;

    public void setOnLayoutDragingListener(InitGuideSwipeListener l) {
        mListener = l;
    }

    public void setCanSwipeFlag(boolean mCanSwipeFlag) {
        this.mCanSwipeFlag = mCanSwipeFlag;
    }

    public ViewPagerEndSwipeLayout(Context context) {
        super(context);
        init();
    }

    public ViewPagerEndSwipeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ViewPagerEndSwipeLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        mTopViewDragHelper = ViewDragHelper.create(this, 1.0f, new ViewDragHelperCallBack());
    }

    private class ViewDragHelperCallBack extends ViewDragHelper.Callback {

        @Override
        public boolean tryCaptureView(View child, int pointerId) {
            return child == mSwipeView;
        }

        @Override
        public int clampViewPositionHorizontal(View child, int left, int dx) {
            //手指触摸移动时实时回调, left表示要到的x位置
            return left;
        }

        @Override
        public int clampViewPositionVertical(View child, int top, int dy) {
            return 0;
        }


        @Override
        public void onViewPositionChanged(View changedView, int left, int top, int dx, int dy) {
            if (changedView == mSwipeView) {
                int drawerWith= mSwipeView.getMeasuredWidth();
                float precet=(drawerWith- Math.abs(left))/ (float)drawerWith;
                mCurMovePercent =1- precet;
                ViewCompat.setAlpha(mSwipeView,precet);
            }
        }

        //手指释放时回调
        @Override
        public void onViewReleased(View releasedChild, float xvel, float yvel) {
            if (mCurMovePercent <0.2){
                //x轴=0表示回到原点
                mTopViewDragHelper.settleCapturedViewAt(0, 0);

            }else {
                mTopViewDragHelper.settleCapturedViewAt(-mSwipeView.getWidth(), 0);
                postResult();
            }
            invalidate();
        }
        @Override
        public int getViewHorizontalDragRange(View child) {
            return (int) (child.getWidth()+child.getX());
        }
    }

    private void postResult() {
        postDelayed(new Runnable() {
            @Override
            public void run() {
                if (mListener != null) {
                    mListener.onFinishSwipe();
                }
            }
        },20);
    }

    @Override
    public void computeScroll() {
        if (mTopViewDragHelper.continueSettling(true)) {
            invalidate();
        }
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        boolean touchResult = false;
        boolean helperIntercept = mTopViewDragHelper.shouldInterceptTouchEvent(ev);

        if (mCanSwipeFlag){
            switch (MotionEventCompat.getActionMasked(ev)) {
                case MotionEvent.ACTION_DOWN:
                    mInitXpos = ev.getX();
                    break;
                case MotionEvent.ACTION_MOVE:
                    float x=ev.getX();
                    float min= ViewConfiguration.getTouchSlop();
                    float xDis=x-mInitXpos;
                    if (xDis<0&& Math.abs(xDis)>min){
                        touchResult=true;
                        mTopViewDragHelper.captureChildView(mSwipeView,ev.getPointerId(0));
                    }
                    break;
            }
        }else {
            touchResult=false;
        }

        return helperIntercept&&touchResult;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        try {
            mTopViewDragHelper.processTouchEvent(event);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return true;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mBackgroundView = getChildAt(0);
        mSwipeView = getChildAt(1);
    }

    public static interface InitGuideSwipeListener {
        /**
         * 暂时不实现
         */
        void onStratSwipe();
        void onFinishSwipe();
    }
}
