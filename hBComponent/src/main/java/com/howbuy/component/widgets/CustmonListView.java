package com.howbuy.component.widgets;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ListView;

/**
 * Created by jinxing.liu on 2018/4/13.
 */

public class CustmonListView extends ListView {
    /**
     * 是否充满高度
     */
    private boolean mMatchHeight;

    public CustmonListView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (mMatchHeight){
            int spec = View.MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE>>2, MeasureSpec.AT_MOST);
            super.onMeasure(widthMeasureSpec, spec);
        }else{
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }
    }

    public void matchParentHeight(boolean matchHeight){
        mMatchHeight = matchHeight;
    }
}
