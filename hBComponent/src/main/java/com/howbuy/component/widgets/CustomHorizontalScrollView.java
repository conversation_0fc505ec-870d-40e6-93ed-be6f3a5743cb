package com.howbuy.component.widgets;

import android.annotation.TargetApi;
import android.content.Context;
import android.util.AttributeSet;
import android.widget.HorizontalScrollView;

public class CustomHorizontalScrollView extends HorizontalScrollView {
    private ScrollListener listener;
    public interface ScrollListener {
        void onCustomScrollChange(CustomHorizontalScrollView view, int scrollX, int scrollY, int oldScrollX, int oldScrollY);
    }

    public CustomHorizontalScrollView(Context context) {
        super(context);
    }

    public CustomHorizontalScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomHorizontalScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @TargetApi(21)
    public CustomHorizontalScrollView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    protected void onScrollChanged(int l, int t, int oldl, int oldt) {
        super.onScrollChanged(l, t, oldl, oldt);
        if (null != listener) {
            listener.onCustomScrollChange(CustomHorizontalScrollView.this, l, t, oldl, oldt);
        }
    }

    public ScrollListener getListener() {
        return listener;
    }

    public void setListener(ScrollListener listener) {
        this.listener = listener;
    }
}
