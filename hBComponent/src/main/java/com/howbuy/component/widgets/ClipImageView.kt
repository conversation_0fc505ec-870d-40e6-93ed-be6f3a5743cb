package com.howbuy.component.widgets

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PaintFlagsDrawFilter
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.widget.ImageView
import com.howbuy.component.R


@SuppressLint("AppCompatCustomView")
class ClipImageView : ImageView {
    var path: Path = Path()
    var paintFlagsFilter: PaintFlagsDrawFilter = PaintFlagsDrawFilter(
        0,
        Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG
    )
    var rectF: RectF = RectF()

    var leftTopRadius: Int = 0
    var rightTopRadius: Int = 0
    var rightBottomRadius: Int = 0
    var leftBottomRadius: Int = 0

    //背景色
    var bgColor = 0

    //边框宽度
    var borderWidth: Int = 0

    //边框颜色
    var borderColor: Int = 0

    val paint = Paint()

    constructor(context: Context?) : super(context) {
        initAttr(context, null)
    }

    constructor(
        context: Context?,
        attrs: AttributeSet?
    ) : super(context, attrs) {
        initAttr(context, attrs)
    }

    constructor(
        context: Context?,
        attrs: AttributeSet?,
        defStyleAttr: Int
    ) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initAttr(context, attrs)
    }

    constructor(
        context: Context?,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes) {
        initAttr(context, attrs)
    }

    @SuppressLint("Recycle")
    fun initAttr(
        context: Context?,
        attrs: AttributeSet?
    ) {
        val ta: TypedArray = context?.obtainStyledAttributes(attrs, R.styleable.ClipImageView) ?: return
        val radius = ta.getDimensionPixelSize(R.styleable.ClipImageView_radius, 0)
        if (radius > 0) {
            this.leftTopRadius = radius
            this.rightTopRadius = radius
            this.rightBottomRadius = radius
            this.leftBottomRadius = radius
        } else {
            this.leftTopRadius = ta.getDimensionPixelSize(R.styleable.ClipImageView_leftTopRadius, 0)
            this.rightTopRadius = ta.getDimensionPixelSize(R.styleable.ClipImageView_rightTopRadius, 0)
            this.rightBottomRadius = ta.getDimensionPixelSize(R.styleable.ClipImageView_rightBottomRadius, 0)
            this.leftBottomRadius = ta.getDimensionPixelSize(R.styleable.ClipImageView_leftBottomRadius, 0)
        }
        this.bgColor = ta.getColor(R.styleable.ClipImageView_bgColor, 0)
        this.borderWidth = ta.getDimensionPixelSize(R.styleable.ClipImageView_widthBorder, 0)
        this.borderColor = ta.getColor(R.styleable.ClipImageView_colorBorder, 0)

        ta.recycle()
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        this.rectF = RectF(0f, 0f, w.toFloat(), h.toFloat())
    }

    override fun onDraw(canvas: Canvas) {
        this.path.reset()
        this.path.addRoundRect(
            rectF,
            floatArrayOf(
                leftTopRadius.toFloat(),
                leftTopRadius.toFloat(),

                rightTopRadius.toFloat(),
                rightTopRadius.toFloat(),

                rightBottomRadius.toFloat(),
                rightBottomRadius.toFloat(),

                leftBottomRadius.toFloat(),
                leftBottomRadius.toFloat()
            ),
            Path.Direction.CW
        )

        canvas.drawFilter = paintFlagsFilter
        canvas.save()
        canvas.clipPath(this.path)
        //draw bg color
        if (0 != bgColor) {
            canvas.drawColor(bgColor)
        }
        super.onDraw(canvas)
        if (0 != borderColor && 0 < borderWidth) {
            //draw border
            paint.reset()
            paint.isAntiAlias = true
            paint.color = borderColor
            paint.style = Paint.Style.STROKE
            paint.strokeWidth = 2 * borderWidth.toFloat()
            canvas.drawPath(path, paint)
        }

        canvas.restore()
    }

    fun changeBgColor(bgColor: Int) {
        this.bgColor = bgColor
        invalidate()
    }

    fun changeBorderWidth(borderWidth: Int) {
        this.borderWidth = borderWidth
        invalidate()
    }

    fun changeBorderColor(borderColor: Int) {
        this.borderColor = borderColor
        invalidate()
    }
}