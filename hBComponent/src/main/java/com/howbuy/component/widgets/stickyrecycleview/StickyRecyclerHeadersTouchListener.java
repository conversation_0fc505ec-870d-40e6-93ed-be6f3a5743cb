package com.howbuy.component.widgets.stickyrecycleview;

import android.graphics.Rect;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.SoundEffectConstants;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

public class StickyRecyclerHeadersTouchListener implements RecyclerView.OnItemTouchListener {
  private static final String TAG = "StickyRecyclerHeadersTo";
  private final GestureDetector mTapDetector;
  private final RecyclerView mRecyclerView;
  private final StickyRecyclerHeadersDecoration mDecor;
  private OnHeaderClickListener mOnHeaderClickListener;

  public interface OnHeaderClickListener {
    void onHeaderClick(View header, int position, long headerId, MotionEvent event);
  }

  public StickyRecyclerHeadersTouchListener(final RecyclerView recyclerView,
                                            final StickyRecyclerHeadersDecoration decor) {
    mTapDetector = new GestureDetector(recyclerView.getContext(), new SingleTapDetector());
    mRecyclerView = recyclerView;
    mDecor = decor;
  }

  public StickyRecyclerHeadersAdapter getAdapter() {
    if (mRecyclerView.getAdapter() instanceof StickyRecyclerHeadersAdapter) {
      return (StickyRecyclerHeadersAdapter) mRecyclerView.getAdapter();
    } else {
      throw new IllegalStateException("A RecyclerView with " +
          StickyRecyclerHeadersTouchListener.class.getSimpleName() +
          " requires a " + StickyRecyclerHeadersAdapter.class.getSimpleName());
    }
  }


  public void setOnHeaderClickListener(OnHeaderClickListener listener) {
    mOnHeaderClickListener = listener;
  }

  @Override
  public boolean onInterceptTouchEvent(RecyclerView view, MotionEvent e) {
    if (this.mOnHeaderClickListener != null) {
      boolean tapDetectorResponse = this.mTapDetector.onTouchEvent(e);
      if (tapDetectorResponse) {
        // Don't return false if a single tap is detected
        return true;
      }
      if (e.getAction() == MotionEvent.ACTION_DOWN) {
        int position = mDecor.findHeaderPositionUnder((int)e.getX(), (int)e.getY());
        Log.i(TAG, "onInterceptTouchEvent: position = " + position);
        if (position != -1){
          View headerView = mDecor.getHeaderView(mRecyclerView, position);
          long headerId = getAdapter().getHeaderId(position);
          mOnHeaderClickListener.onHeaderClick(headerView, position, headerId, e);
          mRecyclerView.playSoundEffect(SoundEffectConstants.CLICK);
          //performClick(headerView, e, position);
          return true;
        }
        return false;
      }
    }
    return false;
  }

  public void performClick(View view, MotionEvent e,int position) {

    if (view instanceof ViewGroup) {
      ViewGroup viewGroup = (ViewGroup) view;
      for (int i = 0; i < viewGroup.getChildCount(); i++) {
        View child = viewGroup.getChildAt(i);
        performClick(child, e ,position);
      }
    }
    containsBounds(view, e ,position);

  }


  private View containsBounds(View view, MotionEvent e ,int parentTop) {
    int x = (int) e.getX();
    int y = (int) e.getY();
    Rect rect = new Rect();
    view.getHitRect(rect);


    Log.i(TAG, "containsBounds: view = " + view + " 显示 = " + (view.getVisibility() == View.VISIBLE) + " 包含点击 = " + (rect.contains(x, y)) + " rect = " + rect + " x = " + x + " y = " + y);
    if (view.getVisibility() == View.VISIBLE
            && rect.left < rect.right && rect.top < rect.bottom && x >= rect.left && x < rect.right && y >= rect.top && y <= rect.bottom) {
      //view.setTag(position);

      if (view.hasOnClickListeners()) {
        if (!view.dispatchTouchEvent(e)){
          Log.i(TAG, "containsBounds: 不分发view = " + view);
        }
        Log.i(TAG, "containsBounds: view Id = " + view);
        view.performClick();
      }

      return view;
    }
    return null;
  }

  @Override
  public void onTouchEvent(RecyclerView view, MotionEvent e) { /* do nothing? */ }

  @Override
  public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
    // do nothing
  }

  private class SingleTapDetector extends GestureDetector.SimpleOnGestureListener {
    @Override
    public boolean onSingleTapUp(MotionEvent e) {
      int position = mDecor.findHeaderPositionUnder((int) e.getX(), (int) e.getY());
      if (position != -1) {
        View headerView = mDecor.getHeaderView(mRecyclerView, position);
        long headerId = getAdapter().getHeaderId(position);
        mOnHeaderClickListener.onHeaderClick(headerView, position, headerId, e);
        mRecyclerView.playSoundEffect(SoundEffectConstants.CLICK);
        headerView.onTouchEvent(e);
        return true;
      }
      return false;
    }

    @Override
    public boolean onDoubleTap(MotionEvent e) {
      return true;
    }
  }
}
