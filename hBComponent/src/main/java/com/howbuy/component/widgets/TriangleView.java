package com.howbuy.component.widgets;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;

import com.howbuy.component.R;

import java.util.concurrent.atomic.AtomicReference;

// 自定义三角形 支持上下左右
public class TriangleView extends View {

    // 0正三角 1倒三角 2左三角 3右三角
    private int orientation = 0;
    //是否绘制stoke
    private boolean drawStroke = false;
    private boolean strokeClose = false;
    float strokeWidth = 0;

    public enum Orientation {
        UP(0), DOWN(1), LEFT(2), RIGHT(3), RANG_LEFT(4), RANG_RIGHT(5);

        public int id;

        Orientation(int id) {
            this.id = id;
        }
    }

    Paint p = new Paint();
    Paint paintStroke = new Paint();

    public TriangleView setOrientation(int orientation) {
        this.orientation = orientation;
        return this;
    }

    public TriangleView setPaintColor(int paintColor) {
        p.setColor(paintColor);
        return this;
    }

    public TriangleView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(attrs);
    }

    public TriangleView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        @SuppressLint({"CustomViewStyleable", "Recycle"})
        TypedArray typedArray = getContext().obtainStyledAttributes(attrs, R.styleable.TriangleView);
        orientation = typedArray.getInt(R.styleable.TriangleView_triangleOrientation, 0);
        strokeWidth = typedArray.getFloat(R.styleable.TriangleView_triangleStrokeWidth, 1);
        int paintColor = typedArray.getColor(R.styleable.TriangleView_triangleColor, Color.BLACK);
        boolean isFill = typedArray.getBoolean(R.styleable.TriangleView_triangleFill, true);
        int paintStrokeColor = typedArray.getColor(R.styleable.TriangleView_triangleStrokeColor, Color.RED);
        strokeClose = typedArray.getBoolean(R.styleable.TriangleView_triangleStrokeClose, false);
        drawStroke = typedArray.getBoolean(R.styleable.TriangleView_triangleDrawStorke, false);
        p.setColor(paintColor);
        p.setAntiAlias(true);
        p.setStyle(isFill ? Paint.Style.FILL : Paint.Style.STROKE);
        paintStroke.setColor(paintStrokeColor);
        paintStroke.setAntiAlias(true);
        paintStroke.setStyle(Paint.Style.STROKE);
        paintStroke.setStrokeWidth(strokeWidth);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        @SuppressLint("DrawAllocation") AtomicReference<Path> path = new AtomicReference<>(new Path());
        @SuppressLint("DrawAllocation") AtomicReference<Path> pathStroke = new AtomicReference<>(new Path());
        int w = this.getWidth();
        int h = this.getHeight();

        if (orientation == Orientation.UP.id) {
            path.get().moveTo(w / 2f, 0);
            path.get().lineTo(0, h);
            path.get().lineTo(w, h);

            pathStroke.get().moveTo(0, h);
            pathStroke.get().lineTo(w / 2f, 0);
            pathStroke.get().lineTo(w, h);
        } else if (orientation == Orientation.DOWN.id) {
            path.get().moveTo(0, 0);
            path.get().lineTo(w / 2f, h);
            path.get().lineTo(w, 0);

            pathStroke.get().moveTo(w / 2f, h);
            pathStroke.get().lineTo(0, 0);
            pathStroke.get().lineTo(w, 0);
        } else if (orientation == Orientation.LEFT.id) {
            path.get().moveTo(0, h / 2f);
            path.get().lineTo(w, 0);
            path.get().lineTo(w, h);
        } else if (orientation == Orientation.RIGHT.id) {
            path.get().moveTo(w, h / 2f);
            path.get().lineTo(0, 0);
            path.get().lineTo(0, h);
        } else if (orientation == Orientation.RANG_LEFT.id) {
            path.get().moveTo(0, 0);
            path.get().lineTo(0, h);
            path.get().lineTo(w, h);
        } else if (orientation == Orientation.RANG_RIGHT.id) {
            path.get().moveTo(w, 0);
            path.get().lineTo(w, h);
            path.get().lineTo(0, h);
        } else {
            path.get().moveTo(w, h / 2f);
            path.get().lineTo(0, 0);
            path.get().lineTo(0, h);
        }
        path.get().close();
        canvas.drawPath(path.get(), p);

        if (drawStroke) {
            if (strokeClose) {
                pathStroke.get().close();
            }
            canvas.drawPath(pathStroke.get(), paintStroke);
        }
    }
}