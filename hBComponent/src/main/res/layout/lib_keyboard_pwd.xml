<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="#ffffff"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="#FFE4E5E8"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/iv_top"
            android:layout_marginTop="6dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:src="@drawable/gm_safe_keybord_dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="好买安全键盘"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/iv_top"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_centerHorizontal="true"
            android:textColor="#bbbdcb"
            android:layout_below="@id/iv_top"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_hide_keyboard"
            android:paddingBottom="2dp"
            android:paddingRight="15dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/gm_safe_keybord_sq"
            android:layout_width="36dp"
            android:layout_height="18dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TableLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFE4E5E8">

        <TableRow
            android:layout_marginTop="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_1"
                style="@style/keyboard_item_pwd"
                android:layout_marginLeft="6dp"
                android:tag="1"
                android:text="1" />

            <TextView
                android:id="@+id/tv_2"
                style="@style/keyboard_item_pwd"
                android:tag="2"
                android:layout_marginLeft="6dp"
                android:text="2" />

            <TextView
                android:id="@+id/tv_3"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                style="@style/keyboard_item_pwd"
                android:tag="3"
                android:text="3" />

        </TableRow>

        <TableRow
            android:layout_marginTop="6dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_4"
                style="@style/keyboard_item_pwd"
                android:layout_marginLeft="6dp"
                android:tag="4"
                android:text="4" />

            <TextView
                android:id="@+id/tv_5"
                style="@style/keyboard_item_pwd"
                android:layout_marginLeft="6dp"
                android:tag="5"
                android:text="5" />

            <TextView
                android:id="@+id/tv_6"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                style="@style/keyboard_item_pwd"
                android:tag="6"
                android:text="6" />

        </TableRow>

        <TableRow
            android:layout_marginTop="6dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_7"
                android:layout_marginLeft="6dp"
                style="@style/keyboard_item_pwd"
                android:tag="7"
                android:text="7" />

            <TextView
                android:id="@+id/tv_8"
                android:layout_marginLeft="6dp"
                style="@style/keyboard_item_pwd"
                android:tag="8"
                android:text="8" />

            <TextView
                android:id="@+id/tv_9"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                style="@style/keyboard_item_pwd"
                android:tag="9"
                android:text="9" />

        </TableRow>

        <TableRow
            android:layout_marginTop="6dp"
            android:layout_marginBottom="10dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_dot"
                style="@style/keyboard_item_pwd"
                android:layout_marginLeft="6dp"
                android:tag="."
                android:text="." />

            <TextView
                android:id="@+id/tv_0"
                android:layout_marginLeft="6dp"
                style="@style/keyboard_item_pwd"
                android:tag="0"
                android:text="0" />

            <LinearLayout
                android:id="@+id/tv_keyboard_del"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:layout_weight="1"
                android:clickable="true"
                android:tag="-2"
                android:background="@drawable/pay_keyboard_item_pwd_selector"
                android:gravity="center">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:src="@drawable/gm_safe_keybord_delete" />
            </LinearLayout>

        </TableRow>

    </TableLayout>
</LinearLayout>